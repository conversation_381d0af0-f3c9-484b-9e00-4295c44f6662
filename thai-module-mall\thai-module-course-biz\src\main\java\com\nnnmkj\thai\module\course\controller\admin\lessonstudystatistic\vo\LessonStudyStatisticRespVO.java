package com.nnnmkj.thai.module.course.controller.admin.lessonstudystatistic.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Schema(description = "管理后台 - 课程学习统计 Response VO")
@Data
@ExcelIgnoreUnannotated
public class LessonStudyStatisticRespVO {

    @Schema(description = "编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "4693")
    @ExcelProperty("编号")
    private Long id;

    @Schema(description = "课程编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "28731")
    @ExcelProperty("课程编号")
    private Long courseLessonId;

    @Schema(description = "学习次数", requiredMode = Schema.RequiredMode.REQUIRED, example = "21837")
    @ExcelProperty("学习次数")
    private Integer count;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}