package com.nnnmkj.thai.module.course.dal.dataobject.lessonchapter;

import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.nnnmkj.thai.framework.mybatis.core.dataobject.BaseDO;
import lombok.*;

/**
 * 课程章节附件 DO
 *
 * <AUTHOR>
 */
@TableName("course_lesson_chatper_attachment")
@KeySequence("course_lesson_chatper_attachment_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class LessonChatperAttachmentDO extends BaseDO {

    /**
     * 编号
     */
    @TableId
    private Long id;
    /**
     * 章节ID
     */
    private Long chapterId;
    /**
     * 附件类型
     *
     * 枚举 {@link TODO attachment_type 对应的类}
     */
    private Integer attachmentType;
    /**
     * 附件地址
     */
    private String attachmentUrl;
    /**
     * 附件内容
     */
    private String attachmentContent;
    /**
     * 总时间(s)
     */
    private Integer totalTime;
    /**
     * 是否任务点
     */
    private Integer isTaskPoint;
    /**
     * 是否原位播放
     */
    private Integer isPlayOriginal;
    /**
     * 是否可以拖拽
     */
    private Integer isDrag;
    /**
     * 是否倍速播放
     */
    private Integer isPlaySpeed;
    /**
     * 排序
     */
    private Integer sort;

}