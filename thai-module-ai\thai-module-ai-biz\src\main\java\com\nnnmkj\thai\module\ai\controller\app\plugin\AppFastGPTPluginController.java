package com.nnnmkj.thai.module.ai.controller.app.plugin;

import com.nnnmkj.thai.framework.common.pojo.CommonResult;
import com.nnnmkj.thai.module.ai.controller.app.plugin.vo.FastGPTExcelGenerateReqVO;
import com.nnnmkj.thai.module.ai.controller.app.plugin.vo.FastGPTExcelGenerateRespVO;
import com.nnnmkj.thai.module.ai.service.plugin.FastGPTPluginService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import static com.nnnmkj.thai.framework.common.pojo.CommonResult.success;

@Tag(name = "FastGPT - 插件")
@RestController
@RequestMapping("/fast-gpt/plugin")
@Validated
@Slf4j
public class AppFastGPTPluginController {

    @Resource
    private FastGPTPluginService fastGPTPluginService;

    @PostMapping("/generate-excel")
    @Operation(summary = "生成Excel文件并上传到OSS")
    public CommonResult<FastGPTExcelGenerateRespVO> generateExcel(@Valid @RequestBody FastGPTExcelGenerateReqVO reqVO) {
        log.debug("[generateExcel][接收到FastGPT数据，标题：{}，数据行数：{}]", reqVO.getTitle(), reqVO.getRowData().size());
        FastGPTExcelGenerateRespVO respVO = fastGPTPluginService.generateExcelAndUpload(reqVO);
        return success(respVO);
    }

}
