package com.nnnmkj.thai.module.course.service.lessoncollectionstatistic;

import com.nnnmkj.thai.framework.common.pojo.PageResult;
import com.nnnmkj.thai.module.course.controller.admin.lessoncollectionstatistic.vo.LessonCollectionStatisticPageReqVO;
import com.nnnmkj.thai.module.course.controller.admin.lessoncollectionstatistic.vo.LessonCollectionStatisticSaveReqVO;
import com.nnnmkj.thai.module.course.dal.dataobject.lessoncollectionstatistic.LessonCollectionStatisticDO;
import jakarta.validation.Valid;

/**
 * 课程收藏统计 Service 接口
 *
 * <AUTHOR>
 */
public interface LessonCollectionStatisticService {

    /**
     * 创建课程收藏统计
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createLessonCollectionStatistic(@Valid LessonCollectionStatisticSaveReqVO createReqVO);

    /**
     * 更新课程收藏统计
     *
     * @param updateReqVO 更新信息
     */
    void updateLessonCollectionStatistic(@Valid LessonCollectionStatisticSaveReqVO updateReqVO);

    /**
     * 删除课程收藏统计
     *
     * @param id 编号
     */
    void deleteLessonCollectionStatistic(Long id);

    /**
     * 获得课程收藏统计
     *
     * @param id 编号
     * @return 课程收藏统计
     */
    LessonCollectionStatisticDO getLessonCollectionStatistic(Long id);

    /**
     * 获得课程收藏统计分页
     *
     * @param pageReqVO 分页查询
     * @return 课程收藏统计分页
     */
    PageResult<LessonCollectionStatisticDO> getLessonCollectionStatisticPage(LessonCollectionStatisticPageReqVO pageReqVO);

}