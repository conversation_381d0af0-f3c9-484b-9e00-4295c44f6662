package com.nnnmkj.thai.module.course.controller.admin.lessoncollectionstatistic.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

@Schema(description = "管理后台 - 课程收藏统计新增/修改 Request VO")
@Data
public class LessonCollectionStatisticSaveReqVO {

    @Schema(description = "编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "7569")
    private Long id;

    @Schema(description = "课程编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "29017")
    @NotNull(message = "课程编号不能为空")
    private Long courseLessonId;

    @Schema(description = "收藏次数", requiredMode = Schema.RequiredMode.REQUIRED, example = "25411")
    @NotNull(message = "收藏次数不能为空")
    private Integer count;

}