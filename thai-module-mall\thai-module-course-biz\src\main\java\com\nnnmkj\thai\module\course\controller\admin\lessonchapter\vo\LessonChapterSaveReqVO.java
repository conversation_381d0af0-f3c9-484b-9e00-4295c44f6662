package com.nnnmkj.thai.module.course.controller.admin.lessonchapter.vo;

import com.nnnmkj.thai.module.course.dal.dataobject.lessonchapter.LessonChatperAttachmentDO;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.List;

@Schema(description = "管理后台 - 课程章节新增/修改 Request VO")
@Data
public class LessonChapterSaveReqVO {

    @Schema(description = "编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "200")
    private Long id;

    @Schema(description = "课程ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "100")
    @NotNull(message = "课程ID不能为空")
    private Long courseId;

    @Schema(description = "父章节ID", example = "10086")
    private Long parentId;

    @Schema(description = "章节名称", example = "王五")
    private String name;

    @Schema(description = "章节简介")
    private String blurb;

    @Schema(description = "排序")
    private Integer sort;

    @Schema(description = "课程章节附件列表")
    private List<LessonChatperAttachmentDO> lessonChatperAttachments;

}