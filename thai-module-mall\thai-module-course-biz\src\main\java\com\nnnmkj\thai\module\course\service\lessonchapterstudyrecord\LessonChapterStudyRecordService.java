package com.nnnmkj.thai.module.course.service.lessonchapterstudyrecord;

import com.nnnmkj.thai.framework.common.pojo.PageResult;
import com.nnnmkj.thai.module.course.controller.admin.lessonchapterstudyrecord.vo.LessonChapterStudyRecordPageReqVO;
import com.nnnmkj.thai.module.course.controller.admin.lessonchapterstudyrecord.vo.LessonChapterStudyRecordSaveReqVO;
import com.nnnmkj.thai.module.course.dal.dataobject.lessonchapterstudyrecord.LessonChapterStudyRecordDO;
import jakarta.validation.Valid;

import java.util.List;

/**
 * 课程章节学习记录 Service 接口
 *
 * <AUTHOR>
 */
public interface LessonChapterStudyRecordService {

    /**
     * 创建课程章节学习记录
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createLessonChapterStudyRecord(@Valid LessonChapterStudyRecordSaveReqVO createReqVO);

    /**
     * 更新课程章节学习记录
     *
     * @param updateReqVO 更新信息
     */
    void updateLessonChapterStudyRecord(@Valid LessonChapterStudyRecordSaveReqVO updateReqVO);

    /**
     * 删除课程章节学习记录
     *
     * @param id 编号
     */
    void deleteLessonChapterStudyRecord(Long id);

    /**
     * 获得课程章节学习记录
     *
     * @param id 编号
     * @return 课程章节学习记录
     */
    LessonChapterStudyRecordDO getLessonChapterStudyRecord(Long id);

    /**
     * 获得课程章节学习记录分页
     *
     * @param pageReqVO 分页查询
     * @return 课程章节学习记录分页
     */
    PageResult<LessonChapterStudyRecordDO> getLessonChapterStudyRecordPage(LessonChapterStudyRecordPageReqVO pageReqVO);
    
    /**
     * 获得所有课程章节学习记录
     * 
     * @return 所有课程章节学习记录列表
     */
    List<LessonChapterStudyRecordDO> getAllLessonChapterStudyRecords();

}