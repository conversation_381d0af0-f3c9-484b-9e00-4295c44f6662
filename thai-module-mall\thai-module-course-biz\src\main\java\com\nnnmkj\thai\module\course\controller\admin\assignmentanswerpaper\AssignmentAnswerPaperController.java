package com.nnnmkj.thai.module.course.controller.admin.assignmentanswerpaper;

import com.nnnmkj.thai.framework.apilog.core.annotation.ApiAccessLog;
import com.nnnmkj.thai.framework.common.pojo.CommonResult;
import com.nnnmkj.thai.framework.common.pojo.PageParam;
import com.nnnmkj.thai.framework.common.pojo.PageResult;
import com.nnnmkj.thai.framework.common.util.object.BeanUtils;
import com.nnnmkj.thai.framework.excel.core.util.ExcelUtils;
import com.nnnmkj.thai.module.course.controller.admin.assignmentanswerpaper.vo.AssignmentAnswerPaperPageReqVO;
import com.nnnmkj.thai.module.course.controller.admin.assignmentanswerpaper.vo.AssignmentAnswerPaperRespVO;
import com.nnnmkj.thai.module.course.controller.admin.assignmentanswerpaper.vo.AssignmentAnswerPaperSaveReqVO;
import com.nnnmkj.thai.module.course.dal.dataobject.assignmentanswerpaper.AssignmentAnswerPaperDO;
import com.nnnmkj.thai.module.course.service.assignmentanswerpaper.AssignmentAnswerPaperService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.util.List;

import static com.nnnmkj.thai.framework.apilog.core.enums.OperateTypeEnum.EXPORT;
import static com.nnnmkj.thai.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 课程作业作答试卷")
@RestController
@RequestMapping("/course/assignment-answer-paper")
@Validated
public class AssignmentAnswerPaperController {

    @Resource
    private AssignmentAnswerPaperService assignmentAnswerPaperService;

    @PostMapping("/create")
    @Operation(summary = "创建课程作业作答试卷")
    @PreAuthorize("@ss.hasPermission('course:assignment-answer-paper:create')")
    public CommonResult<Long> createAssignmentAnswerPaper(@Valid @RequestBody AssignmentAnswerPaperSaveReqVO createReqVO) {
        return success(assignmentAnswerPaperService.createAssignmentAnswerPaper(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新课程作业作答试卷")
    @PreAuthorize("@ss.hasPermission('course:assignment-answer-paper:update')")
    public CommonResult<Boolean> updateAssignmentAnswerPaper(@Valid @RequestBody AssignmentAnswerPaperSaveReqVO updateReqVO) {
        assignmentAnswerPaperService.updateAssignmentAnswerPaper(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除课程作业作答试卷")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('course:assignment-answer-paper:delete')")
    public CommonResult<Boolean> deleteAssignmentAnswerPaper(@RequestParam("id") Long id) {
        assignmentAnswerPaperService.deleteAssignmentAnswerPaper(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得课程作业作答试卷")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('course:assignment-answer-paper:query')")
    public CommonResult<AssignmentAnswerPaperRespVO> getAssignmentAnswerPaper(@RequestParam("id") Long id) {
        AssignmentAnswerPaperDO assignmentAnswerPaper = assignmentAnswerPaperService.getAssignmentAnswerPaper(id);
        return success(BeanUtils.toBean(assignmentAnswerPaper, AssignmentAnswerPaperRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得课程作业作答试卷分页")
    @PreAuthorize("@ss.hasPermission('course:assignment-answer-paper:query')")
    public CommonResult<PageResult<AssignmentAnswerPaperRespVO>> getAssignmentAnswerPaperPage(@Valid AssignmentAnswerPaperPageReqVO pageReqVO) {
        PageResult<AssignmentAnswerPaperDO> pageResult = assignmentAnswerPaperService.getAssignmentAnswerPaperPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, AssignmentAnswerPaperRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出课程作业作答试卷 Excel")
    @PreAuthorize("@ss.hasPermission('course:assignment-answer-paper:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportAssignmentAnswerPaperExcel(@Valid AssignmentAnswerPaperPageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<AssignmentAnswerPaperDO> list = assignmentAnswerPaperService.getAssignmentAnswerPaperPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "课程作业作答试卷.xls", "数据", AssignmentAnswerPaperRespVO.class,
                        BeanUtils.toBean(list, AssignmentAnswerPaperRespVO.class));
    }

}