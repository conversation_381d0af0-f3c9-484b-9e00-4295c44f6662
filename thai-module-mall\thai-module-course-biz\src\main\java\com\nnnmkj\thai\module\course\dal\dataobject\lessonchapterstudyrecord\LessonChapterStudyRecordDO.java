package com.nnnmkj.thai.module.course.dal.dataobject.lessonchapterstudyrecord;

import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.nnnmkj.thai.framework.mybatis.core.dataobject.BaseDO;
import lombok.*;

import java.math.BigDecimal;

/**
 * 课程章节学习记录 DO
 *
 * <AUTHOR>
 */
@TableName("course_lesson_chapter_study_record")
@KeySequence("course_lesson_chapter_study_record_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class LessonChapterStudyRecordDO extends BaseDO {

    /**
     * 编号
     */
    @TableId
    private Long id;
    /**
     * 课程ID
     */
    private Long courseId;
    /**
     * 章节ID
     */
    private Long chapterId;
    /**
     * 用户ID
     */
    private Long userId;
    /**
     * 附件ID
     */
    private Long attachmentId;
    /**
     * 总时间(s)
     */
    private Integer allTime;
    /**
     * 当前时间(s)
     */
    private Integer currentTimeSec;
    /**
     * 学习进度百分比
     */
    private BigDecimal progressPercentage;
    /**
     * 状态
     *
     * 枚举 {@link TODO lesson_process_status 对应的类}
     */
    private Integer status;

}