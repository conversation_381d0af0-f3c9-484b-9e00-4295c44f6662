package com.nnnmkj.thai.module.course.service.membergrouplesson;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.nnnmkj.thai.framework.common.enums.CommonStatusEnum;
import com.nnnmkj.thai.framework.common.util.object.BeanUtils;
import com.nnnmkj.thai.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.nnnmkj.thai.module.course.controller.app.membergrouplesson.vo.AppMemberGroupLessonRespVO;
import com.nnnmkj.thai.module.course.controller.app.membergrouplesson.vo.AppMemberGroupLessonSaveReqVO;
import com.nnnmkj.thai.module.course.controller.app.membergrouplesson.vo.AppMemberGroupLessonSearchReqVO;
import com.nnnmkj.thai.module.course.convert.membergrouplesson.MembergrouplessonConvert;
import com.nnnmkj.thai.module.course.dal.dataobject.lesson.LessonDO;
import com.nnnmkj.thai.module.course.dal.dataobject.membergrouplesson.MemberGroupLessonDO;
import com.nnnmkj.thai.module.course.dal.mysql.membergrouplesson.MemberGroupLessonMapper;
import com.nnnmkj.thai.module.course.service.lesson.LessonService;
import com.nnnmkj.thai.module.member.api.group.MemberGroupApi;
import com.nnnmkj.thai.module.member.api.group.dto.MemberGroupRespDTO;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.nnnmkj.thai.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.nnnmkj.thai.module.course.enums.ErrorCodeConstants.MEMBER_GROUP_LESSON_NOT_EXISTS;

/**
 * 班级-课程关联 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class AppMemberGroupLessonServiceImpl implements AppMemberGroupLessonService {

    @Resource
    private MemberGroupLessonMapper memberGroupLessonMapper;
    
    @Resource
    private MemberGroupApi memberGroupApi;
    
    @Resource
    private LessonService lessonService;

    @Override
    public Long createMemberGroupLesson(AppMemberGroupLessonSaveReqVO createReqVO) {
        // 插入
        MemberGroupLessonDO memberGroupLesson = BeanUtils.toBean(createReqVO, MemberGroupLessonDO.class);
        memberGroupLessonMapper.insert(memberGroupLesson);
        // 返回
        return memberGroupLesson.getId();
    }

    @Override
    public void deleteMemberGroupLesson(Long id) {
        // 校验存在
        validateMemberGroupLessonExists(id);
        // 删除
        memberGroupLessonMapper.deleteById(id);
    }

    private void validateMemberGroupLessonExists(Long id) {
        if (memberGroupLessonMapper.selectById(id) == null) {
            throw exception(MEMBER_GROUP_LESSON_NOT_EXISTS);
        }
    }

    @Override
    public List<MemberGroupLessonDO> getMemberGroupLessonListByCourseId(Long courseId) {
        return memberGroupLessonMapper.selectList(
            MemberGroupLessonDO::getCourseId, courseId);
    }
    
    @Override
    public List<MemberGroupLessonDO> searchMemberGroupLessons(AppMemberGroupLessonSearchReqVO reqVO) {
        // 根据课程ID查询关联关系
        List<MemberGroupLessonDO> memberGroupLessons = memberGroupLessonMapper.selectList(
                new LambdaQueryWrapperX<MemberGroupLessonDO>()
                .eqIfPresent(MemberGroupLessonDO::getCourseId, reqVO.getCourseId()));
        
        if (CollUtil.isEmpty(memberGroupLessons)) {
            return Collections.emptyList();
        }
        
        // 如果没有关键词，直接返回结果
        if (StrUtil.isBlank(reqVO.getKeyword())) {
            return memberGroupLessons;
        }
        
        // 获取所有班级ID
        List<Long> groupIds = memberGroupLessons.stream()
                .map(MemberGroupLessonDO::getGroupId)
                .collect(Collectors.toList());
        
        // 获取班级详细信息
        List<MemberGroupRespDTO> groupList = memberGroupApi.getGroupList(groupIds);
        if (CollUtil.isEmpty(groupList)) {
            return Collections.emptyList();
        }
        
        // 过滤掉非开启状态的班级
        List<MemberGroupRespDTO> enabledGroups = groupList.stream()
                .filter(group -> CommonStatusEnum.ENABLE.getStatus().equals(group.getStatus()))
                .collect(Collectors.toList());
        
        if (CollUtil.isEmpty(enabledGroups)) {
            return Collections.emptyList();
        }
        
        // 根据关键词过滤班级
        String keyword = reqVO.getKeyword().toLowerCase();
        List<MemberGroupRespDTO> filteredGroups = enabledGroups.stream()
                .filter(group -> {
                    // 班级名称包含关键词
                    if (StrUtil.isNotBlank(group.getName()) && 
                            group.getName().toLowerCase().contains(keyword)) {
                        return true;
                    }
                    // 班级备注包含关键词
                    return StrUtil.isNotBlank(group.getRemark()) && 
                            group.getRemark().toLowerCase().contains(keyword);
                })
                .collect(Collectors.toList());
        
        if (CollUtil.isEmpty(filteredGroups)) {
            return Collections.emptyList();
        }
        
        // 获取匹配关键词的班级ID列表
        List<Long> filteredGroupIds = filteredGroups.stream()
                .map(MemberGroupRespDTO::getId).toList();
        
        // 过滤只保留匹配关键词的班级关联
        return memberGroupLessons.stream()
                .filter(relation -> filteredGroupIds.contains(relation.getGroupId()))
                .collect(Collectors.toList());
    }
    
    /**
     * 获取课程关联班级的详细信息列表
     *
     * @param courseId 课程ID
     * @return 班级详细信息列表
     */
    public List<AppMemberGroupLessonRespVO> getClassesWithDetailsByCourseId(Long courseId) {
        // 获取课程信息
        LessonDO lesson = lessonService.getLesson(courseId);
        if (lesson == null) {
            return Collections.emptyList();
        }
        
        // 获取课程关联的班级列表
        List<MemberGroupLessonDO> memberGroupLessons = getMemberGroupLessonListByCourseId(courseId);
        if (CollUtil.isEmpty(memberGroupLessons)) {
            return Collections.emptyList();
        }
        
        return getClassesWithDetails(memberGroupLessons, Collections.singletonList(lesson));
    }
    
    /**
     * 获取课程关联班级的详细信息列表（搜索）
     *
     * @param reqVO 搜索请求
     * @return 班级详细信息列表
     */
    public List<AppMemberGroupLessonRespVO> searchClassesWithDetails(AppMemberGroupLessonSearchReqVO reqVO) {
        // 获取课程信息
        LessonDO lesson = null;
        if (reqVO.getCourseId() != null) {
            lesson = lessonService.getLesson(reqVO.getCourseId());
            if (lesson == null) {
                return Collections.emptyList();
            }
        }
        
        // 搜索课程关联的班级列表
        List<MemberGroupLessonDO> memberGroupLessons = searchMemberGroupLessons(reqVO);
        if (CollUtil.isEmpty(memberGroupLessons)) {
            return Collections.emptyList();
        }
        
        return getClassesWithDetails(memberGroupLessons, lesson != null ? Collections.singletonList(lesson) : Collections.emptyList());
    }
    
    /**
     * 获取班级的详细信息
     * 
     * @param memberGroupLessons 班级课程关联列表
     * @param lessons 课程列表
     * @return 班级详细信息列表
     */
    private List<AppMemberGroupLessonRespVO> getClassesWithDetails(List<MemberGroupLessonDO> memberGroupLessons, List<LessonDO> lessons) {
        // 获取所有班级ID
        List<Long> groupIds = memberGroupLessons.stream()
                .map(MemberGroupLessonDO::getGroupId)
                .collect(Collectors.toList());
        
        // 获取所有班级详细信息
        List<MemberGroupRespDTO> groupList = memberGroupApi.getGroupList(groupIds);
        
        // 过滤掉非开启状态的班级
        List<MemberGroupRespDTO> enabledGroups = groupList.stream()
                .filter(group -> CommonStatusEnum.ENABLE.getStatus().equals(group.getStatus()))
                .collect(Collectors.toList());
        
        if (CollUtil.isEmpty(enabledGroups)) {
            return Collections.emptyList();
        }
        
        // 创建班级ID到班级详情的映射
        Map<Long, MemberGroupRespDTO> groupMap = enabledGroups.stream()
                .collect(Collectors.toMap(MemberGroupRespDTO::getId, group -> group));
        
        // 过滤只保留开启状态班级的关联
        List<MemberGroupLessonDO> filteredGroupLessons = memberGroupLessons.stream()
                .filter(relation -> groupMap.containsKey(relation.getGroupId()))
                .collect(Collectors.toList());
        
        // 转换为VO
        List<AppMemberGroupLessonRespVO> respVOList = MembergrouplessonConvert.INSTANCE.appConvertList(
                filteredGroupLessons, lessons);
        
        // 填充班级详细信息
        for (AppMemberGroupLessonRespVO respVO : respVOList) {
            MemberGroupRespDTO group = groupMap.get(respVO.getGroupId());
            if (group != null) {
                respVO.setGroupName(group.getName());
                respVO.setGroupRemark(group.getRemark());
                respVO.setGroupUserId(group.getUserId());
            }
        }
        
        return respVOList;
    }

    /**
     * 批量为课程添加班级（已存在的不重复添加）
     *
     * @param courseId 课程ID
     * @param groupIds 班级ID列表
     * @return 创建的记录数
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int batchCreateGroupsInCourse(Long courseId, List<Long> groupIds) {
        if (CollUtil.isEmpty(groupIds)) {
            return 0;
        }
        
        // 查询已存在的关联关系，避免重复创建
        List<MemberGroupLessonDO> existingRelations = memberGroupLessonMapper.selectList(
                new LambdaQueryWrapperX<MemberGroupLessonDO>()
                        .eq(MemberGroupLessonDO::getCourseId, courseId)
                        .in(MemberGroupLessonDO::getGroupId, groupIds)
        );
        
        // 找出未关联的班级ID
        List<Long> existingGroupIds = existingRelations.stream()
                .map(MemberGroupLessonDO::getGroupId).toList();
        List<Long> newGroupIds = groupIds.stream()
                .filter(id -> !existingGroupIds.contains(id))
                .collect(Collectors.toList());
        
        if (CollUtil.isEmpty(newGroupIds)) {
            return 0; // 所有班级都已关联，无需创建
        }
        
        // 批量插入新的关联关系
        List<MemberGroupLessonDO> toInsert = new ArrayList<>();
        for (Long groupId : newGroupIds) {
            MemberGroupLessonDO relation = new MemberGroupLessonDO();
            relation.setGroupId(groupId);
            relation.setCourseId(courseId);
            toInsert.add(relation);
        }
        
        // 批量插入
        for (MemberGroupLessonDO relation : toInsert) {
            memberGroupLessonMapper.insert(relation);
        }
        
        return toInsert.size();
    }
    
    /**
     * 批量删除课程中的班级
     *
     * @param courseId 课程ID
     * @param groupIds 班级ID列表
     * @return 删除的记录数
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int batchDeleteGroupsInCourse(Long courseId, List<Long> groupIds) {
        if (CollUtil.isEmpty(groupIds)) {
            return 0;
        }
        
        // 删除匹配条件的关联关系
        return memberGroupLessonMapper.delete(
                new LambdaQueryWrapperX<MemberGroupLessonDO>()
                        .eq(MemberGroupLessonDO::getCourseId, courseId)
                        .in(MemberGroupLessonDO::getGroupId, groupIds)
        );
    }

    /**
     * 批量创建班级-课程关联
     *
     * @param courseIds 课程ID列表
     * @param classId 班级ID
     * @return 创建的记录数
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int batchCreateMemberGroupLesson(List<Long> courseIds, Long classId) {
        if (CollUtil.isEmpty(courseIds)) {
            return 0;
        }
        
        // 查询已存在的关联关系，避免重复创建
        List<MemberGroupLessonDO> existingRelations = memberGroupLessonMapper.selectList(
                new LambdaQueryWrapperX<MemberGroupLessonDO>()
                        .eq(MemberGroupLessonDO::getGroupId, classId)
                        .in(MemberGroupLessonDO::getCourseId, courseIds)
        );
        
        // 找出未关联的课程ID
        List<Long> existingCourseIds = existingRelations.stream()
                .map(MemberGroupLessonDO::getCourseId).toList();
        List<Long> newCourseIds = courseIds.stream()
                .filter(id -> !existingCourseIds.contains(id))
                .collect(Collectors.toList());
        
        if (CollUtil.isEmpty(newCourseIds)) {
            return 0; // 所有课程都已关联，无需创建
        }
        
        // 批量插入新的关联关系
        List<MemberGroupLessonDO> toInsert = new ArrayList<>();
        for (Long courseId : newCourseIds) {
            MemberGroupLessonDO relation = new MemberGroupLessonDO();
            relation.setGroupId(classId);
            relation.setCourseId(courseId);
            toInsert.add(relation);
        }
        
        // 批量插入
        for (MemberGroupLessonDO relation : toInsert) {
            memberGroupLessonMapper.insert(relation);
        }
        
        return toInsert.size();
    }
    
    /**
     * 批量删除班级-课程关联
     *
     * @param courseIds 课程ID列表
     * @param classId 班级ID
     * @return 删除的记录数
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int batchDeleteMemberGroupLesson(List<Long> courseIds, Long classId) {
        if (CollUtil.isEmpty(courseIds)) {
            return 0;
        }
        
        // 删除匹配条件的关联关系
        return memberGroupLessonMapper.delete(
                new LambdaQueryWrapperX<MemberGroupLessonDO>()
                        .eq(MemberGroupLessonDO::getGroupId, classId)
                        .in(MemberGroupLessonDO::getCourseId, courseIds)
        );
    }
}