package com.nnnmkj.thai.module.course.controller.app.lesson.vo;

import com.nnnmkj.thai.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

import static com.nnnmkj.thai.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 课程分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class AppLessonPageReqVO extends PageParam {

    @Schema(description = "课程标题")
    private String title;

    @Schema(description = "课程简介", example = "你猜")
    private String description;

    @Schema(description = "课程封面图URL")
    private String coverImage;

    @Schema(description = "用户ID", example = "14855")
    private Long userId;

    @Schema(description = "用户类型", example = "1")
    private Integer userType;

    @Schema(description = "可见范围", example = "1")
    private Integer visibility;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

    @Schema(description = "排序字段，可选值：1-收藏数, 2-学习次数, 3-点击量, 4-更新时间")
    private Integer sortField;

    @Schema(description = "排序方式，可选值：asc-升序, desc-降序，默认为 desc")
    private String sortOrder;

}