package com.nnnmkj.thai.module.course.controller.app.lesson;

import com.nnnmkj.thai.framework.common.enums.UserTypeEnum;
import com.nnnmkj.thai.framework.common.pojo.CommonResult;
import com.nnnmkj.thai.framework.common.pojo.PageResult;
import com.nnnmkj.thai.framework.security.core.util.SecurityFrameworkUtils;
import com.nnnmkj.thai.module.course.controller.app.lesson.vo.AppLessonPageReqVO;
import com.nnnmkj.thai.module.course.controller.app.lesson.vo.AppLessonRespVO;
import com.nnnmkj.thai.module.course.service.lesson.AppQualityLessonService;
import com.nnnmkj.thai.module.learning.enums.WordSetVisibilityEnum;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import static com.nnnmkj.thai.framework.common.pojo.CommonResult.success;

/**
 * 用户 APP - 优质课程
 */
@Tag(name = "用户 APP - 优质课程")
@RestController
@RequestMapping("/course/quality-lesson")
@Validated
public class AppQualityLessonController {

    @Resource
    private AppQualityLessonService qualityLessonService;

    @GetMapping("/page")
    @Operation(summary = "获取优质课程分页")
    public CommonResult<PageResult<AppLessonRespVO>> getQualityLessons(@Valid AppLessonPageReqVO pageReqVO) {
        pageReqVO.setUserId(SecurityFrameworkUtils.getLoginUserId());
        pageReqVO.setUserType(UserTypeEnum.MEMBER.getValue());
        pageReqVO.setVisibility(WordSetVisibilityEnum.ALL.getType());
        return success(qualityLessonService.getQualityLessons(pageReqVO));
    }

    @PostMapping("/view")
    @Operation(summary = "增加课程点击量")
    @Parameter(name = "lessonId", description = "课程ID", required = true, example = "1024")
    public CommonResult<Boolean> incrementViewCount(@RequestParam("lessonId") Long lessonId) {
        qualityLessonService.incrementViewCount(lessonId, SecurityFrameworkUtils.getLoginUserId());
        return success(true);
    }
}
