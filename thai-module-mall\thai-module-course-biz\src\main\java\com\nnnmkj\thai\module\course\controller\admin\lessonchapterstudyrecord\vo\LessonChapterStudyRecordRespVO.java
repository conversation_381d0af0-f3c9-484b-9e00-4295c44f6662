package com.nnnmkj.thai.module.course.controller.admin.lessonchapterstudyrecord.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.nnnmkj.thai.framework.excel.core.annotations.DictFormat;
import com.nnnmkj.thai.framework.excel.core.convert.DictConvert;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Schema(description = "管理后台 - 课程章节学习记录 Response VO")
@Data
@ExcelIgnoreUnannotated
public class LessonChapterStudyRecordRespVO {

    @Schema(description = "编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "24851")
    @ExcelProperty("编号")
    private Long id;

    @Schema(description = "课程ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "23385")
    @ExcelProperty("课程ID")
    private Long courseId;

    @Schema(description = "章节ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "3322")
    @ExcelProperty("章节ID")
    private Long chapterId;

    @Schema(description = "用户ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "5489")
    @ExcelProperty("用户ID")
    private Long userId;

    @Schema(description = "附件ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "27388")
    @ExcelProperty("附件ID")
    private Long attachmentId;

    @Schema(description = "总时间(s)")
    @ExcelProperty("总时间(s)")
    private Integer allTime;

    @Schema(description = "当前时间(s)")
    @ExcelProperty("当前时间(s)")
    private Integer currentTimeSec;

    @Schema(description = "学习进度百分比", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("学习进度百分比")
    private BigDecimal progressPercentage;

    @Schema(description = "状态", example = "1")
    @ExcelProperty(value = "状态", converter = DictConvert.class)
    @DictFormat("lesson_process_status") // TODO 代码优化：建议设置到对应的 DictTypeConstants 枚举类中
    private Integer status;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;
    
    @Schema(description = "课程标题")
    @ExcelProperty("课程标题")
    private String courseTitle;
    
    @Schema(description = "章节名称")
    @ExcelProperty("章节名称")
    private String chapterName;
    
    @Schema(description = "用户昵称")
    @ExcelProperty("用户昵称")
    private String nickname;
}