package com.nnnmkj.thai.module.course.service.lessoncollectionstatistic;

import com.nnnmkj.thai.framework.common.pojo.PageResult;
import com.nnnmkj.thai.framework.common.util.object.BeanUtils;
import com.nnnmkj.thai.module.course.controller.admin.lessoncollectionstatistic.vo.LessonCollectionStatisticPageReqVO;
import com.nnnmkj.thai.module.course.controller.admin.lessoncollectionstatistic.vo.LessonCollectionStatisticSaveReqVO;
import com.nnnmkj.thai.module.course.dal.dataobject.lessoncollectionstatistic.LessonCollectionStatisticDO;
import com.nnnmkj.thai.module.course.dal.mysql.lessoncollectionstatistic.LessonCollectionStatisticMapper;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import static com.nnnmkj.thai.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.nnnmkj.thai.module.course.enums.ErrorCodeConstants.LESSON_COLLECTION_STATISTIC_NOT_EXISTS;

/**
 * 课程收藏统计 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class LessonCollectionStatisticServiceImpl implements LessonCollectionStatisticService {

    @Resource
    private LessonCollectionStatisticMapper lessonCollectionStatisticMapper;

    @Override
    public Long createLessonCollectionStatistic(LessonCollectionStatisticSaveReqVO createReqVO) {
        // 插入
        LessonCollectionStatisticDO lessonCollectionStatistic = BeanUtils.toBean(createReqVO, LessonCollectionStatisticDO.class);
        lessonCollectionStatisticMapper.insert(lessonCollectionStatistic);
        // 返回
        return lessonCollectionStatistic.getId();
    }

    @Override
    public void updateLessonCollectionStatistic(LessonCollectionStatisticSaveReqVO updateReqVO) {
        // 校验存在
        validateLessonCollectionStatisticExists(updateReqVO.getId());
        // 更新
        LessonCollectionStatisticDO updateObj = BeanUtils.toBean(updateReqVO, LessonCollectionStatisticDO.class);
        lessonCollectionStatisticMapper.updateById(updateObj);
    }

    @Override
    public void deleteLessonCollectionStatistic(Long id) {
        // 校验存在
        validateLessonCollectionStatisticExists(id);
        // 删除
        lessonCollectionStatisticMapper.deleteById(id);
    }

    private void validateLessonCollectionStatisticExists(Long id) {
        if (lessonCollectionStatisticMapper.selectById(id) == null) {
            throw exception(LESSON_COLLECTION_STATISTIC_NOT_EXISTS);
        }
    }

    @Override
    public LessonCollectionStatisticDO getLessonCollectionStatistic(Long id) {
        return lessonCollectionStatisticMapper.selectById(id);
    }

    @Override
    public PageResult<LessonCollectionStatisticDO> getLessonCollectionStatisticPage(LessonCollectionStatisticPageReqVO pageReqVO) {
        return lessonCollectionStatisticMapper.selectPage(pageReqVO);
    }
}