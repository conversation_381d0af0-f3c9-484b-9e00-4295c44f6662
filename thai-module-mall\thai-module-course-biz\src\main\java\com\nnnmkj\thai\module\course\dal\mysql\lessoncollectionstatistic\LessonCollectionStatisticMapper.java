package com.nnnmkj.thai.module.course.dal.mysql.lessoncollectionstatistic;

import com.nnnmkj.thai.framework.common.pojo.PageResult;
import com.nnnmkj.thai.framework.mybatis.core.mapper.BaseMapperX;
import com.nnnmkj.thai.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.nnnmkj.thai.module.course.controller.admin.lessoncollectionstatistic.vo.LessonCollectionStatisticPageReqVO;
import com.nnnmkj.thai.module.course.dal.dataobject.lessoncollectionstatistic.LessonCollectionStatisticDO;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 课程收藏统计 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface LessonCollectionStatisticMapper extends BaseMapperX<LessonCollectionStatisticDO> {

    default PageResult<LessonCollectionStatisticDO> selectPage(LessonCollectionStatisticPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<LessonCollectionStatisticDO>()
                .eqIfPresent(LessonCollectionStatisticDO::getCourseLessonId, reqVO.getCourseLessonId())
                .eqIfPresent(LessonCollectionStatisticDO::getCount, reqVO.getCount())
                .betweenIfPresent(LessonCollectionStatisticDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(LessonCollectionStatisticDO::getId));
    }

    /**
     * 通用原子更新方法
     * @param delta 变化量（正数增加，负数减少）
     */
    @Insert("INSERT INTO course_lesson_collection_statistic (course_lesson_id, count) " +
            "VALUES (#{lessonId}, #{delta}) " +
            "ON DUPLICATE KEY UPDATE count = GREATEST(count + #{delta}, 0)")
    void atomicUpdateCount(@Param("lessonId") Long lessonId, @Param("delta") Integer delta);

}