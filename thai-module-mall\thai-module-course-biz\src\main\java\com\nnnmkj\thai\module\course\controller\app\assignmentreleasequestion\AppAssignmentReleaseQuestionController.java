package com.nnnmkj.thai.module.course.controller.app.assignmentreleasequestion;

import com.nnnmkj.thai.framework.common.pojo.CommonResult;
import com.nnnmkj.thai.framework.common.util.collection.CollectionUtils;
import com.nnnmkj.thai.framework.common.util.object.BeanUtils;
import com.nnnmkj.thai.module.course.controller.app.assignmentreleasequestion.vo.AppAssignmentReleaseQuestionAnswerRespVO;
import com.nnnmkj.thai.module.course.controller.app.assignmentreleasequestion.vo.AppAssignmentReleaseQuestionRespVO;
import com.nnnmkj.thai.module.course.dal.dataobject.assignmentanswerrecord.AssignmentAnswerRecordDO;
import com.nnnmkj.thai.module.course.service.assignmentanswerrecord.AssignmentAnswerRecordService;
import com.nnnmkj.thai.module.course.service.assignmentreleasequestion.AssignmentReleaseQuestionService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.constraints.NotNull;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Collections;
import java.util.List;
import java.util.Map;

import static com.nnnmkj.thai.framework.common.pojo.CommonResult.success;
import static com.nnnmkj.thai.framework.security.core.util.SecurityFrameworkUtils.getLoginUserId;


@Tag(name = "用户 App - 课程作业发布题目")
@RestController
@RequestMapping("/course/assignment-release-question")
@Validated
public class AppAssignmentReleaseQuestionController {

    @Resource
    private AssignmentReleaseQuestionService assignmentReleaseQuestionService;
    @Resource
    private AssignmentAnswerRecordService assignmentAnswerRecordService;

    @GetMapping("/view")
    @Operation(summary = "学生查看作业题目")
    @Parameter(name = "assignmentReleaseId", description = "课程作业发布编号", required = true)
    public CommonResult<List<AppAssignmentReleaseQuestionAnswerRespVO>> viewAssignmentReleaseQuestion(@NotNull(message = "请选择作业") Long assignmentReleaseId) {
        // 获取当前作业发布ID对应的题目列表
        List<AppAssignmentReleaseQuestionRespVO> questionList = assignmentReleaseQuestionService.getAssignmentReleaseQuestionList(assignmentReleaseId);
        if (questionList == null) {
            return success(Collections.emptyList());
        }

        // 转换为带用户答案信息的VO对象
        List<AppAssignmentReleaseQuestionAnswerRespVO> result = BeanUtils.toBean(questionList, AppAssignmentReleaseQuestionAnswerRespVO.class);

        // 查询该用户最新的作答记录
        List<AssignmentAnswerRecordDO> answerRecords = assignmentAnswerRecordService.getAssignmentAnswerRecordListLast(assignmentReleaseId, getLoginUserId());

        // 构建题目ID到答案的映射关系
        Map<Long, String> questionIdToAnswerMap = CollectionUtils.convertMap(answerRecords, AssignmentAnswerRecordDO::getQuestionId, AssignmentAnswerRecordDO::getAnswer);

        // 为每个题目设置对应的用户答案
        for (AppAssignmentReleaseQuestionAnswerRespVO questionVO : result) {
            Long questionId = questionVO.getId();
            String userAnswer = questionIdToAnswerMap.get(questionId);
            questionVO.setUserAnswer(userAnswer);
        }
        return success(result);
    }

}
