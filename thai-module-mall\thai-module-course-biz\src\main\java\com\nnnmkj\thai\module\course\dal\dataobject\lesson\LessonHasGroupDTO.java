package com.nnnmkj.thai.module.course.dal.dataobject.lesson;

import java.util.List;
import lombok.*;



/**
 * 课程 DTO
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
public class LessonHasGroupDTO extends LessonDO {

    /**
     * 用户昵称
     */
    private String nickname;

    /**
     * 班级ID
     */
    private List<Long> groupIds;

}
