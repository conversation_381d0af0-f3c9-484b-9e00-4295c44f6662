package com.nnnmkj.thai.module.course.dal.redis;

import com.alibaba.fastjson.JSON;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Repository;

import jakarta.annotation.Resource;
import java.time.Duration;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

import static com.nnnmkj.thai.module.course.dal.redis.RedisKeyConstants.ASSIGNMENT_REMINDER_SENT;

/**
 * 作业提醒 Redis DAO
 *
 * <AUTHOR>
 */
@Repository
public class AssignmentReminderRedisDAO {

    /**
     * 缓存过期时间：7天
     */
    private static final Duration CACHE_TIMEOUT = Duration.ofDays(7);
    
    /**
     * 作业提醒信息的Redis键前缀
     */
    private static final String ASSIGNMENT_REMINDER_INFO = "assignment_reminder_info:";
    
    /**
     * 作业提醒时间的Redis键前缀
     */
    private static final String ASSIGNMENT_REMINDER_TIME = "assignment_reminder_time:";

    @Resource
    private StringRedisTemplate stringRedisTemplate;

    /**
     * 检查是否已发送提醒
     *
     * @param assignmentReleaseId 作业发布ID
     * @param userId 用户ID
     * @return 是否已发送
     */
    public boolean isReminderSent(Long assignmentReleaseId, Long userId) {
        String key = formatKey(assignmentReleaseId, userId);
        return Boolean.TRUE.equals(stringRedisTemplate.hasKey(key));
    }

    /**
     * 标记提醒已发送
     *
     * @param assignmentReleaseId 作业发布ID
     * @param userId 用户ID
     */
    public void markReminderSent(Long assignmentReleaseId, Long userId) {
        String key = formatKey(assignmentReleaseId, userId);
        String value = LocalDateTime.now().format(DateTimeFormatter.ISO_LOCAL_DATE_TIME);
        stringRedisTemplate.opsForValue().set(key, value, CACHE_TIMEOUT);
    }

    /**
     * 批量检查用户是否已发送提醒
     *
     * @param assignmentReleaseId 作业发布ID
     * @param userIds 用户ID列表
     * @return 已发送提醒的用户ID列表
     */
    public java.util.Set<Long> getAlreadySentUsers(Long assignmentReleaseId, java.util.List<Long> userIds) {
        java.util.Set<Long> sentUsers = new java.util.HashSet<>();
        for (Long userId : userIds) {
            if (isReminderSent(assignmentReleaseId, userId)) {
                sentUsers.add(userId);
            }
        }
        return sentUsers;
    }

    /**
     * 批量标记提醒已发送
     *
     * @param assignmentReleaseId 作业发布ID
     * @param userIds 用户ID列表
     */
    public void batchMarkReminderSent(Long assignmentReleaseId, java.util.List<Long> userIds) {
        String timestamp = LocalDateTime.now().format(DateTimeFormatter.ISO_LOCAL_DATE_TIME);
        for (Long userId : userIds) {
            String key = formatKey(assignmentReleaseId, userId);
            stringRedisTemplate.opsForValue().set(key, timestamp, CACHE_TIMEOUT);
        }
    }
    
    /**
     * 保存作业提醒信息到Redis
     * 
     * @param assignmentReleaseId 作业发布ID
     * @param reminderTime 提醒时间
     * @param reminderInfo 提醒信息
     */
    public void saveAssignmentReminderInfo(Long assignmentReleaseId, LocalDateTime reminderTime, Map<String, Object> reminderInfo) {
        // 保存提醒信息
        String infoKey = ASSIGNMENT_REMINDER_INFO + assignmentReleaseId;
        String infoValue = JSON.toJSONString(reminderInfo);
        
        // 计算过期时间：截止时间后一天自动过期
        LocalDateTime endTime = (LocalDateTime) reminderInfo.get("endTime");
        Duration timeout = Duration.between(LocalDateTime.now(), endTime.plusDays(1));
        
        // 保存提醒信息，设置过期时间
        stringRedisTemplate.opsForValue().set(infoKey, infoValue, timeout);
        
        // 保存提醒时间，用于定时任务查询
        String timeKey = ASSIGNMENT_REMINDER_TIME + reminderTime.format(DateTimeFormatter.ofPattern("yyyyMMddHH"));
        stringRedisTemplate.opsForSet().add(timeKey, assignmentReleaseId.toString());
        
        // 设置提醒时间的过期时间为提醒时间后24小时
        stringRedisTemplate.expire(timeKey, Duration.between(LocalDateTime.now(), reminderTime.plusHours(24)));
    }
    
    /**
     * 获取指定时间需要发送提醒的作业ID列表
     * 
     * @param hour 指定小时（格式：yyyyMMddHH）
     * @return 作业发布ID列表
     */
    public List<Long> getAssignmentRemindersByTime(String hour) {
        String timeKey = ASSIGNMENT_REMINDER_TIME + hour;
        Set<String> assignmentIds = stringRedisTemplate.opsForSet().members(timeKey);
        if (assignmentIds == null || assignmentIds.isEmpty()) {
            return Collections.emptyList();
        }
        
        List<Long> result = new ArrayList<>();
        for (String id : assignmentIds) {
            try {
                result.add(Long.valueOf(id));
            } catch (NumberFormatException e) {
                // 忽略无效的ID
            }
        }
        return result;
    }
    
    /**
     * 获取作业提醒信息
     * 
     * @param assignmentReleaseId 作业发布ID
     * @return 提醒信息
     */
    public Map<String, Object> getAssignmentReminderInfo(Long assignmentReleaseId) {
        String infoKey = ASSIGNMENT_REMINDER_INFO + assignmentReleaseId;
        String infoValue = stringRedisTemplate.opsForValue().get(infoKey);
        if (infoValue == null) {
            return null;
        }
        return JSON.parseObject(infoValue);
    }
    
    /**
     * 删除作业提醒信息
     * 
     * @param assignmentReleaseId 作业发布ID
     * @param reminderTime 提醒时间
     */
    public void deleteAssignmentReminderInfo(Long assignmentReleaseId, LocalDateTime reminderTime) {
        // 删除提醒信息
        String infoKey = ASSIGNMENT_REMINDER_INFO + assignmentReleaseId;
        stringRedisTemplate.delete(infoKey);
        
        // 从提醒时间集合中移除
        String timeKey = ASSIGNMENT_REMINDER_TIME + reminderTime.format(DateTimeFormatter.ofPattern("yyyyMMddHH"));
        stringRedisTemplate.opsForSet().remove(timeKey, assignmentReleaseId.toString());
    }

    /**
     * 格式化缓存键
     *
     * @param assignmentReleaseId 作业发布ID
     * @param userId 用户ID
     * @return 缓存键
     */
    private String formatKey(Long assignmentReleaseId, Long userId) {
        return ASSIGNMENT_REMINDER_SENT + assignmentReleaseId + ":" + userId;
    }

}