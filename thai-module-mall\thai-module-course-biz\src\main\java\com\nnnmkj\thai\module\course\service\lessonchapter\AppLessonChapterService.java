package com.nnnmkj.thai.module.course.service.lessonchapter;

import com.nnnmkj.thai.framework.common.pojo.PageResult;
import com.nnnmkj.thai.module.course.controller.app.lessonchapter.vo.AppLessonChapterPageReqVO;
import com.nnnmkj.thai.module.course.controller.app.lessonchapter.vo.AppLessonChapterRespVO;
import com.nnnmkj.thai.module.course.controller.app.lessonchapter.vo.AppLessonChapterSaveReqVO;
import com.nnnmkj.thai.module.course.dal.dataobject.lessonchapter.LessonChapterDO;
import com.nnnmkj.thai.module.course.dal.dataobject.lessonchapter.LessonChatperAttachmentDO;
import jakarta.validation.Valid;

import java.util.Collection;
import java.util.List;

/**
 * 课程章节 Service 接口
 *
 * <AUTHOR>
 */
public interface AppLessonChapterService {

    /**
     * 创建课程章节
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createLessonChapter(@Valid AppLessonChapterSaveReqVO createReqVO);

    /**
     * 更新课程章节
     *
     * @param updateReqVO 更新信息
     */
    void updateLessonChapter(@Valid AppLessonChapterSaveReqVO updateReqVO);

    /**
     * 删除课程章节
     *
     * @param id 编号
     */
    void deleteLessonChapter(Long id);

    /**
     * 获得课程章节
     *
     * @param id 编号
     * @return 课程章节
     */
    LessonChapterDO getLessonChapter(Long id);
    
    /**
     * 获取课程章节详情，包含相关信息
     *
     * @param id 编号
     * @return 课程章节详情
     */
    AppLessonChapterRespVO getLessonChapterWithRelatedInfo(Long id);

    /**
     * 获得课程章节分页
     *
     * @param pageReqVO 分页查询
     * @return 课程章节分页
     */
    PageResult<LessonChapterDO> getLessonChapterPage(AppLessonChapterPageReqVO pageReqVO);
    
    /**
     * 获得课程章节分页，包含相关信息
     *
     * @param pageReqVO 分页查询
     * @return 课程章节分页
     */
    PageResult<AppLessonChapterRespVO> getLessonChapterPageWithRelatedInfo(AppLessonChapterPageReqVO pageReqVO);

    /**
     * 获得课程章节列表
     *
     * @param ids 学习集的数组
     * @return 学习集列表
     */
    List<LessonChapterDO> getLessonChapterList(Collection<Long> ids);
    
    /**
     * 获取课程章节列表用于导出
     *
     * @param pageReqVO 查询条件
     * @return 课程章节列表
     */
    List<AppLessonChapterRespVO> getLessonChapterListForExport(AppLessonChapterPageReqVO pageReqVO);

    // ==================== 子表（课程章节附件） ====================

    /**
     * 获得课程章节附件列表
     *
     * @param chapterId 章节ID
     * @return 课程章节附件列表
     */
    List<LessonChatperAttachmentDO> getLessonChatperAttachmentListByChapterId(Long chapterId);

    /**
     * 根据课程ID获取课程章节列表
     *
     * @param courseId 课程ID
     * @return 课程章节列表
     */
    List<LessonChapterDO> getLessonChapterListByCourseId(Long courseId);
    
    /**
     * 根据课程ID获取课程章节列表，包含完成状态
     *
     * @param courseId 课程ID
     * @return 课程章节列表
     */
    List<AppLessonChapterRespVO> getLessonChapterListByCourseIdWithStatus(Long courseId);

    /**
     * 获得顶级课程章节分页（父章节ID为null或为0）
     *
     * @param pageReqVO 分页查询
     * @return 课程章节分页
     */
    PageResult<LessonChapterDO> getTopLessonChapterPage(AppLessonChapterPageReqVO pageReqVO);
    
    /**
     * 获取顶级章节分页，包含完成状态
     *
     * @param pageReqVO 分页查询
     * @return 顶级章节分页
     */
    PageResult<AppLessonChapterRespVO> getParentLessonChapterPageWithStatus(AppLessonChapterPageReqVO pageReqVO);

    /**
     * 根据父章节ID获取子章节列表
     *
     * @param parentId 父章节ID
     * @return 子章节列表
     */
    List<LessonChapterDO> getLessonChapterListByParentId(Long parentId);
    
    /**
     * 获取子章节列表，包含完成状态
     *
     * @param parentId 父章节ID
     * @return 子章节列表
     */
    List<AppLessonChapterRespVO> getChildLessonChapterListWithStatus(Long parentId);
    
    /**
     * 根据章节名称搜索章节
     * 搜索顶级章节和子章节，如果搜索到子章节则返回其对应的顶级章节
     *
     * @param keyword 搜索关键词
     * @param courseId 课程ID
     * @return 顶级章节列表
     */
    List<AppLessonChapterRespVO> searchLessonChaptersByKeyword(String keyword, Long courseId);

}