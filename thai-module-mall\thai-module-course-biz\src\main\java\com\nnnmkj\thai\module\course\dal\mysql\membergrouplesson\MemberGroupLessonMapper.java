package com.nnnmkj.thai.module.course.dal.mysql.membergrouplesson;

import com.nnnmkj.thai.framework.common.pojo.PageResult;
import com.nnnmkj.thai.framework.mybatis.core.mapper.BaseMapperX;
import com.nnnmkj.thai.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.nnnmkj.thai.module.course.controller.admin.membergrouplesson.vo.MemberGroupLessonPageReqVO;
import com.nnnmkj.thai.module.course.dal.dataobject.membergrouplesson.MemberGroupLessonDO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 班级-课程关联 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface MemberGroupLessonMapper extends BaseMapperX<MemberGroupLessonDO> {

    default PageResult<MemberGroupLessonDO> selectPage(MemberGroupLessonPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<MemberGroupLessonDO>()
                .eqIfPresent(MemberGroupLessonDO::getGroupId, reqVO.getGroupId())
                .eqIfPresent(MemberGroupLessonDO::getCourseId, reqVO.getCourseId())
                .inIfPresent(MemberGroupLessonDO::getCourseId, reqVO.getCourseIds())
                .betweenIfPresent(MemberGroupLessonDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(MemberGroupLessonDO::getId));
    }
    
    /**
     * 根据分组ID查询课程数量
     *
     * @param groupId 分组ID
     * @return 课程数量
     */
    default int selectLessonCountByGroupId(Long groupId) {
        return selectCount(MemberGroupLessonDO::getGroupId, groupId).intValue();
    }


    /**
     * 根据课程ID删除课程——班级关联
     *
     * @param courseId 课程ID
     */
    default void delectByCourseId(Long courseId) {
        delete(MemberGroupLessonDO::getCourseId, courseId);

    }

    /**
     * 根据课程ID和一个或多个班级ID删除特定的课程——班级关联
     *
     * @param courseIdAndGroupIds，课程ID和班级ID集合
     */
    default void delectByCourseIdAndGroupId(Map<Long,Set<Long>>  courseIdAndGroupIds) {
        int count = 0;
        for (Map.Entry<Long, Set<Long>> entry : courseIdAndGroupIds.entrySet()) {
            Long courseId = entry.getKey();
            Set<Long> groupIds = entry.getValue();
            count += delete(new LambdaQueryWrapperX<MemberGroupLessonDO>()
                    .eq(MemberGroupLessonDO::getCourseId, courseId)
                    .in(MemberGroupLessonDO::getGroupId, groupIds));
        }
    }

    /**
     * 根据课程Id获取课程——班级关联
     *
     * @param courseId 课程ID
     * @return 班级-课程关联 DO
     */
    default List<MemberGroupLessonDO> selectByCourseId(Long courseId) {
        return selectList(MemberGroupLessonDO::getCourseId, courseId);

    }

}