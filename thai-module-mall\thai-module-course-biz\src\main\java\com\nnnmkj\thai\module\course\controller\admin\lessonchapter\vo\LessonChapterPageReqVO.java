package com.nnnmkj.thai.module.course.controller.admin.lessonchapter.vo;

import com.nnnmkj.thai.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;
import java.util.Set;

import static com.nnnmkj.thai.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 课程章节分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class LessonChapterPageReqVO extends PageParam {

    @Schema(description = "课程ID", example = "100")
    private Long courseId;

    @Schema(description = "课程ID列表")
    private Set<Long> courseIds;

    @Schema(description = "父章节ID", example = "10086")
    private Long parentId;

    @Schema(description = "章节名称", example = "王五")
    private String name;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}