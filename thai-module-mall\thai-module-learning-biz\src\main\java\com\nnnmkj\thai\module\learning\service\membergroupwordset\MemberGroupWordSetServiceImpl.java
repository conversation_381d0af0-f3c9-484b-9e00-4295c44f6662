package com.nnnmkj.thai.module.learning.service.membergroupwordset;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.nnnmkj.thai.framework.common.pojo.PageResult;
import com.nnnmkj.thai.framework.common.util.object.BeanUtils;
import com.nnnmkj.thai.module.learning.controller.admin.membergroupwordset.vo.MemberGroupWordSetPageReqVO;
import com.nnnmkj.thai.module.learning.controller.admin.membergroupwordset.vo.MemberGroupWordSetSaveReqVO;
import com.nnnmkj.thai.module.learning.dal.dataobject.membergroupwordset.MemberGroupWordSetDO;
import com.nnnmkj.thai.module.learning.dal.dataobject.wordset.WordSetDO;
import com.nnnmkj.thai.module.learning.dal.mysql.membergroupwordset.MemberGroupWordSetMapper;
import com.nnnmkj.thai.module.learning.dal.mysql.wordset.WordSetMapper;
import com.nnnmkj.thai.module.learning.enums.WordSetVisibilityEnum;
import com.nnnmkj.thai.module.member.api.user.MemberUserApi;
import com.nnnmkj.thai.module.system.api.permission.PermissionApi;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

import static com.nnnmkj.thai.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.nnnmkj.thai.module.learning.enums.ErrorCodeConstants.MEMBER_GROUP_WORD_SET_NOT_EXISTS;

/**
 * 班级-学习集关联 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class MemberGroupWordSetServiceImpl implements MemberGroupWordSetService {

    @Resource
    private MemberGroupWordSetMapper memberGroupWordSetMapper;

    @Resource
    private WordSetMapper wordSetMapper;

    @Resource
    private PermissionApi permissionApi;
    
    @Resource
    private MemberUserApi memberUserApi;

    @Override
    public Long createMemberGroupWordSet(MemberGroupWordSetSaveReqVO createReqVO) {
        // 插入
        MemberGroupWordSetDO memberGroupWordSetDO = memberGroupWordSetMapper
                .selectOne(MemberGroupWordSetDO::getWordSetId, createReqVO.getWordSetId(), MemberGroupWordSetDO::getGroupId, createReqVO.getGroupId());
        if (memberGroupWordSetDO != null) {
            memberGroupWordSetDO = BeanUtils.toBean(createReqVO, MemberGroupWordSetDO.class);
            memberGroupWordSetMapper.updateById(memberGroupWordSetDO);
        } else {
            memberGroupWordSetDO = BeanUtils.toBean(createReqVO, MemberGroupWordSetDO.class);
            memberGroupWordSetMapper.insert(memberGroupWordSetDO);
        }
        // 返回
        return memberGroupWordSetDO.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int batchCreateMemberGroupWordSet(List<Long> setIds, Long groupId) {
        if (CollUtil.isEmpty(setIds)) {
            return 0;
        }
        
        // 查询当前班级已存在的学习集关联
        List<MemberGroupWordSetDO> existingRelations = memberGroupWordSetMapper.selectList(
                MemberGroupWordSetDO::getGroupId, groupId);
        
        // 获取已存在的学习集ID集合
        Set<Long> existingSetIds = existingRelations.stream()
                .map(MemberGroupWordSetDO::getWordSetId)
                .collect(Collectors.toSet());
        
        // 过滤掉已存在的学习集ID
        List<Long> newSetIds = setIds.stream()
                .filter(id -> !existingSetIds.contains(id))
                .collect(Collectors.toList());
        
        if (CollUtil.isEmpty(newSetIds)) {
            return 0;
        }
        
        // 批量创建新的关联
        List<MemberGroupWordSetDO> newRelations = new ArrayList<>();
        for (Long setId : newSetIds) {
            MemberGroupWordSetDO relation = new MemberGroupWordSetDO();
            relation.setGroupId(groupId);
            relation.setWordSetId(setId);
            newRelations.add(relation);
        }
        
        // 批量插入
        memberGroupWordSetMapper.insertBatch(newRelations);
        
        return newRelations.size();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int batchDeleteMemberGroupWordSet(List<Long> setIds, Long groupId) {
        if (CollUtil.isEmpty(setIds)) {
            return 0;
        }
        
        // 查询当前班级已存在的学习集关联
        List<MemberGroupWordSetDO> existingRelations = memberGroupWordSetMapper.selectList(
                MemberGroupWordSetDO::getGroupId, groupId);
        
        // 过滤出需要删除的关联记录ID
        List<Long> relationIdsToDelete = existingRelations.stream()
                .filter(relation -> setIds.contains(relation.getWordSetId()))
                .map(MemberGroupWordSetDO::getId)
                .collect(Collectors.toList());
        
        if (CollUtil.isEmpty(relationIdsToDelete)) {
            return 0;
        }
        
        // 批量删除
        memberGroupWordSetMapper.deleteBatchIds(relationIdsToDelete);
        
        return relationIdsToDelete.size();
    }

    @Override
    public void updateMemberGroupWordSet(MemberGroupWordSetSaveReqVO updateReqVO) {
        // 校验存在
        validateMemberGroupWordSetExists(updateReqVO.getId());
        // 更新
        MemberGroupWordSetDO updateObj = BeanUtils.toBean(updateReqVO, MemberGroupWordSetDO.class);
        memberGroupWordSetMapper.updateById(updateObj);
    }

    @Override
    public void deleteMemberGroupWordSet(Long id) {
        // 校验存在
        validateMemberGroupWordSetExists(id);
        // 删除
        memberGroupWordSetMapper.deleteById(id);
    }

    private void validateMemberGroupWordSetExists(Long id) {
        if (memberGroupWordSetMapper.selectById(id) == null) {
            throw exception(MEMBER_GROUP_WORD_SET_NOT_EXISTS);
        }
    }

    @Override
    public MemberGroupWordSetDO getMemberGroupWordSet(Long id) {
        return memberGroupWordSetMapper.selectById(id);
    }

    @Override
    public PageResult<MemberGroupWordSetDO> getMemberGroupWordSetPage(MemberGroupWordSetPageReqVO pageReqVO) {
        return memberGroupWordSetMapper.selectPage(pageReqVO);
    }

    @Override
    public int countByGroupId(Long groupId) {
        return memberGroupWordSetMapper.selectCountByGroupId(groupId);
    }

    @Override
    public int countVisibleByGroupId(Long groupId) {
        // 1. 首先获取该分组下所有的学习集关联
        List<MemberGroupWordSetDO> relations = memberGroupWordSetMapper.selectList(
                new LambdaQueryWrapper<MemberGroupWordSetDO>()
                        .eq(MemberGroupWordSetDO::getGroupId, groupId));
        
        if (CollUtil.isEmpty(relations)) {
            return 0;
        }
        
        // 2. 获取所有学习集ID
        List<Long> wordSetIds = relations.stream()
                .map(MemberGroupWordSetDO::getWordSetId)
                .collect(Collectors.toList());
        
        // 3. 查询这些学习集中非"仅自己可见"的数量
        Long count = wordSetMapper.selectCount(
                new LambdaQueryWrapper<WordSetDO>()
                        .in(WordSetDO::getId, wordSetIds)
                        .ne(WordSetDO::getVisibility, WordSetVisibilityEnum.SELF.getType()));
        
        return count == null ? 0 : count.intValue();
    }

    @Override
    public List<MemberGroupWordSetDO> getMemberGroupWordSetList(Long wordSetId) {
        return memberGroupWordSetMapper.selectList(
                new LambdaQueryWrapper<MemberGroupWordSetDO>()
                        .eq(MemberGroupWordSetDO::getWordSetId, wordSetId));

    }


}