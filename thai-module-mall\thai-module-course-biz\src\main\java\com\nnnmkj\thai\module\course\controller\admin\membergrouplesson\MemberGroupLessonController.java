package com.nnnmkj.thai.module.course.controller.admin.membergrouplesson;

import cn.hutool.core.collection.CollUtil;
import com.nnnmkj.thai.framework.apilog.core.annotation.ApiAccessLog;
import com.nnnmkj.thai.framework.common.pojo.CommonResult;
import com.nnnmkj.thai.framework.common.pojo.PageParam;
import com.nnnmkj.thai.framework.common.pojo.PageResult;
import com.nnnmkj.thai.framework.excel.core.util.ExcelUtils;
import com.nnnmkj.thai.module.course.controller.admin.membergrouplesson.vo.LessonAndAllGroupVO;
import com.nnnmkj.thai.module.course.controller.admin.membergrouplesson.vo.MemberGroupLessonPageReqVO;
import com.nnnmkj.thai.module.course.controller.admin.membergrouplesson.vo.MemberGroupLessonRespVO;
import com.nnnmkj.thai.module.course.controller.admin.membergrouplesson.vo.MemberGroupLessonSaveReqVO;
import com.nnnmkj.thai.module.course.convert.membergrouplesson.MembergrouplessonConvert;
import com.nnnmkj.thai.module.course.dal.dataobject.lesson.LessonDO;
import com.nnnmkj.thai.module.course.dal.dataobject.membergrouplesson.MemberGroupLessonDO;
import com.nnnmkj.thai.module.course.service.lesson.LessonService;
import com.nnnmkj.thai.module.course.service.membergrouplesson.MemberGroupLessonService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import org.apache.ibatis.annotations.Update;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.util.Collections;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

import static com.nnnmkj.thai.framework.apilog.core.enums.OperateTypeEnum.EXPORT;
import static com.nnnmkj.thai.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 班级-课程关联")
@RestController
@RequestMapping("/course/member-group-lesson")
@Validated
public class MemberGroupLessonController {

    @Resource
    private MemberGroupLessonService memberGroupLessonService;
    
    @Resource
    private LessonService lessonService;
    
    @PostMapping("/create")
    @Operation(summary = "创建班级-课程关联")
    @PreAuthorize("@ss.hasPermission('course:member-group-lesson:create')")
    public CommonResult<Long> createMemberGroupLesson(@Valid @RequestBody MemberGroupLessonSaveReqVO createReqVO) {
        return success(memberGroupLessonService.createMemberGroupLesson(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新班级-课程关联")
    @PreAuthorize("@ss.hasPermission('course:member-group-lesson:update')")
    public CommonResult<Boolean> updateMemberGroupLesson(@Valid @RequestBody MemberGroupLessonSaveReqVO updateReqVO) {
        memberGroupLessonService.updateMemberGroupLesson(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除班级-课程关联")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('course:member-group-lesson:delete')")
    public CommonResult<Boolean> deleteMemberGroupLesson(@RequestParam("id") Long id) {
        memberGroupLessonService.deleteMemberGroupLesson(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得班级-课程关联")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('course:member-group-lesson:query')")
    public CommonResult<MemberGroupLessonRespVO> getMemberGroupLesson(@RequestParam("id") Long id) {
        MemberGroupLessonDO memberGroupLesson = memberGroupLessonService.getMemberGroupLesson(id);
        if (memberGroupLesson == null) {
            return success(null);
        }
        // 处理课程返显
        LessonDO lesson = lessonService.getLesson(memberGroupLesson.getCourseId());
        return success(MembergrouplessonConvert.INSTANCE.convert(memberGroupLesson, lesson));
    }

    @GetMapping("/page")
    @Operation(summary = "获得班级-课程关联分页")
    @PreAuthorize("@ss.hasPermission('course:member-group-lesson:query')")
    public CommonResult<PageResult<MemberGroupLessonRespVO>> getMemberGroupLessonPage(@Valid MemberGroupLessonPageReqVO pageReqVO) {
        PageResult<MemberGroupLessonDO> pageResult = memberGroupLessonService.getMemberGroupLessonPage(pageReqVO);

        if (CollUtil.isEmpty(pageResult.getList())) {
            return success(PageResult.empty());
        }
        // 处理课程返显
        Set<Long> courseIds = pageResult.getList().stream().map(MemberGroupLessonDO::getCourseId).collect(Collectors.toSet());
        List<LessonDO> lessonList = lessonService.getLessonList(courseIds);
        return success(MembergrouplessonConvert.INSTANCE.convertPage(pageResult, lessonList));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出班级-课程关联 Excel")
    @PreAuthorize("@ss.hasPermission('course:member-group-lesson:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportMemberGroupLessonExcel(@Valid MemberGroupLessonPageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        // 获取数据
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        PageResult<MemberGroupLessonDO> pageResult = memberGroupLessonService.getMemberGroupLessonPage(pageReqVO);
        List<MemberGroupLessonDO> list = pageResult.getList();
        
        if (CollUtil.isEmpty(list)) {
            // 导出空 Excel
            ExcelUtils.write(response, "班级-课程关联.xls", "数据", MemberGroupLessonRespVO.class, Collections.emptyList());
            return;
        }
        
        // 处理课程返显
        Set<Long> courseIds = list.stream().map(MemberGroupLessonDO::getCourseId).collect(Collectors.toSet());
        List<LessonDO> lessonList = lessonService.getLessonList(courseIds);
        List<MemberGroupLessonRespVO> respVOList = MembergrouplessonConvert.INSTANCE.convertList(list, lessonList);
        // 导出 Excel
        ExcelUtils.write(response, "班级-课程关联.xls", "数据", MemberGroupLessonRespVO.class, respVOList);
    }

    @PutMapping("/updateAllMemberGroupLesson")
    @Operation(summary = "批量更改班级-课程关联")
    @Parameter(name = "courseId", description = "课程编号", required = true)
    @PreAuthorize("@ss.hasPermission('course:member-group-lesson:update')")
    public CommonResult<Boolean> updateAllMemberGroupLesson(@Valid LessonAndAllGroupVO reqVO) {
        memberGroupLessonService.updateAllMemberGroupLesson(reqVO);
        return success(true);
    }
}