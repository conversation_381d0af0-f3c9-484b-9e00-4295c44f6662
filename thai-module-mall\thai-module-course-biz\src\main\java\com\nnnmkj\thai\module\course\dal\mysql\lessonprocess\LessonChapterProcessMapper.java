package com.nnnmkj.thai.module.course.dal.mysql.lessonprocess;

import com.nnnmkj.thai.framework.mybatis.core.mapper.BaseMapperX;
import com.nnnmkj.thai.module.course.dal.dataobject.lessonprocess.LessonChapterProcessDO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 课程章节进度 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface LessonChapterProcessMapper extends BaseMapperX<LessonChapterProcessDO> {

    default List<LessonChapterProcessDO> selectListByCourseId(Long courseId) {
        return selectList(LessonChapterProcessDO::getCourseId, courseId);
    }

    default void deleteByCourseId(Long courseId) {
        delete(LessonChapterProcessDO::getCourseId, courseId);
    }

}