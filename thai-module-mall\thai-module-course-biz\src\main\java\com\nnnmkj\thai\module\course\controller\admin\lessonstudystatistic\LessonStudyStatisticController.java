package com.nnnmkj.thai.module.course.controller.admin.lessonstudystatistic;

import com.nnnmkj.thai.framework.apilog.core.annotation.ApiAccessLog;
import com.nnnmkj.thai.framework.common.pojo.CommonResult;
import com.nnnmkj.thai.framework.common.pojo.PageParam;
import com.nnnmkj.thai.framework.common.pojo.PageResult;
import com.nnnmkj.thai.framework.common.util.object.BeanUtils;
import com.nnnmkj.thai.framework.excel.core.util.ExcelUtils;
import com.nnnmkj.thai.module.course.controller.admin.lessonstudystatistic.vo.LessonStudyStatisticPageReqVO;
import com.nnnmkj.thai.module.course.controller.admin.lessonstudystatistic.vo.LessonStudyStatisticRespVO;
import com.nnnmkj.thai.module.course.controller.admin.lessonstudystatistic.vo.LessonStudyStatisticSaveReqVO;
import com.nnnmkj.thai.module.course.dal.dataobject.lessonstudystatistic.LessonStudyStatisticDO;
import com.nnnmkj.thai.module.course.service.lessonstudystatistic.LessonStudyStatisticService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.util.List;

import static com.nnnmkj.thai.framework.apilog.core.enums.OperateTypeEnum.EXPORT;
import static com.nnnmkj.thai.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 课程学习统计")
@RestController
@RequestMapping("/course/lesson-study-statistic")
@Validated
public class LessonStudyStatisticController {

    @Resource
    private LessonStudyStatisticService lessonStudyStatisticService;

    @PostMapping("/create")
    @Operation(summary = "创建课程学习统计")
    @PreAuthorize("@ss.hasPermission('course:lesson-study-statistic:create')")
    public CommonResult<Long> createLessonStudyStatistic(@Valid @RequestBody LessonStudyStatisticSaveReqVO createReqVO) {
        return success(lessonStudyStatisticService.createLessonStudyStatistic(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新课程学习统计")
    @PreAuthorize("@ss.hasPermission('course:lesson-study-statistic:update')")
    public CommonResult<Boolean> updateLessonStudyStatistic(@Valid @RequestBody LessonStudyStatisticSaveReqVO updateReqVO) {
        lessonStudyStatisticService.updateLessonStudyStatistic(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除课程学习统计")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('course:lesson-study-statistic:delete')")
    public CommonResult<Boolean> deleteLessonStudyStatistic(@RequestParam("id") Long id) {
        lessonStudyStatisticService.deleteLessonStudyStatistic(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得课程学习统计")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('course:lesson-study-statistic:query')")
    public CommonResult<LessonStudyStatisticRespVO> getLessonStudyStatistic(@RequestParam("id") Long id) {
        LessonStudyStatisticDO lessonStudyStatistic = lessonStudyStatisticService.getLessonStudyStatistic(id);
        return success(BeanUtils.toBean(lessonStudyStatistic, LessonStudyStatisticRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得课程学习统计分页")
    @PreAuthorize("@ss.hasPermission('course:lesson-study-statistic:query')")
    public CommonResult<PageResult<LessonStudyStatisticRespVO>> getLessonStudyStatisticPage(@Valid LessonStudyStatisticPageReqVO pageReqVO) {
        PageResult<LessonStudyStatisticDO> pageResult = lessonStudyStatisticService.getLessonStudyStatisticPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, LessonStudyStatisticRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出课程学习统计 Excel")
    @PreAuthorize("@ss.hasPermission('course:lesson-study-statistic:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportLessonStudyStatisticExcel(@Valid LessonStudyStatisticPageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<LessonStudyStatisticDO> list = lessonStudyStatisticService.getLessonStudyStatisticPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "课程学习统计.xls", "数据", LessonStudyStatisticRespVO.class,
                        BeanUtils.toBean(list, LessonStudyStatisticRespVO.class));
    }

}