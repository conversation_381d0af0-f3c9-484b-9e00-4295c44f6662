package com.nnnmkj.thai.module.course.controller.admin.lessonstudystatistic.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

@Schema(description = "管理后台 - 课程学习统计新增/修改 Request VO")
@Data
public class LessonStudyStatisticSaveReqVO {

    @Schema(description = "编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "4693")
    private Long id;

    @Schema(description = "课程编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "28731")
    @NotNull(message = "课程编号不能为空")
    private Long courseLessonId;

    @Schema(description = "学习次数", requiredMode = Schema.RequiredMode.REQUIRED, example = "21837")
    @NotNull(message = "学习次数不能为空")
    private Integer count;

}