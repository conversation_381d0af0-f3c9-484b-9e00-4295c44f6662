package com.nnnmkj.thai.module.course.controller.admin.membergrouplesson.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;
@Schema(description = "管理后台 - 课程-所有关联班级 Request VO")
@Data
public class LessonAndAllGroupVO {
    @Schema(description = "编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "7")
    @ExcelProperty("编号")
    private Long id;

    @Schema(description = "分组编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "8")
    @ExcelProperty("分组编号集合")
    private List<Long> groupIds;

    @Schema(description = "课程编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "9527")
    @ExcelProperty("课程编号")
    private Long courseId;
}
