package com.nnnmkj.thai.module.course.controller.admin.assignmentquestion;

import com.nnnmkj.thai.framework.apilog.core.annotation.ApiAccessLog;
import com.nnnmkj.thai.framework.common.pojo.CommonResult;
import com.nnnmkj.thai.framework.common.pojo.PageParam;
import com.nnnmkj.thai.framework.common.pojo.PageResult;
import com.nnnmkj.thai.framework.common.util.object.BeanUtils;
import com.nnnmkj.thai.framework.excel.core.util.ExcelUtils;
import com.nnnmkj.thai.module.course.controller.admin.assignmentquestion.vo.AssignmentQuestionCreateFromBankReqVO;
import com.nnnmkj.thai.module.course.controller.admin.assignmentquestion.vo.AssignmentQuestionPageReqVO;
import com.nnnmkj.thai.module.course.controller.admin.assignmentquestion.vo.AssignmentQuestionRespVO;
import com.nnnmkj.thai.module.course.controller.admin.assignmentquestion.vo.AssignmentQuestionSaveReqVO;
import com.nnnmkj.thai.module.course.dal.dataobject.assignmentquestion.AssignmentQuestionDO;
import com.nnnmkj.thai.module.course.dal.dataobject.assignmentquestion.AssignmentQuestionOptionDO;
import com.nnnmkj.thai.module.course.service.assignmentquestion.AssignmentQuestionService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.util.List;

import static com.nnnmkj.thai.framework.apilog.core.enums.OperateTypeEnum.EXPORT;
import static com.nnnmkj.thai.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 课程作业题目")
@RestController
@RequestMapping("/course/assignment-question")
@Validated
public class AssignmentQuestionController {

    @Resource
    private AssignmentQuestionService assignmentQuestionService;

    @PostMapping("/create/from-bank")
    public CommonResult<Boolean> createQuestionFromBank(@Valid @RequestBody AssignmentQuestionCreateFromBankReqVO createReqVO) {
        assignmentQuestionService.createQuestionFromBank(createReqVO);
        return success(true);
    }

    @PostMapping("/create")
    @Operation(summary = "创建课程作业题目")
    @PreAuthorize("@ss.hasPermission('course:assignment-question:create')")
    public CommonResult<Long> createAssignmentQuestion(@Valid @RequestBody AssignmentQuestionSaveReqVO createReqVO) {
        return success(assignmentQuestionService.createAssignmentQuestion(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新课程作业题目")
    @PreAuthorize("@ss.hasPermission('course:assignment-question:update')")
    public CommonResult<Boolean> updateAssignmentQuestion(@Valid @RequestBody AssignmentQuestionSaveReqVO updateReqVO) {
        assignmentQuestionService.updateAssignmentQuestion(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除课程作业题目")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('course:assignment-question:delete')")
    public CommonResult<Boolean> deleteAssignmentQuestion(@RequestParam("id") Long id) {
        assignmentQuestionService.deleteAssignmentQuestion(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得课程作业题目")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('course:assignment-question:query')")
    public CommonResult<AssignmentQuestionRespVO> getAssignmentQuestion(@RequestParam("id") Long id) {
        AssignmentQuestionDO assignmentQuestion = assignmentQuestionService.getAssignmentQuestion(id);
        List<AssignmentQuestionOptionDO> options = assignmentQuestionService.getAssignmentQuestionOptionListByQuestionId(id);
        AssignmentQuestionRespVO bean = BeanUtils.toBean(assignmentQuestion, AssignmentQuestionRespVO.class);
        bean.setOptionList(options);
        return success(bean);
    }

    @GetMapping("/page")
    @Operation(summary = "获得课程作业题目分页")
    @PreAuthorize("@ss.hasPermission('course:assignment-question:query')")
    public CommonResult<PageResult<AssignmentQuestionRespVO>> getAssignmentQuestionPage(@Valid AssignmentQuestionPageReqVO pageReqVO) {
        PageResult<AssignmentQuestionDO> pageResult = assignmentQuestionService.getAssignmentQuestionPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, AssignmentQuestionRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出课程作业题目 Excel")
    @PreAuthorize("@ss.hasPermission('course:assignment-question:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportAssignmentQuestionExcel(@Valid AssignmentQuestionPageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<AssignmentQuestionDO> list = assignmentQuestionService.getAssignmentQuestionPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "课程作业题目.xls", "数据", AssignmentQuestionRespVO.class,
                        BeanUtils.toBean(list, AssignmentQuestionRespVO.class));
    }

    // ==================== 子表（课程作业题目选项） ====================

    @GetMapping("/assignment-question-option/list-by-question-id")
    @Operation(summary = "获得课程作业题目选项列表")
    @Parameter(name = "questionId", description = "题目ID")
    @PreAuthorize("@ss.hasPermission('course:assignment-question:query')")
    public CommonResult<List<AssignmentQuestionOptionDO>> getAssignmentQuestionOptionListByQuestionId(@RequestParam("questionId") Long questionId) {
        return success(assignmentQuestionService.getAssignmentQuestionOptionListByQuestionId(questionId));
    }

}