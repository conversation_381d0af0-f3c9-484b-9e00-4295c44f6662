package com.nnnmkj.thai.module.course.controller.admin.assignmentreleasequestion;

import com.nnnmkj.thai.framework.apilog.core.annotation.ApiAccessLog;
import com.nnnmkj.thai.framework.common.pojo.CommonResult;
import com.nnnmkj.thai.framework.common.pojo.PageParam;
import com.nnnmkj.thai.framework.common.pojo.PageResult;
import com.nnnmkj.thai.framework.common.util.object.BeanUtils;
import com.nnnmkj.thai.framework.excel.core.util.ExcelUtils;
import com.nnnmkj.thai.module.course.controller.admin.assignmentreleasequestion.vo.AssignmentReleaseQuestionPageReqVO;
import com.nnnmkj.thai.module.course.controller.admin.assignmentreleasequestion.vo.AssignmentReleaseQuestionRespVO;
import com.nnnmkj.thai.module.course.controller.admin.assignmentreleasequestion.vo.AssignmentReleaseQuestionSaveReqVO;
import com.nnnmkj.thai.module.course.dal.dataobject.assignmentreleasequestion.AssignmentReleaseQuestionDO;
import com.nnnmkj.thai.module.course.service.assignmentreleasequestion.AssignmentReleaseQuestionService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.util.List;

import static com.nnnmkj.thai.framework.apilog.core.enums.OperateTypeEnum.EXPORT;
import static com.nnnmkj.thai.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 课程作业发布题目")
@RestController
@RequestMapping("/course/assignment-release-question")
@Validated
public class AssignmentReleaseQuestionController {

    @Resource
    private AssignmentReleaseQuestionService assignmentReleaseQuestionService;

    @PostMapping("/create")
    @Operation(summary = "创建课程作业发布题目")
    @PreAuthorize("@ss.hasPermission('course:assignment-release-question:create')")
    public CommonResult<Long> createAssignmentReleaseQuestion(@Valid @RequestBody AssignmentReleaseQuestionSaveReqVO createReqVO) {
        return success(assignmentReleaseQuestionService.createAssignmentReleaseQuestion(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新课程作业发布题目")
    @PreAuthorize("@ss.hasPermission('course:assignment-release-question:update')")
    public CommonResult<Boolean> updateAssignmentReleaseQuestion(@Valid @RequestBody AssignmentReleaseQuestionSaveReqVO updateReqVO) {
        assignmentReleaseQuestionService.updateAssignmentReleaseQuestion(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除课程作业发布题目")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('course:assignment-release-question:delete')")
    public CommonResult<Boolean> deleteAssignmentReleaseQuestion(@RequestParam("id") Long id) {
        assignmentReleaseQuestionService.deleteAssignmentReleaseQuestion(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得课程作业发布题目")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('course:assignment-release-question:query')")
    public CommonResult<AssignmentReleaseQuestionRespVO> getAssignmentReleaseQuestion(@RequestParam("id") Long id) {
        AssignmentReleaseQuestionDO assignmentReleaseQuestion = assignmentReleaseQuestionService.getAssignmentReleaseQuestion(id);
        return success(BeanUtils.toBean(assignmentReleaseQuestion, AssignmentReleaseQuestionRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得课程作业发布题目分页")
    @PreAuthorize("@ss.hasPermission('course:assignment-release-question:query')")
    public CommonResult<PageResult<AssignmentReleaseQuestionRespVO>> getAssignmentReleaseQuestionPage(@Valid AssignmentReleaseQuestionPageReqVO pageReqVO) {
        PageResult<AssignmentReleaseQuestionDO> pageResult = assignmentReleaseQuestionService.getAssignmentReleaseQuestionPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, AssignmentReleaseQuestionRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出课程作业发布题目 Excel")
    @PreAuthorize("@ss.hasPermission('course:assignment-release-question:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportAssignmentReleaseQuestionExcel(@Valid AssignmentReleaseQuestionPageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<AssignmentReleaseQuestionDO> list = assignmentReleaseQuestionService.getAssignmentReleaseQuestionPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "课程作业发布题目.xls", "数据", AssignmentReleaseQuestionRespVO.class,
                        BeanUtils.toBean(list, AssignmentReleaseQuestionRespVO.class));
    }

}