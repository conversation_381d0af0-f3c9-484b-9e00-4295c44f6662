package com.nnnmkj.thai.module.course.controller.admin.lessonclickstatistic.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Schema(description = "管理后台 - 课程点击量统计 Response VO")
@Data
@ExcelIgnoreUnannotated
public class LessonClickStatisticRespVO {

    @Schema(description = "编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "2197")
    @ExcelProperty("编号")
    private Long id;

    @Schema(description = "课程编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "16385")
    @ExcelProperty("课程编号")
    private Long courseLessonId;

    @Schema(description = "点击次数", requiredMode = Schema.RequiredMode.REQUIRED, example = "12645")
    @ExcelProperty("点击次数")
    private Integer count;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}