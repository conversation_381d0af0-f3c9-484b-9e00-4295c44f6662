package com.nnnmkj.thai.module.course.service.lesson;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.excel.util.StringUtils;
import com.fasterxml.jackson.core.type.TypeReference;
import com.nnnmkj.thai.framework.common.pojo.PageResult;
import com.nnnmkj.thai.framework.common.util.common.CommonUtils;
import com.nnnmkj.thai.framework.common.util.json.JsonUtils;
import com.nnnmkj.thai.framework.common.util.object.BeanUtils;
import com.nnnmkj.thai.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.nnnmkj.thai.framework.security.core.util.SecurityFrameworkUtils;
import com.nnnmkj.thai.module.course.controller.app.lesson.vo.AppLessonPageReqVO;
import com.nnnmkj.thai.module.course.controller.app.lesson.vo.AppLessonRespVO;
import com.nnnmkj.thai.module.course.dal.dataobject.lesson.LessonDTO;
import com.nnnmkj.thai.module.course.dal.dataobject.lessonclickstatistic.LessonClickStatisticDO;
import com.nnnmkj.thai.module.course.dal.dataobject.lessoncollection.LessonCollectionDO;
import com.nnnmkj.thai.module.course.dal.mysql.lesson.LessonMapperX;
import com.nnnmkj.thai.module.course.dal.mysql.lessonclickstatistic.LessonClickStatisticMapper;
import com.nnnmkj.thai.module.course.dal.mysql.lessoncollection.LessonCollectionMapper;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 优质课程 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
@Slf4j
public class AppQualityLessonServiceImpl implements AppQualityLessonService {
    // 缓存相关常量
    private static final String CACHE_KEY_QUALITY_LESSON = "course:lesson:quality";
    private static final String CACHE_KEY_USER_VIEW_PREFIX = "course:lesson:user_view:";
    private static final String REDIS_KEY_VIEW_COUNT = "course:lesson:view_count";
    private static final String REDIS_KEY_SYNC_LOCK = "course:lesson:view_count_sync_lock";
    private static final String REDIS_KEY_LAST_SYNC_TIME = "course:lesson:view_count_last_sync_time";

    // 时间相关常量
    private static final long CACHE_EXPIRE_TIME = 15 * 60; // 15分钟缓存过期时间
    private static final long VIEW_LOCK_TIME = 10 * 60; // 10分钟内重复点击不计数
    private static final long SYNC_INTERVAL = 10 * 60 * 1000; // 10分钟同步间隔
    private static final long SYNC_LOCK_TIME = 30; // 同步锁过期时间（30秒）

    @Resource
    private StringRedisTemplate stringRedisTemplate;

    @Resource
    private LessonCollectionMapper lessonCollectionMapper;

    @Resource
    private LessonMapperX lessonMapperX;

    @Resource
    private LessonClickStatisticMapper lessonClickStatisticMapper;

    @Override
    public PageResult<AppLessonRespVO> getQualityLessons(AppLessonPageReqVO pageReqVO) {
        // 构建缓存key，包含排序参数和标题
        String cacheKey = String.format("%s:%d:%d:%s", CACHE_KEY_QUALITY_LESSON,
                pageReqVO.getPageNo(), pageReqVO.getPageSize(),
                pageReqVO.getTitle() != null ? pageReqVO.getTitle() : "");
        PageResult<AppLessonRespVO> result = getRedisResult(pageReqVO, cacheKey);
        if (result != null) {
            return result;
        }
        // 缓存中没有数据，从数据库查询
        PageResult<LessonDTO> pageResult = lessonMapperX.selectQualityLessons(pageReqVO);
        if (CollUtil.isEmpty(pageResult.getList())) {
            return PageResult.empty();
        }
        // 转换为响应对象
        result = BeanUtils.toBean(pageResult, AppLessonRespVO.class);
        // 将结果缓存到Redis，设置过期时间
        try {
            // 使用 JsonUtils 将 PageResult 序列化为 JSON 字符串
            String jsonData = JsonUtils.toJsonString(result);
            stringRedisTemplate.opsForValue().set(cacheKey, jsonData, CACHE_EXPIRE_TIME, TimeUnit.SECONDS);
            log.info("[优质课程] 将数据缓存到Redis，页码：{}，每页大小：{}，总数：{}",
                    pageReqVO.getPageNo(), pageReqVO.getPageSize(), result.getTotal());
        } catch (Exception e) {
            log.error("[优质课程] 缓存数据序列化失败", e);
        }
        return result;
    }

    /**
     * 从缓存中获取数据
     * @param pageReqVO 请求参数
     * @param cacheKey 缓存key
     * @return PageResult<AppLessonRespVO>
     */
    private PageResult<AppLessonRespVO> getRedisResult(AppLessonPageReqVO pageReqVO, String cacheKey) {
        // 获取当前登录用户ID
        Long userId = SecurityFrameworkUtils.getLoginUserId();
        String cachedData = stringRedisTemplate.opsForValue().get(cacheKey);
        if (StringUtils.isBlank(cachedData)) {
            return null;
        }
        // 如果缓存中有数据，直接返回
        try {
            // 使用 JsonUtils 将 JSON 字符串反序列化为 PageResult
            PageResult<AppLessonRespVO> cachedResult = JsonUtils.parseObject(cachedData,
                    new TypeReference<>() {
                    });
            log.info("[优质课程] 从缓存中获取数据，页码：{}，每页大小：{}，总数：{}",
                    pageReqVO.getPageNo(), pageReqVO.getPageSize(), cachedResult.getTotal());

            // 如果用户已登录，需要处理收藏状态
            if (userId != null && !cachedResult.getList().isEmpty()) {
                updateCollectionStatus(cachedResult.getList(), userId, pageReqVO.getUserType());
            }

            return cachedResult;
        } catch (Exception e) {
            // 反序列化失败，删除缓存并重新查询
            log.error("[优质课程] 缓存数据反序列化失败", e);
            stringRedisTemplate.delete(cacheKey);
        }
        return null;
    }

    /**
     * 更新课程列表的收藏状态
     *
     * @param list   课程列表
     * @param userId 用户ID
     */
    private void updateCollectionStatus(List<AppLessonRespVO> list, Long userId, Integer userType) {
        if (CollUtil.isEmpty(list) || userId == null) {
            return;
        }
        // 获取课程ID列表
        Set<Long> lessonIds = list.stream().map(AppLessonRespVO::getId).collect(Collectors.toSet());
        // 查询用户收藏的课程
        String creatorQuery = CommonUtils.getCreatorQueryRequired(userId, userType);
        List<LessonCollectionDO> collections = lessonCollectionMapper.selectList(new LambdaQueryWrapperX<LessonCollectionDO>()
                .in(LessonCollectionDO::getLessonId, lessonIds)
                .like(LessonCollectionDO::getLessonId, creatorQuery));
        Set<Long> collectedSetIds = collections.stream().map(LessonCollectionDO::getLessonId).collect(Collectors.toSet());

        // 更新收藏状态
        for (AppLessonRespVO appLessonRespVO : list) {
            boolean isCollected = collectedSetIds.contains(appLessonRespVO.getId());
            appLessonRespVO.setIsStore(isCollected);
        }
    }

    @Override
    public void incrementViewCount(Long lessonId, Long userId) {

        if (lessonId == null || userId == null) {
            log.warn("[增加点击量] 参数无效，setId={}, userId={}", lessonId, userId);
            return;
        }

        // 防刷机制：检查用户是否在短时间内已经点击过该课程
        String viewLockKey = CACHE_KEY_USER_VIEW_PREFIX + userId + ":" + lessonId;
        Boolean isFirstSet = stringRedisTemplate.opsForValue().setIfAbsent(viewLockKey, "1", VIEW_LOCK_TIME, TimeUnit.SECONDS);

        // 如果已经点击过，则不增加点击量
        if (Boolean.FALSE.equals(isFirstSet)) {
            log.info("[增加点击量] 用户{}10分钟内重复点击课程{}，不计数", userId, lessonId);
            return;
        }

        // 在Redis中累积点击量，使用Hash结构存储课程的点击量
        stringRedisTemplate.opsForHash().increment(REDIS_KEY_VIEW_COUNT, lessonId.toString(), 1);
        log.info("[增加点击量] 课程{}点击量在缓存中增加1", lessonId);

        // 检查是否需要将缓存中的点击量同步到数据库
        // 使用分布式锁确保只有一个实例执行同步操作
        Boolean gotLock = stringRedisTemplate.opsForValue().setIfAbsent(REDIS_KEY_SYNC_LOCK, "1", SYNC_LOCK_TIME, TimeUnit.SECONDS);

        if (Boolean.TRUE.equals(gotLock)) {
            try {
                // 获取当前时间戳
                String lastSyncTimeStr = stringRedisTemplate.opsForValue().get(REDIS_KEY_LAST_SYNC_TIME);
                long currentTime = System.currentTimeMillis();
                long lastSyncTime = StringUtils.isNotBlank(lastSyncTimeStr) ? Long.parseLong(lastSyncTimeStr) : 0L;

                // 如果距离上次同步已经过了10分钟，则执行同步
                if (currentTime - lastSyncTime >= SYNC_INTERVAL) {
                    syncViewCountsToDatabase();

                    // 更新最后同步时间
                    stringRedisTemplate.opsForValue().set(REDIS_KEY_LAST_SYNC_TIME, String.valueOf(currentTime));
                }
            } finally {
                // 释放锁
                stringRedisTemplate.delete(REDIS_KEY_SYNC_LOCK);
            }
        }
    }

    /**
     * 将缓存中的点击量同步到数据库
     */
    public void syncViewCountsToDatabase() {
        // 获取所有课程的缓存点击量
        Map<Object, Object> allViewCounts = stringRedisTemplate.opsForHash().entries(REDIS_KEY_VIEW_COUNT);

        if (allViewCounts.isEmpty()) {
            log.info("[同步点击量] 缓存中没有需要同步的点击量数据");
            return;
        }

        log.info("[同步点击量] 开始同步缓存点击量到数据库，共{}个课程", allViewCounts.size());

        // 准备批量更新的课程列表
        List<LessonClickStatisticDO> statisticToUpdate = new ArrayList<>();
        // 首先获取所有需要更新的课程ID
        Set<Long> lessonIds = allViewCounts.keySet().stream().map(key -> Long.valueOf(key.toString())).collect(Collectors.toSet());

        // 批量查询这些课程
        if (!lessonIds.isEmpty()) {
            LambdaQueryWrapperX<LessonClickStatisticDO> wrapperX = new LambdaQueryWrapperX<LessonClickStatisticDO>()
                    .in(LessonClickStatisticDO::getId, lessonIds);
            // 查询已经存在的点击统计
            List<LessonClickStatisticDO> statisticDOS = lessonClickStatisticMapper.selectList(wrapperX);
            // 已存在的点击统计
            Set<Long> existLessonIds = statisticDOS.stream().map(LessonClickStatisticDO::getCourseLessonId).collect(Collectors.toSet());
            // 需要插入的点击统计
            Set<Long> needInsertLessonIds = lessonIds.stream().filter(lessonId -> !existLessonIds.contains(lessonId)).collect(Collectors.toSet());
            // 遍历所有课程的点击量，更新到数据库
            for (Map.Entry<Object, Object> entry : allViewCounts.entrySet()) {
                try {
                    Long lessonId = Long.valueOf(entry.getKey().toString());
                    int increment = Integer.parseInt(entry.getValue().toString());
                    if (existLessonIds.contains(lessonId)) {
                        log.info("[同步点击量] 课程{}已存在点击统计，更新点击量", lessonId);
                        LessonClickStatisticDO lessonClickStatisticDO = statisticDOS.stream()
                                .filter(statisticDO -> statisticDO.getCourseLessonId().equals(lessonId))
                                .findFirst()
                                .orElse(null);
                        if (lessonClickStatisticDO == null) {
                            log.warn("[同步点击量] 课程{}的统计记录不存在，但需要更新", lessonId);
                            continue; // 如果找不到对应的记录，跳过本次循环
                        }
                        Integer viewCount = lessonClickStatisticDO.getCount();
                        if (viewCount == null) {
                            viewCount = increment;
                        } else {
                            viewCount += increment;
                        }
                        lessonClickStatisticDO.setCount(viewCount);
                        statisticToUpdate.add(lessonClickStatisticDO);
                    } else if (needInsertLessonIds.contains(lessonId)) {
                        LessonClickStatisticDO lessonClickStatisticDO = new LessonClickStatisticDO();
                        lessonClickStatisticDO.setCourseLessonId(lessonId);
                        lessonClickStatisticDO.setCount(increment);
                        statisticToUpdate.add(lessonClickStatisticDO);
                    } else {
                        log.warn("[同步点击量] 学习集{}不存在", statisticToUpdate);
                    }
                } catch (Exception e) {
                    log.error("[同步点击量] 处理学习集{}点击量失败", entry.getKey(), e);
                }
            }


            // 批量更新数据库
            if (!statisticToUpdate.isEmpty()) {
                try {
                    if (CollUtil.isNotEmpty(statisticToUpdate)) {
                        // 使用批量更新提高性能
                        lessonClickStatisticMapper.insertOrUpdate(statisticToUpdate);
                        log.info("[同步点击量] 批量更新{}个课程点击量到数据库成功", statisticToUpdate.size());
                    }
                } catch (Exception e) {
                    log.error("[同步点击量] 批量更新课程点击量失败", e);
                    throw e; // 重新抛出异常，触发事务回滚
                }
            }
        }

        // 清空缓存中的点击量计数
        stringRedisTemplate.delete(REDIS_KEY_VIEW_COUNT);

        // 清除优质课程缓存，以便下次查询时重新计算
        Set<String> keys = stringRedisTemplate.keys(CACHE_KEY_QUALITY_LESSON + ":*");
        if (CollUtil.isNotEmpty(keys)) {
            stringRedisTemplate.delete(keys);
            log.info("[同步点击量] 清除优质课程缓存{}个", keys.size());
        }
    }
}
