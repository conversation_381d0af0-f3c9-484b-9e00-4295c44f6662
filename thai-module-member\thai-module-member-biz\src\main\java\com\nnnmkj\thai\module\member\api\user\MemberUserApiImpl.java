package com.nnnmkj.thai.module.member.api.user;

import cn.hutool.core.collection.CollUtil;
import com.nnnmkj.thai.framework.common.enums.UserTypeEnum;
import com.nnnmkj.thai.framework.common.exception.enums.GlobalErrorCodeConstants;
import com.nnnmkj.thai.framework.common.util.object.BeanUtils;
import com.nnnmkj.thai.framework.security.core.LoginUser;
import com.nnnmkj.thai.framework.security.core.util.SecurityFrameworkUtils;
import com.nnnmkj.thai.module.member.api.user.dto.MemberUserRespDTO;
import com.nnnmkj.thai.module.member.controller.app.group.vo.AppMemberGroupUserRespVO;
import com.nnnmkj.thai.module.member.convert.user.MemberUserConvert;
import com.nnnmkj.thai.module.member.dal.dataobject.group.MemberGroupDO;
import com.nnnmkj.thai.module.member.dal.dataobject.user.MemberUserDO;
import com.nnnmkj.thai.module.member.service.group.AppMemberGroupService;
import com.nnnmkj.thai.module.member.service.group.MemberGroupService;
import com.nnnmkj.thai.module.member.service.user.MemberUserService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Objects;

import static com.nnnmkj.thai.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.nnnmkj.thai.module.member.enums.ErrorCodeConstants.USER_MOBILE_NOT_EXISTS;
import static com.nnnmkj.thai.module.member.enums.ErrorCodeConstants.USER_NOT_EXISTS;

/**
 * 会员用户的 API 实现类
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@Validated
public class MemberUserApiImpl implements MemberUserApi {

    @Resource
    private MemberUserService memberUserService;
    @Resource
    private MemberGroupService memberGroupService;
    @Resource
    private AppMemberGroupService appMemberGroupService;

    public static final String DEFAULT_NICKNAME = "后台管理员";

    @Override
    public MemberUserRespDTO getUser(Long id) {
        MemberUserDO user = memberUserService.getUser(id);
        return MemberUserConvert.INSTANCE.convert2(user);
    }

    @Override
    public MemberUserRespDTO getUserByMobile(String mobile) {
        return MemberUserConvert.INSTANCE.convert2(memberUserService.getUserByMobile(mobile));
    }

    @Override
    public List<MemberUserRespDTO> getUserList(Collection<Long> ids) {
        return MemberUserConvert.INSTANCE.convertList(memberUserService.getUserList(ids));
    }

    @Override
    public List<MemberUserRespDTO> getUserListByGroupId(Long groupId) {
        List<MemberUserDO> result = memberUserService.getUserListByGroupId(groupId);
        return BeanUtils.toBean(result,  MemberUserRespDTO.class);
    }

    @Override
    public List<MemberUserRespDTO> getUserListByGroupIds(Collection<Long> groupIds) {
        List<MemberUserDO> result = memberUserService.getUserListByGroupIds(groupIds);
        return BeanUtils.toBean(result, MemberUserRespDTO.class);
    }

    @Override
    public List<MemberUserRespDTO> getUserListByNickname(String nickname) {
        return MemberUserConvert.INSTANCE.convertList(memberUserService.getUserListByNickname(nickname));
    }

    @Override
    public void validateUser(Long id) {
        MemberUserDO user = memberUserService.getUser(id);
        if (user == null) {
            throw exception(USER_MOBILE_NOT_EXISTS);
        }
    }

    @Override
    public MemberUserRespDTO getLoginUser() {
        LoginUser loginUser = SecurityFrameworkUtils.getLoginUser();
        if (loginUser == null) {
            throw exception(GlobalErrorCodeConstants.UNAUTHORIZED);
        }

        Long loginUserId = loginUser.getId();
        UserTypeEnum userType = UserTypeEnum.valueOf(loginUser.getUserType());

        MemberUserDO user = switch (userType) {
            case MEMBER -> memberUserService.getUser(loginUserId);
            case ADMIN -> memberUserService.getUserBySystemUserId(loginUserId);
        };

        if (user == null) {
            throw exception(USER_NOT_EXISTS); // 用户不存在
        }

        return MemberUserConvert.INSTANCE.convert2(user);
    }

    @Override
    public Long getLoginUserId() {
        return getLoginUser().getId();
    }

    @Override
    public Long getLoginUserSystemId() {
        return getLoginUser().getSystemUserId();
    }

    @Override
    public Long getUserIdBySystemUserId(Long systemUserId) {
        MemberUserDO user = memberUserService.getUserBySystemUserId(systemUserId);
        return Objects.nonNull(user) ? user.getId() : null; // 当用户不存在时，返回null
    }

    public List<MemberUserRespDTO> getUserListBySystemUserIds(Collection<Long> systemUserIds) {
        List<MemberUserDO> users = memberUserService.getUserListBySystemUserIds(systemUserIds);
        return BeanUtils.toBean(users, MemberUserRespDTO.class);
    }

    @Override
    public List<Long> getGroupIdsById(Long userId) {
        MemberUserDO memberUserDO = memberUserService.getUser(userId);
        if (memberUserDO == null) {
            throw exception(USER_NOT_EXISTS);
        }
        List<Long> groupIds = memberUserDO.getGroupIds();
        List<MemberGroupDO> memberGroupDOS = memberGroupService.getGroupListByUserId(userId);
        List<Long> ids = memberGroupDOS.stream().map(MemberGroupDO::getId).toList();
        // 合并两个列表并去重
        if (CollUtil.isEmpty(groupIds)) {
            groupIds = ids;
        } else {
            groupIds.addAll(ids);
        }
        if (CollUtil.isEmpty(groupIds)) {
            return new ArrayList<>();
        } else {
            return groupIds.stream().distinct().toList();
        }
    }

}
