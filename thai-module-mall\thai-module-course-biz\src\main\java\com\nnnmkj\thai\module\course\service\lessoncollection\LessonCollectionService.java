package com.nnnmkj.thai.module.course.service.lessoncollection;

import com.nnnmkj.thai.framework.common.pojo.PageResult;
import com.nnnmkj.thai.module.course.controller.admin.lessoncollection.vo.LessonCollectionPageReqVO;
import com.nnnmkj.thai.module.course.controller.admin.lessoncollection.vo.LessonCollectionSaveReqVO;
import com.nnnmkj.thai.module.course.dal.dataobject.lessoncollection.LessonCollectionDO;
import jakarta.validation.Valid;

/**
 * 课程收藏 Service 接口
 *
 * <AUTHOR>
 */
public interface LessonCollectionService {

    /**
     * 创建课程收藏
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createLessonCollection(@Valid LessonCollectionSaveReqVO createReqVO);

    /**
     * 更新课程收藏
     *
     * @param updateReqVO 更新信息
     */
    void updateLessonCollection(@Valid LessonCollectionSaveReqVO updateReqVO);

    /**
     * 删除课程收藏
     *
     * @param id 编号
     */
    void deleteLessonCollection(Long id);

    /**
     * 获得课程收藏
     *
     * @param id 编号
     * @return 课程收藏
     */
    LessonCollectionDO getLessonCollection(Long id);

    /**
     * 获得课程收藏分页
     *
     * @param pageReqVO 分页查询
     * @return 课程收藏分页
     */
    PageResult<LessonCollectionDO> getLessonCollectionPage(LessonCollectionPageReqVO pageReqVO);

}