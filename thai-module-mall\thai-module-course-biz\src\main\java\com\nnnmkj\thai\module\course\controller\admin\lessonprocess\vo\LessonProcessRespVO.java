package com.nnnmkj.thai.module.course.controller.admin.lessonprocess.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Schema(description = "管理后台 - 课程进度 Response VO")
@Data
@ExcelIgnoreUnannotated
public class LessonProcessRespVO {

    @Schema(description = "编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "31976")
    @ExcelProperty("编号")
    private Long id;

    @Schema(description = "课程ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "14942")
    @ExcelProperty("课程ID")
    private Long courseId;

    @Schema(description = "课程标题")
    private String courseTitle;

    @Schema(description = "用户ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "29748")
    @ExcelProperty("用户ID")
    private Long userId;

    @Schema(description = "用户昵称")
    private String nickname;

    @Schema(description = "总章节数", requiredMode = Schema.RequiredMode.REQUIRED, example = "18968")
    @ExcelProperty("总章节数")
    private Integer allCount;

    @Schema(description = "已学习章节数", requiredMode = Schema.RequiredMode.REQUIRED, example = "28536")
    @ExcelProperty("已学习章节数")
    private Integer learnedCount;

    @Schema(description = "未学习章节数", requiredMode = Schema.RequiredMode.REQUIRED, example = "12473")
    @ExcelProperty("未学习章节数")
    private Integer notLearnedCount;

    @Schema(description = "当前学习章节ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "30890")
    @ExcelProperty("当前学习章节ID")
    private Long currentChapterId;

    @Schema(description = "学习进度百分比", requiredMode = Schema.RequiredMode.REQUIRED, example = "10")
    @ExcelProperty("学习进度百分比")
    private Integer progressPercentage;

    @Schema(description = "状态", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @ExcelProperty("状态")
    private Integer status;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}