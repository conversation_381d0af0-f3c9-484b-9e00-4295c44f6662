package com.nnnmkj.thai.module.course.dal.mysql.lessonchapterstudyrecord;

import com.nnnmkj.thai.framework.common.pojo.PageResult;
import com.nnnmkj.thai.framework.mybatis.core.mapper.BaseMapperX;
import com.nnnmkj.thai.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.nnnmkj.thai.module.course.controller.admin.lessonchapterstudyrecord.vo.LessonChapterStudyRecordPageReqVO;
import com.nnnmkj.thai.module.course.controller.app.lessonchapterstudyrecord.vo.AppLessonChapterStudyRecordPageReqVO;
import com.nnnmkj.thai.module.course.dal.dataobject.lessonchapterstudyrecord.LessonChapterStudyRecordDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * 课程章节学习记录 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface LessonChapterStudyRecordMapper extends BaseMapperX<LessonChapterStudyRecordDO> {

    default PageResult<LessonChapterStudyRecordDO> selectPage(LessonChapterStudyRecordPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<LessonChapterStudyRecordDO>()
                .eqIfPresent(LessonChapterStudyRecordDO::getCourseId, reqVO.getCourseId())
                .eqIfPresent(LessonChapterStudyRecordDO::getChapterId, reqVO.getChapterId())
                .eqIfPresent(LessonChapterStudyRecordDO::getUserId, reqVO.getUserId())
                .eqIfPresent(LessonChapterStudyRecordDO::getStatus, reqVO.getStatus())
                .inIfPresent(LessonChapterStudyRecordDO::getCourseId, reqVO.getCourseIds())
                .betweenIfPresent(LessonChapterStudyRecordDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(LessonChapterStudyRecordDO::getId));
    }

    default PageResult<LessonChapterStudyRecordDO> selectPage(AppLessonChapterStudyRecordPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<LessonChapterStudyRecordDO>()
                .eqIfPresent(LessonChapterStudyRecordDO::getCourseId, reqVO.getCourseId())
                .eqIfPresent(LessonChapterStudyRecordDO::getChapterId, reqVO.getChapterId())
                .eqIfPresent(LessonChapterStudyRecordDO::getUserId, reqVO.getUserId())
                .eqIfPresent(LessonChapterStudyRecordDO::getStatus, reqVO.getStatus())
                .betweenIfPresent(LessonChapterStudyRecordDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(LessonChapterStudyRecordDO::getId));
    }

}