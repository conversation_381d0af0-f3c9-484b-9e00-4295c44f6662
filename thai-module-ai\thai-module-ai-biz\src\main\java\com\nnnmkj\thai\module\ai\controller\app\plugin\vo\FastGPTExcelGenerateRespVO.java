package com.nnnmkj.thai.module.ai.controller.app.plugin.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * FastGPT Excel 生成响应 VO
 *
 * <AUTHOR>
 */
@Schema(description = "FastGPT Excel 生成响应 VO")
@Data
public class FastGPTExcelGenerateRespVO {

    @Schema(description = "Excel文件下载URL", example = "https://example.com/files/excel/20240101/example.xlsx")
    private String fileUrl;

    @Schema(description = "文件名", example = "学生成绩表.xlsx")
    private String fileName;

    @Schema(description = "文件大小（字节）", example = "1024")
    private Long fileSize;

}
