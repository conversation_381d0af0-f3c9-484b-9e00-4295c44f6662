package com.nnnmkj.thai.module.learning.controller.admin.membergroupwordset;

import cn.hutool.core.collection.CollUtil;
import com.nnnmkj.thai.framework.apilog.core.annotation.ApiAccessLog;
import com.nnnmkj.thai.framework.common.pojo.CommonResult;
import com.nnnmkj.thai.framework.common.pojo.PageParam;
import com.nnnmkj.thai.framework.common.pojo.PageResult;
import com.nnnmkj.thai.framework.common.util.object.BeanUtils;
import com.nnnmkj.thai.framework.excel.core.util.ExcelUtils;
import com.nnnmkj.thai.module.learning.controller.admin.membergroupwordset.vo.MemberGroupWordSetPageReqVO;
import com.nnnmkj.thai.module.learning.controller.admin.membergroupwordset.vo.MemberGroupWordSetRespVO;
import com.nnnmkj.thai.module.learning.controller.admin.membergroupwordset.vo.MemberGroupWordSetSaveReqVO;
import com.nnnmkj.thai.module.learning.convert.wordsetcollectionstatistic.WordSetCollectionStatisticConvert;
import com.nnnmkj.thai.module.learning.convert.wordsetstudystatistic.WordSetStudyStatisticConvert;
import com.nnnmkj.thai.module.learning.dal.dataobject.membergroupwordset.MemberGroupWordSetDO;
import com.nnnmkj.thai.module.learning.dal.dataobject.wordset.WordSetDO;
import com.nnnmkj.thai.module.learning.service.membergroupwordset.MemberGroupWordSetService;
import com.nnnmkj.thai.module.learning.service.wordset.WordSetService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

import static com.nnnmkj.thai.framework.apilog.core.enums.OperateTypeEnum.EXPORT;
import static com.nnnmkj.thai.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 班级-学习集关联")
@RestController
@RequestMapping("/learning/member-group-word-set")
@Validated
public class MemberGroupWordSetController {

    @Resource
    private MemberGroupWordSetService memberGroupWordSetService;

    @Resource
    private WordSetService wordSetService;

    @PostMapping("/create")
    @Operation(summary = "创建班级-学习集关联")
    @PreAuthorize("@ss.hasPermission('learning:member-group-word-set:create')")
    public CommonResult<Long> createMemberGroupWordSet(@Valid @RequestBody MemberGroupWordSetSaveReqVO createReqVO) {
        return success(memberGroupWordSetService.createMemberGroupWordSet(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新班级-学习集关联")
    @PreAuthorize("@ss.hasPermission('learning:member-group-word-set:update')")
    public CommonResult<Boolean> updateMemberGroupWordSet(@Valid @RequestBody MemberGroupWordSetSaveReqVO updateReqVO) {
        memberGroupWordSetService.updateMemberGroupWordSet(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除班级-学习集关联")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('learning:member-group-word-set:delete')")
    public CommonResult<Boolean> deleteMemberGroupWordSet(@RequestParam("id") Long id) {
        memberGroupWordSetService.deleteMemberGroupWordSet(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得班级-学习集关联")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('learning:member-group-word-set:query')")
    public CommonResult<MemberGroupWordSetRespVO> getMemberGroupWordSet(@RequestParam("id") Long id) {
        MemberGroupWordSetDO memberGroupWordSet = memberGroupWordSetService.getMemberGroupWordSet(id);
        if (memberGroupWordSet == null) {
            return success(null);
        }
        // 处理学习集返显
        WordSetDO wordSet = wordSetService.getWordSet(memberGroupWordSet.getWordSetId());
        return success(WordSetCollectionStatisticConvert.INSTANCE.convert1(memberGroupWordSet, wordSet));
    }

    @GetMapping("/page")
    @Operation(summary = "获得班级-学习集关联分页")
    @PreAuthorize("@ss.hasPermission('learning:member-group-word-set:query')")
    public CommonResult<PageResult<MemberGroupWordSetRespVO>> getMemberGroupWordSetPage(@Valid MemberGroupWordSetPageReqVO pageReqVO) {
        PageResult<MemberGroupWordSetDO> pageResult = memberGroupWordSetService.getMemberGroupWordSetPage(pageReqVO);
        if (CollUtil.isEmpty(pageResult.getList())) {
            return success(PageResult.empty());
        }
        // 处理学习集返显
        Set<Long> setIds = pageResult.getList().stream().map(MemberGroupWordSetDO::getWordSetId).collect(Collectors.toSet());
        List<WordSetDO> wordSetList = wordSetService.getWordSetList(setIds);
        return success(WordSetStudyStatisticConvert.INSTANCE.convertPage1(pageResult, wordSetList));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出班级-学习集关联 Excel")
    @PreAuthorize("@ss.hasPermission('learning:member-group-word-set:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportMemberGroupWordSetExcel(@Valid MemberGroupWordSetPageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<MemberGroupWordSetDO> list = memberGroupWordSetService.getMemberGroupWordSetPage(pageReqVO).getList();
        // 处理学习集返显
        Set<Long> setIds = list.stream().map(MemberGroupWordSetDO::getWordSetId).collect(Collectors.toSet());
        List<WordSetDO> wordSetList = wordSetService.getWordSetList(setIds);
        // 导出 Excel
        ExcelUtils.write(response, "班级-学习集关联.xls", "数据", MemberGroupWordSetRespVO.class,
                WordSetStudyStatisticConvert.INSTANCE.convertList1(list, wordSetList));
    }

    @GetMapping("/list")
    @Operation(summary = "获得班级-学习集关联列表")
    @PreAuthorize("@ss.hasPermission('learning:member-group-word-set:query')")
    public CommonResult<List<MemberGroupWordSetRespVO>> getMemberGroupWordSetListByWordSetId(@RequestParam("wordSetId") Long wordSetId) {
        List<MemberGroupWordSetDO> list = memberGroupWordSetService.getMemberGroupWordSetList(wordSetId);
        // 处理学习集返显
        Set<Long> setIds = list.stream().map(MemberGroupWordSetDO::getWordSetId).collect(Collectors.toSet());

        List<WordSetDO> wordSetList = wordSetService.getWordSetList(setIds);
        return success(BeanUtils.toBean(list, MemberGroupWordSetRespVO.class));
    }

}