package com.nnnmkj.thai.module.ai.api.nls.vo;

import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

@Data
public class AiNlsTtsConfigVO {

    /**
     * 文本
     */
    @NotBlank(message = "文本不能为空")
    private String text;

    /**
     * 发音人
     */
    @NotBlank(message = "发音人不能为空")
    private String speaker;

    /**
     * 语速
     */
    @Max(value = 500, message = "语速不能大于500")
    @Min(value = -500, message = "语速不能小于-500")
    private Integer speechRate;

    /**
     * 音量
     */
    @Max(value = 500, message = "音量不能大于500")
    @Min(value = -500, message = "音量不能小于-500")
    @NotNull(message = "音量不能为空")
    private Integer pitchRate;

    /**
     * 是否显示字幕
     */
    private Boolean displayCaptions;

}
