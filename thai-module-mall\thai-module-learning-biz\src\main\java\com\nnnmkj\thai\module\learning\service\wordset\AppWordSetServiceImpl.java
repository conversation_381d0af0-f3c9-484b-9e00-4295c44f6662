package com.nnnmkj.thai.module.learning.service.wordset;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.nnnmkj.thai.framework.common.pojo.PageResult;
import com.nnnmkj.thai.framework.common.util.common.CommonUtils;
import com.nnnmkj.thai.framework.common.util.object.BeanUtils;
import com.nnnmkj.thai.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.nnnmkj.thai.framework.security.core.util.SecurityFrameworkUtils;
import com.nnnmkj.thai.framework.web.core.util.WebFrameworkUtils;
import com.nnnmkj.thai.module.ai.api.auditors.AiAuditorsApi;
import com.nnnmkj.thai.module.ai.api.auditors.dto.AiAuditorsReqDTO;
import com.nnnmkj.thai.module.ai.api.auditors.dto.AiAuditorsRespDTO;
import com.nnnmkj.thai.module.learning.controller.app.wordset.vo.*;
import com.nnnmkj.thai.module.learning.dal.dataobject.membergroupwordset.MemberGroupWordSetDO;
import com.nnnmkj.thai.module.learning.dal.dataobject.question.QuestionDO;
import com.nnnmkj.thai.module.learning.dal.dataobject.questionoption.QuestionOptionDO;
import com.nnnmkj.thai.module.learning.dal.dataobject.session.SessionDO;
import com.nnnmkj.thai.module.learning.dal.dataobject.sessioncardrecord.SessionCardRecordDO;
import com.nnnmkj.thai.module.learning.dal.dataobject.sessioncardrecord.SessionCardRecordDTO;
import com.nnnmkj.thai.module.learning.dal.dataobject.wordcard.WordCardDO;
import com.nnnmkj.thai.module.learning.dal.dataobject.wordcard.WordCardDTO;
import com.nnnmkj.thai.module.learning.dal.dataobject.wordcarddefinition.WordCardDefinitionDO;
import com.nnnmkj.thai.module.learning.dal.dataobject.wordset.WordSetDO;
import com.nnnmkj.thai.module.learning.dal.dataobject.wordset.WordSetDTO;
import com.nnnmkj.thai.module.learning.dal.dataobject.wordsetcollection.WordSetCollectionDO;
import com.nnnmkj.thai.module.learning.dal.dataobject.wordsetcollectionstatistic.WordSetCollectionStatisticDO;
import com.nnnmkj.thai.module.learning.dal.dataobject.wordsettop.WordSetTopDO;
import com.nnnmkj.thai.module.learning.dal.mysql.membergroupwordset.MemberGroupWordSetMapper;
import com.nnnmkj.thai.module.learning.dal.mysql.question.QuestionMapper;
import com.nnnmkj.thai.module.learning.dal.mysql.questionoption.QuestionOptionMapper;
import com.nnnmkj.thai.module.learning.dal.mysql.session.SessionMapper;
import com.nnnmkj.thai.module.learning.dal.mysql.sessioncardrecord.SessionCardRecordMapper;
import com.nnnmkj.thai.module.learning.dal.mysql.wordcard.WordCardMapper;
import com.nnnmkj.thai.module.learning.dal.mysql.wordcarddefinition.WordCardDefinitionMapper;
import com.nnnmkj.thai.module.learning.dal.mysql.wordset.WordSetMapper;
import com.nnnmkj.thai.module.learning.dal.mysql.wordset.WordSetMapperX;
import com.nnnmkj.thai.module.learning.dal.mysql.wordsetcollection.WordSetCollectionMapper;
import com.nnnmkj.thai.module.learning.dal.mysql.wordsettop.WordSetTopMapper;
import com.nnnmkj.thai.module.learning.enums.*;
import com.nnnmkj.thai.module.learning.service.wordsetcollectionstatistic.WordSetCollectionStatisticService;
import com.nnnmkj.thai.module.member.api.user.MemberUserApi;
import com.nnnmkj.thai.module.member.api.user.dto.MemberUserRespDTO;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import java.util.*;
import java.util.stream.Collectors;

import static com.nnnmkj.thai.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.nnnmkj.thai.module.learning.enums.ErrorCodeConstants.*;

/**
 * 学习集 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class AppWordSetServiceImpl implements AppWordSetService {

    @Resource
    private WordSetMapper wordSetMapper;

    @Resource
    private WordSetCollectionMapper wordSetCollectionMapper;

    @Resource
    private WordSetTopMapper wordSetTopMapper;

    @Resource
    private WordSetMapperX wordSetMapperX;
    @Resource
    private WordCardMapper wordCardMapper;

    @Resource
    private QuestionMapper questionMapper;

    @Resource
    private WordCardDefinitionMapper wordCardDefinitionMapper;

    @Resource
    private QuestionOptionMapper questionOptionMapper;

    @Resource
    private QuestionGeneratorService questionGeneratorService;

    @Resource
    private SessionCardRecordMapper sessionCardRecordMapper;

    @Resource
    private SessionMapper sessionMapper;

    @Resource
    private MemberGroupWordSetMapper memberGroupWordSetMapper;

    @Resource
    private MemberUserApi memberUserApi;

    @Resource
    private AiAuditorsApi aiAuditorsApi;

    @Resource
    private WordSetCollectionStatisticService wordSetCollectionStatisticService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long createWordSet(AppWordSetSaveReqVO createReqVO) {
        // 插入
        WordSetDO wordSet = BeanUtils.toBean(createReqVO, WordSetDO.class);

        wordSetMapper.insert(wordSet);
        // 学习集ID
        Long wordSetId = wordSet.getId();
        // 可见范围
        Integer visibility = createReqVO.getVisibility();
        // 如果不是仅自己可见，需要进行内容审核
        if (!WordSetVisibilityEnum.SELF.getType().equals(visibility)) {
            // 转换对象AppWordSetSaveReqVO转换AppWordSetRespVO
            AppWordSetRespVO wordSetTopRespVO = BeanUtils.toBean(createReqVO, AppWordSetRespVO.class);
            // 进行内容审核
            auditWordSetContent(wordSetTopRespVO, SecurityFrameworkUtils.getLoginUserId());
        }
        // 传入的班级id列表
        List<Long> classIds = createReqVO.getClassIds();
        // 如果是组内可见并且班级ID列表不为空，创建分享
        if (WordSetVisibilityEnum.GROUP.getType().equals(visibility) && CollUtil.isNotEmpty(classIds)) {
            List<Long> groupIds = memberUserApi.getGroupIdsById(SecurityFrameworkUtils.getLoginUserId());
            // 保留classIds在groupIds内的项
            List<Long> ids = classIds.stream().filter(groupIds::contains).collect(Collectors.toList());
            List<MemberGroupWordSetDO> list = new ArrayList<>();
            if (CollUtil.isNotEmpty(ids)) {
                for (Long id : ids) {
                    MemberGroupWordSetDO memberGroupWordSetDO = new MemberGroupWordSetDO();
                    memberGroupWordSetDO.setGroupId(id);
                    memberGroupWordSetDO.setWordSetId(wordSetId);
                    list.add(memberGroupWordSetDO);
                }
            }
            memberGroupWordSetMapper.insertBatch(list);
        }

        // 传入的单词卡列表
        List<WordCardDTO> wordCards = createReqVO.getWordCards();
        // 插入子表
        createWordCardList(wordSet.getId(), wordCards);
        // 单词卡为空，直接返回
        if (CollUtil.isEmpty(wordCards)) {
            return wordSetId;
        }
        // 根据单词卡生成题目、题目选项
        List<WordCardDefinitionDO> wordCardDefinitionList = wordCards.stream().map(WordCardDTO::getDefinitions)
                .filter(CollUtil::isNotEmpty).flatMap(Collection::stream).toList();
        Set<String> definitions = wordCardDefinitionList.stream().map(WordCardDefinitionDO::getDefinition).collect(Collectors.toSet());
        questionGeneratorService.generateQuestions(wordSetId, wordCards, definitions);
        // 返回
        return wordSetId;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateWordSet(AppWordSetSaveReqVO reqVO) {
        Long wordSetId = reqVO.getId();
        // 可见范围
        Integer visibility = reqVO.getVisibility();
        // 如果不是仅自己可见，需要进行内容审核
        if (!WordSetVisibilityEnum.SELF.getType().equals(visibility)) {
            // 转换对象AppWordSetSaveReqVO转换AppWordSetRespVO
            AppWordSetRespVO wordSetTopRespVO = BeanUtils.toBean(reqVO, AppWordSetRespVO.class);
            // 进行内容审核
            auditWordSetContent(wordSetTopRespVO, SecurityFrameworkUtils.getLoginUserId());
        }
        // 传入的班级id列表
        List<Long> classIds = reqVO.getClassIds();
        // 如果是组内可见，处理班级分享
        if (WordSetVisibilityEnum.GROUP.getType().equals(visibility)) {
            // 先删除当前学习集的所有班级关联
            memberGroupWordSetMapper.delete(new LambdaQueryWrapper<MemberGroupWordSetDO>()
                    .eq(MemberGroupWordSetDO::getWordSetId, wordSetId));
            
            // 如果班级ID列表不为空，重新创建分享
            if (CollUtil.isNotEmpty(classIds)) {
                List<Long> groupIds = memberUserApi.getGroupIdsById(SecurityFrameworkUtils.getLoginUserId());
                // 保留classIds在groupIds内的项
                List<Long> ids = classIds.stream().filter(groupIds::contains).collect(Collectors.toList());
                
                List<MemberGroupWordSetDO> list = new ArrayList<>();
                if (CollUtil.isNotEmpty(ids)) {
                    for (Long id : ids) {
                        MemberGroupWordSetDO memberGroupWordSetDO = new MemberGroupWordSetDO();
                        memberGroupWordSetDO.setGroupId(id);
                        memberGroupWordSetDO.setWordSetId(wordSetId);
                        list.add(memberGroupWordSetDO);
                    }
                    memberGroupWordSetMapper.insertBatch(list);
                }
            }
        } else {
            // 如果不是组内可见，删除所有班级关联
            memberGroupWordSetMapper.delete(new LambdaQueryWrapper<MemberGroupWordSetDO>()
                    .eq(MemberGroupWordSetDO::getWordSetId, wordSetId));
        }
        // 传入的单词卡列表
        List<WordCardDTO> wordCards = reqVO.getWordCards();
        // 查询已存在的单词卡
        List<WordCardDO> existWordCards = wordCardMapper.selectList(new LambdaQueryWrapper<WordCardDO>()
                .eq(WordCardDO::getWordSetId, wordSetId));
        // 查询已存在的单词定义
        Set<Long> wordCardIds = existWordCards.stream().map(WordCardDO::getId).collect(Collectors.toSet());
        LambdaQueryWrapper<WordCardDefinitionDO> wrapper = new LambdaQueryWrapper<WordCardDefinitionDO>().in(WordCardDefinitionDO::getWordCardId, wordCardIds);
        List<WordCardDefinitionDO> wordCardDefinitionDOS = wordCardDefinitionMapper.selectList(wrapper);
        // 转换为DTO
        List<WordCardDTO> wordCardDTOList = new ArrayList<>();
        // 转换为DTO
        for (WordCardDO existWordCard : existWordCards) {
            WordCardDTO wordCardDTO = BeanUtils.toBean(existWordCard, WordCardDTO.class);
            if (CollUtil.isNotEmpty(wordCardDefinitionDOS)) {
                List<WordCardDefinitionDO> definitionDOList = wordCardDefinitionDOS.stream()
                        .filter(wordCardDefinitionDO -> wordCardDefinitionDO.getWordCardId().equals(existWordCard.getId())).toList();
                wordCardDTO.setDefinitions(definitionDOList);
            }
            wordCardDTOList.add(wordCardDTO);
        }
        // 1. 处理学习集
        WordSetDO wordSetDO = handleWordSet(reqVO);
        // 2. 处理单词卡
        List<WordCardDO> savedWordCards = handleWordCards(wordCards, wordSetDO.getId());
        // 3. 处理单词卡定义
        handleWordCardDefinitions(wordCards, savedWordCards, wordSetDO.getId());
        // 新增的单词卡为空，直接返回
        if (CollUtil.isEmpty(wordCards)) {
            return;
        }
        // 根据单词卡生成题目、题目选项
        List<WordCardDTO> wordCardDTOS = filterExistingQuestions(wordCardDTOList, wordCards);
        Set<String> definitions;
        if (CollUtil.isNotEmpty(wordCardIds)) {
            LambdaQueryWrapper<WordCardDefinitionDO> queryWrapper = new LambdaQueryWrapper<WordCardDefinitionDO>()
                    .in(WordCardDefinitionDO::getWordCardId, wordCardIds);
            List<WordCardDefinitionDO> cardDefinitionDOS = wordCardDefinitionMapper.selectList(queryWrapper);
            // 提取定义列表
            definitions = cardDefinitionDOS.stream().map(WordCardDefinitionDO::getDefinition)
                    .collect(Collectors.toSet());
            // 提取传入的单词定义
            Set<String> reqDefinitions = wordCards.stream().map(WordCardDTO::getDefinitions)
                    .filter(CollUtil::isNotEmpty).flatMap(Collection::stream)
                    .map(WordCardDefinitionDO::getDefinition).collect(Collectors.toSet());
            // 合并
            definitions.addAll(reqDefinitions);

        } else {
            // 提取传入的单词定义
            definitions = wordCards.stream().map(WordCardDTO::getDefinitions)
                    .filter(CollUtil::isNotEmpty).flatMap(Collection::stream)
                    .map(WordCardDefinitionDO::getDefinition).collect(Collectors.toSet());
        }

        questionGeneratorService.generateQuestions(wordSetId, wordCardDTOS, definitions);
    }

    @Override
    public void updateWordSetVisibility(AppWordSetVisibilityReqVO reqVO) {
        // 判断可见范围是否为仅自己可见
        if (!WordSetVisibilityEnum.SELF.getType().equals(reqVO.getVisibility())) {
            // 转换对象AppWordSetSaveReqVO转换AppWordSetRespVO
            AppWordSetRespVO wordSetTopRespVO = BeanUtils.toBean(reqVO, AppWordSetRespVO.class);
            // 进行内容审核
            auditWordSetContent(wordSetTopRespVO, SecurityFrameworkUtils.getLoginUserId());
        }
        WordSetDO wordSetDO = wordSetMapper.selectOne(WordSetDO::getId, reqVO.getId(), WordSetDO::getUserId, reqVO.getUserId());
        if (wordSetDO == null) {
            throw exception(WORD_SET_NOT_EXISTS);
        }
        wordSetDO = BeanUtils.toBean(reqVO, WordSetDO.class);
        wordSetMapper.updateById(wordSetDO);
    }

    /**
     * 处理学习集
     *
     * @param reqVO 请求参数
     * @return 学习集
     */
    private WordSetDO handleWordSet(AppWordSetSaveReqVO reqVO) {
        WordSetDO wordSetDO;
        if (reqVO.getId() != null) {
            // 更新现有学习集
            wordSetDO = wordSetMapper.selectById(reqVO.getId());
            if (wordSetDO == null) {
                throw exception(WORD_SET_NOT_EXISTS);
            }
            wordSetDO = BeanUtils.toBean(reqVO, WordSetDO.class);
            wordSetMapper.updateById(wordSetDO);
        } else {
            // 创建新学习集
            wordSetDO = BeanUtils.toBean(reqVO, WordSetDO.class);
            wordSetMapper.insert(wordSetDO);
        }
        return wordSetDO;
    }

    /**
     * 处理单词卡
     *
     * @param wordCards 传入的单词卡
     * @param wordSetId 学习集id
     * @return 保存的单词卡
     */
    private List<WordCardDO> handleWordCards(List<WordCardDTO> wordCards, Long wordSetId) {
        if (CollUtil.isEmpty(wordCards)) {
            return Collections.emptyList();
        }
        List<WordCardDO> savedWordCards = new ArrayList<>();
        List<WordCardDO> existingWordCards = wordCardMapper.selectList(WordCardDO::getWordSetId, wordSetId);
        // 获取已存在的单词卡ID列表
        Set<Long> setIds = existingWordCards.stream().map(WordCardDO::getId).collect(Collectors.toSet());
        // 传入的单词卡ID列表
        Set<Long> reqSetIds = wordCards.stream().map(WordCardDTO::getId).collect(Collectors.toSet());
        // 获取
        if (CollUtil.isNotEmpty(setIds)) {
            // setIds过滤掉reqSetIds在内已存在的项
            setIds.removeAll(reqSetIds);
            wordCardMapper.deleteByIds(setIds);
        }
        for (WordCardDTO cardDTO : wordCards) {
            WordCardDO wordCardDO;
            if (cardDTO.getId() != null && cardDTO.getId() != 0L) {
                // 有ID的单词卡 - 更新
                wordCardDO = wordCardMapper.selectById(cardDTO.getId());
                if (wordCardDO == null) {
                    throw exception(WORD_CARD_NOT_EXISTS);
                }
                wordCardDO.setWord(cardDTO.getWord());
                wordCardDO.setPhoneticSymbol(cardDTO.getPhoneticSymbol());
                wordCardDO.setImageUrl(cardDTO.getImageUrl());
                wordCardDO.setAudioUrl(cardDTO.getAudioUrl());
                wordCardMapper.updateById(wordCardDO);
            } else {
                // 全新单词卡 - 插入
                wordCardDO = new WordCardDO();
                wordCardDO.setWordSetId(wordSetId);
                wordCardDO.setWord(cardDTO.getWord());
                wordCardDO.setPhoneticSymbol(cardDTO.getPhoneticSymbol());
                wordCardDO.setImageUrl(cardDTO.getImageUrl());
                wordCardDO.setAudioUrl(cardDTO.getAudioUrl());
                wordCardMapper.insert(wordCardDO);
                // 更新DTO的ID、用于后续操作
                cardDTO.setId(wordCardDO.getId());
            }
            savedWordCards.add(wordCardDO);
        }

        return savedWordCards;
    }

    /**
     * 处理单词卡定义
     *
     * @param wordCards      用户传入的单词卡列表
     * @param savedWordCards 保存的单词卡列表
     * @param wordSetId      学习集ID
     */
    private void handleWordCardDefinitions(List<WordCardDTO> wordCards, List<WordCardDO> savedWordCards, Long wordSetId) {
        if (CollUtil.isEmpty(wordCards)) {
            return;
        }

        // 建立词语到单词卡ID的映射
        Map<String, Long> wordToCardIdMap = savedWordCards.stream()
                .collect(Collectors.toMap(WordCardDO::getWord, WordCardDO::getId));

        for (WordCardDTO cardDTO : wordCards) {
            if (CollUtil.isEmpty(cardDTO.getDefinitions())) {
                continue;
            }

            Long wordCardId = wordToCardIdMap.get(cardDTO.getWord());
            if (wordCardId == null) {
                continue;
            }

            // 获取该单词卡下现有的所有定义
            List<WordCardDefinitionDO> existingDefinitions = wordCardDefinitionMapper.selectList(
                    new LambdaQueryWrapper<WordCardDefinitionDO>()
                            .eq(WordCardDefinitionDO::getWordCardId, wordCardId));

            for (WordCardDefinitionDO definitionDTO : cardDTO.getDefinitions()) {
                WordCardDefinitionDO definitionDO;
                if (definitionDTO.getId() != null && definitionDTO.getId() != 0L) {
                    // 有ID的定义 - 更新
                    definitionDO = wordCardDefinitionMapper.selectById(definitionDTO.getId());
                    if (definitionDO == null) {
                        throw exception(WORD_CARD_DEFINITION_NOT_EXISTS);
                    }
                    definitionDO.setDefinition(definitionDTO.getDefinition());
                    wordCardDefinitionMapper.updateById(definitionDO);
                } else {
                    // 检查是否有相同内容的定义
                    Optional<WordCardDefinitionDO> existingDef = existingDefinitions.stream()
                            .filter(d -> d.getDefinition().equals(definitionDTO.getDefinition()))
                            .findFirst();

                    if (existingDef.isPresent()) {
                        // 有相同内容的定义 - 更新
                        definitionDO = existingDef.get();
                        definitionDO.setDefinition(definitionDTO.getDefinition());
                        wordCardDefinitionMapper.updateById(definitionDO);
                    } else {
                        // 全新定义 - 插入
                        definitionDO = new WordCardDefinitionDO();
                        definitionDO.setWordSetId(wordSetId);
                        definitionDO.setWordCardId(wordCardId);
                        definitionDO.setDefinition(definitionDTO.getDefinition());
                        wordCardDefinitionMapper.insert(definitionDO);
                    }
                }
            }
        }
    }

    /**
     * 过滤掉已存在的单词卡
     *
     * @param existWordCards 已存在的单词卡列表
     * @param wordCards      传入的单词卡列表
     * @return 过滤后的单词卡列表
     */
    private List<WordCardDTO> filterExistingQuestions(List<WordCardDTO> existWordCards, List<WordCardDTO> wordCards) {
        if (CollUtil.isEmpty(wordCards)) {
            return Collections.emptyList();
        }
        // 获取已存在的单词卡ID及其定义
        if (CollUtil.isEmpty(existWordCards)) {
            return wordCards;
        }
        // 过滤掉已存在的单词卡，如果定义相同则保留
        List<WordCardDTO> newWordCards = new ArrayList<>();
        for (WordCardDTO wordCard : wordCards) {
            Long wordCardId = wordCard.getId();
            String word = wordCard.getWord();
            List<WordCardDefinitionDO> wordCardDefinitions = wordCard.getDefinitions();
            if (CollUtil.isEmpty(wordCardDefinitions)) {
                continue;
            }
            // 如果不存在，则添加到新列表中
            if (existWordCards.stream().noneMatch(e -> e.getId().equals(wordCardId))) {
                newWordCards.add(wordCard);
                continue;
            }
            // 判断word是否相同
            List<WordCardDTO> wordCardDTOS = existWordCards.stream().filter(e -> e.getId().equals(wordCardId)).toList();
            List<String> words = wordCardDTOS.stream().map(WordCardDTO::getWord).toList();
            if (CollUtil.isEmpty(words)) {
                newWordCards.add(wordCard);
                continue;
            }
            // 判断单词是否已存在
            if (!words.contains(word)) {
                newWordCards.add(wordCard);
                continue;
            }
            List<String> existingDefinitions = wordCardDTOS.stream()
                    .map(WordCardDTO::getDefinitions)
                    .flatMap(Collection::stream).map(WordCardDefinitionDO::getDefinition).toList();
            List<String> currentDefinitions = wordCardDefinitions.stream().map(WordCardDefinitionDO::getDefinition).toList();

            if (!existingDefinitions.equals(currentDefinitions)) {
                // 如果定义不同，删除旧题目
                questionMapper.delete(new LambdaQueryWrapper<QuestionDO>().eq(QuestionDO::getWordCardId, wordCardId));
                newWordCards.add(wordCard);
            }
        }

        return newWordCards;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteWordSet(Long id, Long userId) {
        // 校验存在
        validateWordSetExists(id, userId);
        // 删除
        wordSetMapper.deleteById(id);

        // 删除学习集收藏
        wordSetCollectionMapper.delete(new LambdaQueryWrapper<WordSetCollectionDO>()
                .eq(WordSetCollectionDO::getSetId, id));
        
        // 删除学习集收藏统计
        WordSetCollectionStatisticDO statistic = wordSetCollectionStatisticService.getWordSetCollectionStatisticBySetId(id);
        if (statistic != null) {
            wordSetCollectionStatisticService.deleteWordSetCollectionStatistic(statistic.getId());
        }
                
        // 删除子表
        deleteWordCardByWordSetId(id);
    }

    private void validateWordSetExists(Long id, Long userId) {
        if (wordSetMapper.selectOne(WordSetDO::getId, id, WordSetDO::getUserId, userId) == null) {
            throw exception(WORD_SET_NOT_EXISTS);
        }
    }

    @Override
    public WordSetDO getWordSet(Long id) {
        return wordSetMapper.selectById(id);
    }


    @Override
    public AppWordSetTopRespVO resetWord(Long setId, Long userId) {
        WordSetDO wordSetDO = wordSetMapper.selectById(setId);
        if (wordSetDO == null) {
            throw exception(WORD_SET_NOT_EXISTS);
        }
        // 查询用户昵称
        MemberUserRespDTO user = memberUserApi.getUser(wordSetDO.getUserId());
        // 构建响应对象
        AppWordSetTopRespVO respVO = BeanUtils.toBean(wordSetDO, AppWordSetTopRespVO.class);
        if (user != null) {
            respVO.setNickname(user.getNickname());
            respVO.setAvatar(user.getAvatar());
        }
        List<SessionDO> sessionDOS = sessionMapper.selectList(new LambdaQueryWrapper<SessionDO>()
                .eq(SessionDO::getSetId, setId)
                .eq(SessionDO::getUserId, userId)
                .eq(SessionDO::getMode, StudyModeEnum.WORD_CARD.getType())
                .orderByAsc(SessionDO::getStatus)
                .orderByDesc(SessionDO::getCreateTime));
        SessionDO sessionDO = null;
        if (CollUtil.isNotEmpty(sessionDOS)) {
            sessionDO = sessionDOS.get(0);
        }
        if (sessionDO != null) {
            // 删除
            sessionMapper.deleteById(sessionDO.getId());
        }
        // 插入
        sessionDO = new SessionDO();
        sessionDO.setSetId(setId);
        sessionDO.setUserId(userId);
        sessionDO.setMode(StudyModeEnum.WORD_CARD.getType());
        sessionDO.setStatus(SessionStatusEnum.ONGOING.getType());
        sessionMapper.insert(sessionDO);

        respVO.setStatus(sessionDO.getStatus());
        // 查询单词卡记录列表
        respVO.setMasteredWordCardCount(0);
        // 查询单词卡列表
        List<WordCardDO> wordCardDOs = wordCardMapper.selectList(WordCardDO::getWordSetId, setId);
        if (CollUtil.isNotEmpty(wordCardDOs)) {
            // 转换为DTO并填充定义
            List<WordCardDTO> wordCardDTOs = wordCardDOs.stream().map(card -> {
                WordCardDTO dto = BeanUtils.toBean(card, WordCardDTO.class);
                // 查询单词卡定义
                List<WordCardDefinitionDO> definitions = wordCardDefinitionMapper.selectList(
                        new LambdaQueryWrapper<WordCardDefinitionDO>()
                                .eq(WordCardDefinitionDO::getWordCardId, card.getId()));
                dto.setDefinitions(definitions);

                return dto;
            }).collect(Collectors.toList());

            respVO.setWordCards(wordCardDTOs);
            respVO.setAllWordCardCount(CollUtil.isEmpty(wordCardDOs) ? 0 : wordCardDOs.size());
        }
        return respVO;
    }

    @Override
    public void setTopWordSet(Long setId, Long userId) {
        WordSetTopDO wordSetTopDO = wordSetTopMapper.selectOne(WordSetTopDO::getUserId, userId);
        if (wordSetTopDO == null) {
            wordSetTopDO = new WordSetTopDO();
            wordSetTopDO.setUserId(userId);
            wordSetTopDO.setSetId(setId);
            wordSetTopMapper.insert(wordSetTopDO);
        } else {
            wordSetTopDO.setSetId(setId);
            wordSetTopMapper.updateById(wordSetTopDO);
        }
    }

    @Override
    public AppWordSetTopRespVO getTopWordSet(Long userId) {
        WordSetTopDO wordSetTopDO = wordSetTopMapper.selectOne(WordSetTopDO::getUserId, userId);
        if (wordSetTopDO == null) {
            return null;
        }
        // 查询学习集基本信息
        Long setId = wordSetTopDO.getSetId();
        WordSetDO wordSetDO = wordSetMapper.selectById(setId);
        if (wordSetDO == null) {
            throw exception(WORD_SET_NOT_EXISTS);
        }
        // 查询用户昵称
        MemberUserRespDTO user = memberUserApi.getUser(wordSetDO.getUserId());
        // 构建响应对象
        AppWordSetTopRespVO respVO = BeanUtils.toBean(wordSetDO, AppWordSetTopRespVO.class);
        if (user != null) {
            respVO.setNickname(user.getNickname());
            respVO.setAvatar(user.getAvatar());
        }
        List<SessionDO> sessionDOS = sessionMapper.selectList(new LambdaQueryWrapper<SessionDO>()
                .eq(SessionDO::getSetId, setId)
                .eq(SessionDO::getUserId, userId)
                .eq(SessionDO::getMode, StudyModeEnum.WORD_CARD.getType())
                .orderByAsc(SessionDO::getStatus)
                .orderByDesc(SessionDO::getCreateTime));
        SessionDO sessionDO = null;
        if (CollUtil.isNotEmpty(sessionDOS)) {
            sessionDO = sessionDOS.get(0);
        }
        if (sessionDO == null) {
            // 插入
            sessionDO = new SessionDO();
            sessionDO.setSetId(setId);
            sessionDO.setUserId(userId);
            sessionDO.setMode(StudyModeEnum.WORD_CARD.getType());
            sessionDO.setStatus(SessionStatusEnum.ONGOING.getType());
            sessionMapper.insert(sessionDO);
        }
        respVO.setStatus(sessionDO.getStatus());
        // 查询单词卡记录列表
        List<SessionCardRecordDO> sessionCardRecordDOS = sessionCardRecordMapper
                .selectList(SessionCardRecordDO::getSessionId, sessionDO.getId(), SessionCardRecordDO::getStatus, WordCardStatusEnum.MASTERED.getType());
        List<Long> wordCardIds = sessionCardRecordDOS.stream().map(SessionCardRecordDO::getWordCardId).toList();
        if (CollUtil.isNotEmpty(wordCardIds)) {
            List<SessionCardRecordDTO> cardRecords = new ArrayList<>();
            // 获取关联的单词卡列表
            List<WordCardDO> wordCardList = wordCardMapper.selectList(WordCardDO::getId, wordCardIds);
            List<WordCardDefinitionDO> definitionList = wordCardDefinitionMapper.selectList(WordCardDefinitionDO::getWordCardId, wordCardIds);
            // 转换为DTO
            for (SessionCardRecordDO sessionCardRecordDO : sessionCardRecordDOS) {
                SessionCardRecordDTO sessionCardRecordDTO = BeanUtils.toBean(sessionCardRecordDO, SessionCardRecordDTO.class);
                WordCardDTO wordCardDTO = BeanUtils.toBean(wordCardList.stream()
                        .filter(wordCardDO -> wordCardDO.getId().equals(sessionCardRecordDO.getWordCardId()))
                        .findFirst().orElse(null), WordCardDTO.class);
                wordCardDTO.setDefinitions(definitionList.stream()
                        .filter(wordCardDefinitionDO -> wordCardIds.contains(wordCardDefinitionDO.getWordCardId()))
                        .map(wordCardDefinitionDO -> BeanUtils.toBean(wordCardDefinitionDO, WordCardDefinitionDO.class))
                        .collect(Collectors.toList()));
                sessionCardRecordDTO.setWord(wordCardDTO.getWord());
                sessionCardRecordDTO.setDefinitions(wordCardDTO.getDefinitions());
                cardRecords.add(sessionCardRecordDTO);
            }
            respVO.setCardRecords(cardRecords);
            respVO.setMasteredWordCardCount(wordCardIds.size());
        } else {
            respVO.setMasteredWordCardCount(0);
        }
        // 查询单词卡列表
        List<WordCardDO> wordCardDOs = wordCardMapper.selectList(WordCardDO::getWordSetId, setId);
        if (CollUtil.isNotEmpty(wordCardDOs)) {
            // 转换为DTO并填充定义
            List<WordCardDTO> wordCardDTOs = wordCardDOs.stream().map(card -> {
                WordCardDTO dto = BeanUtils.toBean(card, WordCardDTO.class);
                // 查询单词卡定义
                List<WordCardDefinitionDO> definitions = wordCardDefinitionMapper.selectList(
                        new LambdaQueryWrapper<WordCardDefinitionDO>()
                                .eq(WordCardDefinitionDO::getWordCardId, card.getId()));
                dto.setDefinitions(definitions);

                return dto;
            }).collect(Collectors.toList());

            respVO.setWordCards(wordCardDTOs);
            respVO.setAllWordCardCount(wordCardDOs.size());
        } else {
            respVO.setAllWordCardCount(0);
        }

        return respVO;
    }

    @Override
    public AppWordSetRespVO getWordSetWithInfo(Long id) {
        // 查询学习集基本信息
        WordSetDO wordSetDO = wordSetMapper.selectById(id);
        if (wordSetDO == null) {
            throw exception(WORD_SET_NOT_EXISTS);
        }
        // 查询用户昵称
        MemberUserRespDTO user = memberUserApi.getUser(wordSetDO.getUserId());
        // 构建响应对象
        AppWordSetRespVO respVO = BeanUtils.toBean(wordSetDO, AppWordSetRespVO.class);
        if (user != null) {
            respVO.setNickname(user.getNickname());
        }
        String creatorQuery = CommonUtils.getCreatorQuery(WebFrameworkUtils.getLoginUserId(), WebFrameworkUtils.getLoginUserType());
        LambdaQueryWrapperX<WordSetCollectionDO> queryWrapperX = new LambdaQueryWrapperX<WordSetCollectionDO>()
                .eq(WordSetCollectionDO::getSetId, id)
                .likeIfPresent(WordSetCollectionDO::getCreator, creatorQuery);
        List<WordSetCollectionDO> wordSetCollectionDOS = wordSetCollectionMapper.selectList(queryWrapperX);
        WordSetCollectionDO wordSetCollectionDO = CollUtil.getFirst(wordSetCollectionDOS);
        if (wordSetCollectionDO != null) {
            respVO.setIsStore(IsStoreStatusEnum.YES.getType());
        } else {
            respVO.setIsStore(IsStoreStatusEnum.NO.getType());
        }
        // 查询单词卡列表
        List<WordCardDO> wordCardDOs = wordCardMapper.selectList(
                new LambdaQueryWrapper<WordCardDO>()
                        .eq(WordCardDO::getWordSetId, id));
        // 转换为DTO并填充定义
        List<WordCardDTO> wordCardDTOs = wordCardDOs.stream().map(card -> {
            WordCardDTO dto = BeanUtils.toBean(card, WordCardDTO.class);
            // 查询单词卡定义
            List<WordCardDefinitionDO> definitions = wordCardDefinitionMapper.selectList(
                    new LambdaQueryWrapper<WordCardDefinitionDO>()
                            .eq(WordCardDefinitionDO::getWordCardId, card.getId()));
            dto.setDefinitions(definitions);

            return dto;
        }).collect(Collectors.toList());

        // 设置单词卡列表
        respVO.setWordCards(wordCardDTOs);

        // 设置学习集下关联的班级ID列表
        LambdaQueryWrapper<MemberGroupWordSetDO> wrapper = new LambdaQueryWrapper<MemberGroupWordSetDO>()
                .eq(MemberGroupWordSetDO::getWordSetId, id);
        List<MemberGroupWordSetDO> memberGroupWordSetDOS = memberGroupWordSetMapper.selectList(wrapper);
        if (CollUtil.isNotEmpty(memberGroupWordSetDOS)) {
            List<Long> classIds = memberGroupWordSetDOS.stream().map(MemberGroupWordSetDO::getGroupId).toList();
            respVO.setClassIds(classIds);
        }

        return respVO;
    }

    @Override
    public List<WordSetDO> getWordSetList(Collection<Long> ids) {
        if (ids == null || ids.isEmpty()) {
            return new ArrayList<>();
        }
        return wordSetMapper.selectList(new LambdaQueryWrapperX<WordSetDO>().in(WordSetDO::getId, ids));
    }

    @Override
    public PageResult<WordSetDTO> getWordSetPage(AppWordSetPageReqVO pageReqVO) {
        return wordSetMapperX.selectPage(pageReqVO);
    }

    @Override
    public PageResult<WordSetDTO> getFavoriteWordSetPage(AppWordSetFavoritePageReqVO pageReqVO) {
        return wordSetMapperX.getFavoriteWordSetPage(pageReqVO);
    }

    @Override
    public PageResult<WordSetDTO> getClassWordSetPage(AppWordSetClassPageReqVO pageReqVO) {
        return wordSetMapperX.getClassWordSetPage(pageReqVO);
    }

    @Override
    public List<WordCardDO> getWordCardListByWordSetId(Long wordSetId) {
        return wordCardMapper.selectListByWordSetId(wordSetId);
    }

    private void createWordCardList(Long wordSetId, List<WordCardDTO> list) {
        // 列表为空，直接返回
        if (CollUtil.isEmpty(list)) {
            return;
        }
        // 单词卡词义列表
        List<WordCardDefinitionDO> wordCardDefinitionDOS = new ArrayList<>();
        // 循环获取单词卡内容
        for (WordCardDTO wordCardDTO : list) {
            // 单词卡
            WordCardDO wordCardDO = BeanUtils.toBean(wordCardDTO, WordCardDO.class);
            wordCardDO.setWordSetId(wordSetId);
            wordCardMapper.insert(wordCardDO);
            // 插入后的单词卡ID，设置到单词卡对象中
            wordCardDTO.setId(wordCardDO.getId());
            // 单词卡词义
            List<WordCardDefinitionDO> wordCardDefinitions = wordCardDTO.getDefinitions();
            // 如果列表为空，直接返回
            if (CollUtil.isEmpty(wordCardDefinitions)) {
                continue;
            }
            wordCardDefinitions.forEach(o -> o.setWordSetId(wordSetId).setWordCardId(wordCardDO.getId()));
            wordCardDefinitionDOS.addAll(wordCardDefinitions);
        }
        wordCardDefinitionMapper.insertBatch(wordCardDefinitionDOS);
    }

    private void deleteWordCardByWordSetId(Long wordSetId) {
        // 删除单词卡
        wordCardMapper.deleteByWordSetId(wordSetId);
        // 删除单词卡词义
        wordCardDefinitionMapper.delete(new LambdaQueryWrapperX<WordCardDefinitionDO>()
                .eq(WordCardDefinitionDO::getWordSetId, wordSetId));
        // 删除题目
        List<QuestionDO> questionDOS = questionMapper.selectList(new LambdaQueryWrapperX<QuestionDO>()
                .eq(QuestionDO::getSetId, wordSetId));
        List<Long> questionIds = questionDOS.stream().map(QuestionDO::getId).toList();
        if (CollUtil.isNotEmpty(questionIds)) {
            questionMapper.deleteByIds(questionIds);
            // 删除题目选项
            questionOptionMapper.delete(new LambdaQueryWrapperX<QuestionOptionDO>()
                    .in(QuestionOptionDO::getQuestionId, questionIds));
        }
    }

    /**
     * 审核单词集内容
     */
    private void auditWordSetContent(AppWordSetRespVO wordSetRespVO, Long userId) {
        // 构建审核请求
        AiAuditorsReqDTO auditorsReqDTO = new AiAuditorsReqDTO()
                .setTitle(wordSetRespVO.getTitle())
                .setWordCards(buildWordCardsForAudit(wordSetRespVO.getWordCards()));

        // 调用审核API
        AiAuditorsRespDTO auditorsRespDTO = aiAuditorsApi.audit(auditorsReqDTO, userId);

        // 判断审核结果
        if (auditorsRespDTO.getSensitiveData() != null && auditorsRespDTO.getSensitiveData().length > 0) {
            throw exception(WORD_SET_CONTENT_SENSITIVE, auditorsRespDTO.getMessage());
        }
    }

    /**
     * 构建审核用的单词卡列表（限制前10条）
     *
     * @return 审核用的单词卡列表
     */
    private List<AiAuditorsReqDTO.WordCard> buildWordCardsForAudit(List<WordCardDTO> wordCardDTOs) {
        if (CollUtil.isEmpty(wordCardDTOs)) {
            return new ArrayList<>();
        }

        // 限制处理前10条数据
        List<WordCardDTO> limitedWordCards = CollUtil.sub(wordCardDTOs, 0, Math.min(wordCardDTOs.size(), 10));

        List<AiAuditorsReqDTO.WordCard> wordCards = new ArrayList<>();
        for (WordCardDTO wordCardDTO : limitedWordCards) {
            if (CollUtil.isNotEmpty(wordCardDTO.getDefinitions())) {
                for (WordCardDefinitionDO definition : wordCardDTO.getDefinitions()) {
                    wordCards.add(new AiAuditorsReqDTO.WordCard()
                            .setWord(wordCardDTO.getWord())
                            .setTranslation(definition.getDefinition()));
                }
            } else {
                wordCards.add(new AiAuditorsReqDTO.WordCard()
                        .setWord(wordCardDTO.getWord()));
            }
        }

        return wordCards;
    }
}
