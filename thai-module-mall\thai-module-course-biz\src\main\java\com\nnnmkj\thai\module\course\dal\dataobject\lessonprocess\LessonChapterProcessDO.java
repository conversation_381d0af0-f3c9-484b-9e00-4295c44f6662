package com.nnnmkj.thai.module.course.dal.dataobject.lessonprocess;

import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.nnnmkj.thai.framework.mybatis.core.dataobject.BaseDO;
import lombok.*;

import java.math.BigDecimal;

/**
 * 课程章节进度 DO
 *
 * <AUTHOR>
 */
@TableName("course_lesson_chapter_process")
@KeySequence("course_lesson_chapter_process_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class LessonChapterProcessDO extends BaseDO {

    /**
     * 编号
     */
    @TableId
    private Long id;
    /**
     * 课程ID
     */
    private Long courseId;
    /**
     * 章节ID
     */
    private Long chapterId;
    /**
     * 用户ID
     */
    private Long userId;
    /**
     * 任务点总数
     */
    private Integer allCount;
    /**
     * 已完成数量
     */
    private Integer completedCount;
    /**
     * 未完成数量
     */
    private Integer notCompletedCount;
    /**
     * 当前附件ID
     */
    private Long currentAttachmentId;
    /**
     * 学习进度
     */
    private BigDecimal progressPercentage;
    /**
     * 状态
     *
     * 枚举 {@link TODO lesson_process_status 对应的类}
     */
    private Integer status;
    
    /**
     * 章节名称
     */
    @TableField(exist = false)
    private String chapterName;

}