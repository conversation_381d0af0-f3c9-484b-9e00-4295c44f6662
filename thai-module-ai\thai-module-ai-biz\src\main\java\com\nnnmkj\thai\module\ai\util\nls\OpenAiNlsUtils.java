package com.nnnmkj.thai.module.ai.util.nls;

import com.nnnmkj.thai.module.ai.api.nls.vo.AiNlsTtsConfigVO;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.ai.openai.OpenAiAudioSpeechModel;
import org.springframework.ai.openai.OpenAiAudioSpeechOptions;
import org.springframework.ai.openai.api.OpenAiAudioApi;
import org.springframework.ai.openai.audio.speech.SpeechPrompt;
import org.springframework.ai.openai.audio.speech.SpeechResponse;
import org.springframework.stereotype.Component;

/**
 * OpenAI语音合成工具类
 */
@Component
@Slf4j
public class OpenAiNlsUtils {

    @Resource
    private OpenAiAudioSpeechModel openAiAudioSpeechModel;

    /**
     * 文字转语音（返回音频流）
     *
     * @param configVO 配置VO
     * @return 音频字节数组（MP3格式）
     */
    public byte[] processTTS(@Valid AiNlsTtsConfigVO configVO) {
        return this.processTTS(configVO.getText(), configVO.getSpeaker(), configVO.getSpeechRate(), configVO.getPitchRate(), configVO.getDisplayCaptions());
    }

    /**
     * 文字转语音（返回音频流）
     *
     * @param text            文本内容
     * @param speaker         发音人（OpenAI支持：alloy, echo, fable, onyx, nova, shimmer等）
     * @param speechRate      语速（-500~500，OpenAI使用0.25~4.0，这里会进行转换）
     * @param pitchRate       语调（-500~500，OpenAI不直接支持，通过instructions参数模拟）
     * @param displayCaptions 是否开启字幕（OpenAI不支持，忽略此参数）
     * @return 音频字节数组（MP3格式）
     */
    public byte[] processTTS(String text, String speaker, Integer speechRate, Integer pitchRate, Boolean displayCaptions) {
        try {
            // 将阿里云的语速范围(-500~500)转换为OpenAI的语速范围(0.25~4.0)
            Float speed = convertSpeechRate(speechRate);
            
            // 如果text文件超过4096个token，自动截取
            if (text.length() > 4000) {
                text = text.substring(0, 4000);
                log.warn("文本长度超过4000字符，已自动截取");
            }

            // 构建Spring AI的OpenAI音频选项
            OpenAiAudioSpeechOptions options = OpenAiAudioSpeechOptions.builder()
                    .model(OpenAiAudioApi.TtsModel.TTS_1.getValue()) // 使用标准TTS模型
                    .voice(OpenAiAudioApi.SpeechRequest.Voice.valueOf(mapVoice(speaker).toUpperCase())) // 映射发音人
                    .responseFormat(OpenAiAudioApi.SpeechRequest.AudioResponseFormat.MP3) // 输出MP3格式
                    .speed(speed)
                    .build();
            
            // 创建语音提示
            SpeechPrompt speechPrompt = new SpeechPrompt(text, options);
            
            // 调用Spring AI的OpenAI TTS API
            SpeechResponse response = openAiAudioSpeechModel.call(speechPrompt);
            byte[] audioData = response.getResult().getOutput();
            
            return audioData;
            
        } catch (Exception e) {
            log.error("OpenAI语音合成失败", e);
            throw new RuntimeException("OpenAI语音合成失败: " + e.getMessage(), e);
        }
    }

    /**
     * 将阿里云的语速范围(-500~500)转换为OpenAI的语速范围(0.25~4.0)
     *
     * @param speechRate 阿里云语速值
     * @return OpenAI语速值
     */
    private Float convertSpeechRate(Integer speechRate) {
        if (speechRate == null) {
            return 1.0f; // 默认语速
        }
        
        // 将-500~500映射到0.25~4.0
        // -500 -> 0.25, 0 -> 1.0, 500 -> 4.0
        double normalizedRate = (speechRate + 500) / 1000.0; // 0.0~1.0
        return (float) (0.25 + normalizedRate * 3.75); // 0.25~4.0
    }





    /**
     * 映射发音人名称
     * 将阿里云的发音人映射到OpenAI支持的发音人
     *
     * @param speaker 阿里云发音人
     * @return OpenAI发音人
     */
    private String mapVoice(String speaker) {
        // 默认使用alloy音色
        if (speaker == null || speaker.isEmpty()) {
            return "alloy";
        }
        
        // 根据阿里云发音人名称映射到OpenAI发音人
        // 这里只是简单示例，可以根据实际需求进行更复杂的映射
        switch (speaker.toLowerCase()) {
            case "xiaoyun": // 阿里云女声
            case "xiaogang": // 阿里云男声
                return "alloy"; // 中性声音
            case "ruoxi": // 阿里云女声
                return "nova"; // 女声
            case "siqi": // 阿里云女声
                return "shimmer"; // 女声
            case "sijia": // 阿里云女声
                return "echo"; // 女声
            case "sicheng": // 阿里云男声
            case "maoke": // 阿里云男声
                return "onyx"; // 男声
            case "ninger": // 阿里云童声
                return "fable"; // 较年轻声音
            default:
                return "alloy"; // 默认使用alloy
        }
    }
}