package com.nnnmkj.thai.module.course.service.lessonchapter;

import com.nnnmkj.thai.framework.common.pojo.PageResult;
import com.nnnmkj.thai.module.course.controller.admin.lessonchapter.vo.LessonChapterPageReqVO;
import com.nnnmkj.thai.module.course.controller.admin.lessonchapter.vo.LessonChapterSaveReqVO;
import com.nnnmkj.thai.module.course.dal.dataobject.lessonchapter.LessonChapterDO;
import com.nnnmkj.thai.module.course.dal.dataobject.lessonchapter.LessonChatperAttachmentDO;
import jakarta.validation.Valid;

import java.util.Collection;
import java.util.List;

/**
 * 课程章节 Service 接口
 *
 * <AUTHOR>
 */
public interface LessonChapterService {

    /**
     * 创建课程章节
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createLessonChapter(@Valid LessonChapterSaveReqVO createReqVO);

    /**
     * 更新课程章节
     *
     * @param updateReqVO 更新信息
     */
    void updateLessonChapter(@Valid LessonChapterSaveReqVO updateReqVO);

    /**
     * 删除课程章节
     *
     * @param id 编号
     */
    void deleteLessonChapter(Long id);

    /**
     * 获得课程章节
     *
     * @param id 编号
     * @return 课程章节
     */
    LessonChapterDO getLessonChapter(Long id);

    /**
     * 获得课程章节分页
     *
     * @param pageReqVO 分页查询
     * @return 课程章节分页
     */
    PageResult<LessonChapterDO> getLessonChapterPage(LessonChapterPageReqVO pageReqVO);

    /**
     * 获得顶级课程章节分页（父章节ID为null或为0）
     *
     * @param pageReqVO 分页查询
     * @return 课程章节分页
     */
    PageResult<LessonChapterDO> getTopLessonChapterPage(LessonChapterPageReqVO pageReqVO);

    /**
     * 获得课程章节列表
     *
     * @param ids 学习集的数组
     * @return 学习集列表
     */
    List<LessonChapterDO> getLessonChapterList(Collection<Long> ids);

    /**
     * 根据父章节ID获取子章节列表
     *
     * @param parentId 父章节ID
     * @return 子章节列表
     */
    List<LessonChapterDO> getLessonChapterListByParentId(Long parentId);

    /**
     * 根据课程ID获取课程章节列表
     *
     * @param courseId 课程ID
     * @return 课程章节列表
     */
    List<LessonChapterDO> getLessonChapterListByCourseId(Long courseId);

    // ==================== 子表（课程章节附件） ====================

    /**
     * 获得课程章节附件列表
     *
     * @param chapterId 章节ID
     * @return 课程章节附件列表
     */
    List<LessonChatperAttachmentDO> getLessonChatperAttachmentListByChapterId(Long chapterId);

}