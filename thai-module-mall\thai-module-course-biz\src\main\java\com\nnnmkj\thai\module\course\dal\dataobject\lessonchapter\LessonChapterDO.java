package com.nnnmkj.thai.module.course.dal.dataobject.lessonchapter;

import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.nnnmkj.thai.framework.mybatis.core.dataobject.BaseDO;
import lombok.*;

/**
 * 课程章节 DO
 *
 * <AUTHOR>
 */
@TableName("course_lesson_chapter")
@KeySequence("course_lesson_chapter_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class LessonChapterDO extends BaseDO {

    /**
     * 编号
     */
    @TableId
    private Long id;
    /**
     * 课程ID
     */
    private Long courseId;
    /**
     * 父章节ID
     */
    private Long parentId;
    /**
     * 章节名称
     */
    private String name;
    /**
     * 章节简介
     */
    private String blurb;
    /**
     * 排序
     */
    private Integer sort;

}