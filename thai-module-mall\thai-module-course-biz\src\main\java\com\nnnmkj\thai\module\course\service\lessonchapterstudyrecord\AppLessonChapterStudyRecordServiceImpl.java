package com.nnnmkj.thai.module.course.service.lessonchapterstudyrecord;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.nnnmkj.thai.framework.common.pojo.PageResult;
import com.nnnmkj.thai.module.course.controller.app.lessonchapterstudyrecord.vo.AppLessonChapterStudyRecordPageReqVO;
import com.nnnmkj.thai.module.course.controller.app.lessonchapterstudyrecord.vo.AppLessonChapterStudyRecordSaveReqVO;
import com.nnnmkj.thai.module.course.dal.dataobject.lessonchapter.LessonChapterDO;
import com.nnnmkj.thai.module.course.dal.dataobject.lessonchapterstudyrecord.LessonChapterStudyRecordDO;
import com.nnnmkj.thai.module.course.dal.mysql.lessonchapterstudyrecord.LessonChapterStudyRecordMapper;
import com.nnnmkj.thai.module.course.dal.mysql.lessonstudystatistic.LessonStudyStatisticMapper;
import com.nnnmkj.thai.module.course.service.lessonchapter.AppLessonChapterService;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import static com.nnnmkj.thai.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.nnnmkj.thai.module.course.enums.ErrorCodeConstants.LESSON_CHAPTER_NOT_EXISTS;
import static com.nnnmkj.thai.module.course.enums.ErrorCodeConstants.LESSON_CHAPTER_STUDY_RECORD_NOT_EXISTS;

/**
 * 课程章节学习记录 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class AppLessonChapterStudyRecordServiceImpl implements AppLessonChapterStudyRecordService {

    @Resource
    private LessonChapterStudyRecordMapper lessonChapterStudyRecordMapper;
    
    @Resource
    private AppLessonChapterService lessonChapterService;

    @Resource
    private LessonStudyStatisticMapper lessonStudyStatisticMapper;

    @Override
    public Long createLessonChapterStudyRecord(AppLessonChapterStudyRecordSaveReqVO createReqVO) {
        // 1. 通过章节ID获取章节信息
        LessonChapterDO chapter = lessonChapterService.getLessonChapter(createReqVO.getChapterId());
        if (chapter == null) {
            throw exception(LESSON_CHAPTER_NOT_EXISTS);
        }

        // 2. 设置课程ID
        createReqVO.setCourseId(chapter.getCourseId());
        
        // 3. 检查是否已存在相同用户、章节和附件的记录
        LessonChapterStudyRecordDO existingRecord = lessonChapterStudyRecordMapper.selectOne(
            new LambdaQueryWrapper<LessonChapterStudyRecordDO>()
                .eq(LessonChapterStudyRecordDO::getUserId, createReqVO.getUserId())
                .eq(LessonChapterStudyRecordDO::getChapterId, createReqVO.getChapterId())
                .eq(LessonChapterStudyRecordDO::getAttachmentId, createReqVO.getAttachmentId())
        );
        
        if (existingRecord != null) {
            // 如果记录已存在，则更新记录
            LessonChapterStudyRecordDO updateRecord = new LessonChapterStudyRecordDO();
            updateRecord.setId(existingRecord.getId());
            updateRecord.setStatus(createReqVO.getStatus());
            updateRecord.setProgressPercentage(createReqVO.getProgressPercentage());
            lessonChapterStudyRecordMapper.updateById(updateRecord);
            // 更新学习统计
            lessonStudyStatisticMapper.atomicUpdateCount(updateRecord.getCourseId(), 1);
            return existingRecord.getId();
        }
        
        // 4. 创建新记录
        LessonChapterStudyRecordDO record = new LessonChapterStudyRecordDO();
        record.setChapterId(createReqVO.getChapterId());
        record.setCourseId(createReqVO.getCourseId());
        record.setAttachmentId(createReqVO.getAttachmentId());
        record.setUserId(createReqVO.getUserId());
        record.setStatus(createReqVO.getStatus());
        record.setProgressPercentage(createReqVO.getProgressPercentage());
        
        // 5. 保存记录
        lessonChapterStudyRecordMapper.insert(record);
        // 更新学习统计
        lessonStudyStatisticMapper.atomicUpdateCount(record.getCourseId(), 1);
        return record.getId();
    }

    @Override
    public void updateLessonChapterStudyRecord(AppLessonChapterStudyRecordSaveReqVO updateReqVO) {
        // 1. 校验记录是否存在
        LessonChapterStudyRecordDO record = lessonChapterStudyRecordMapper.selectById(updateReqVO.getId());
        if (record == null) {
            throw exception(LESSON_CHAPTER_STUDY_RECORD_NOT_EXISTS);
        }
        
        // 2. 校验章节是否存在
        LessonChapterDO chapter = lessonChapterService.getLessonChapter(updateReqVO.getChapterId());
        if (chapter == null) {
            throw exception(LESSON_CHAPTER_NOT_EXISTS);
        }
        
        // 3. 更新记录
        LessonChapterStudyRecordDO updateRecord = new LessonChapterStudyRecordDO();
        updateRecord.setId(updateReqVO.getId());
        updateRecord.setChapterId(updateReqVO.getChapterId());
        updateRecord.setCourseId(chapter.getCourseId());
        updateRecord.setAttachmentId(updateReqVO.getAttachmentId());
        updateRecord.setUserId(updateReqVO.getUserId());
        updateRecord.setStatus(updateReqVO.getStatus());
        updateRecord.setProgressPercentage(updateReqVO.getProgressPercentage());
        
        lessonChapterStudyRecordMapper.updateById(updateRecord);
    }

    @Override
    public void deleteLessonChapterStudyRecord(Long id) {
        // 校验记录是否存在
        LessonChapterStudyRecordDO record = lessonChapterStudyRecordMapper.selectById(id);
        if (record == null) {
            throw exception(LESSON_CHAPTER_STUDY_RECORD_NOT_EXISTS);
        }
        lessonChapterStudyRecordMapper.deleteById(id);
    }

    @Override
    public LessonChapterStudyRecordDO getLessonChapterStudyRecord(Long id) {
        return lessonChapterStudyRecordMapper.selectById(id);
    }

    @Override
    public PageResult<LessonChapterStudyRecordDO> getLessonChapterStudyRecordPage(AppLessonChapterStudyRecordPageReqVO pageReqVO) {
        return lessonChapterStudyRecordMapper.selectPage(pageReqVO);
    }

}