package com.nnnmkj.thai.module.course.service.lessonchapterstudyrecord;

import com.nnnmkj.thai.framework.common.pojo.PageResult;
import com.nnnmkj.thai.framework.common.util.object.BeanUtils;
import com.nnnmkj.thai.module.course.controller.admin.lessonchapterstudyrecord.vo.LessonChapterStudyRecordPageReqVO;
import com.nnnmkj.thai.module.course.controller.admin.lessonchapterstudyrecord.vo.LessonChapterStudyRecordSaveReqVO;
import com.nnnmkj.thai.module.course.dal.dataobject.lessonchapterstudyrecord.LessonChapterStudyRecordDO;
import com.nnnmkj.thai.module.course.dal.mysql.lessonchapterstudyrecord.LessonChapterStudyRecordMapper;
import com.nnnmkj.thai.module.course.dal.mysql.lessonstudystatistic.LessonStudyStatisticMapper;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import java.util.List;

import static com.nnnmkj.thai.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.nnnmkj.thai.module.course.enums.ErrorCodeConstants.LESSON_CHAPTER_STUDY_RECORD_NOT_EXISTS;

/**
 * 课程章节学习记录 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class LessonChapterStudyRecordServiceImpl implements LessonChapterStudyRecordService {

    @Resource
    private LessonChapterStudyRecordMapper lessonChapterStudyRecordMapper;

    @Resource
    private LessonStudyStatisticMapper lessonStudyStatisticMapper;

    @Override
    public Long createLessonChapterStudyRecord(LessonChapterStudyRecordSaveReqVO createReqVO) {
        // 插入
        LessonChapterStudyRecordDO lessonChapterStudyRecord = BeanUtils.toBean(createReqVO, LessonChapterStudyRecordDO.class);
        lessonChapterStudyRecordMapper.insert(lessonChapterStudyRecord);
        // 更新学习统计
        lessonStudyStatisticMapper.atomicUpdateCount(lessonChapterStudyRecord.getCourseId(), 1);
        // 返回
        return lessonChapterStudyRecord.getId();
    }

    @Override
    public void updateLessonChapterStudyRecord(LessonChapterStudyRecordSaveReqVO updateReqVO) {
        // 校验存在
        validateLessonChapterStudyRecordExists(updateReqVO.getId());
        // 更新
        LessonChapterStudyRecordDO updateObj = BeanUtils.toBean(updateReqVO, LessonChapterStudyRecordDO.class);
        // 更新学习统计
        lessonStudyStatisticMapper.atomicUpdateCount(updateObj.getCourseId(), 1);
        lessonChapterStudyRecordMapper.updateById(updateObj);
    }

    @Override
    public void deleteLessonChapterStudyRecord(Long id) {
        // 校验存在
        validateLessonChapterStudyRecordExists(id);
        // 删除
        lessonChapterStudyRecordMapper.deleteById(id);
    }

    private void validateLessonChapterStudyRecordExists(Long id) {
        if (lessonChapterStudyRecordMapper.selectById(id) == null) {
            throw exception(LESSON_CHAPTER_STUDY_RECORD_NOT_EXISTS);
        }
    }

    @Override
    public LessonChapterStudyRecordDO getLessonChapterStudyRecord(Long id) {
        return lessonChapterStudyRecordMapper.selectById(id);
    }

    @Override
    public PageResult<LessonChapterStudyRecordDO> getLessonChapterStudyRecordPage(LessonChapterStudyRecordPageReqVO pageReqVO) {
        return lessonChapterStudyRecordMapper.selectPage(pageReqVO);
    }
    
    @Override
    public List<LessonChapterStudyRecordDO> getAllLessonChapterStudyRecords() {
        // 使用selectList方法获取所有记录
        return lessonChapterStudyRecordMapper.selectList();
    }
}