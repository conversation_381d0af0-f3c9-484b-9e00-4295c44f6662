package com.nnnmkj.thai.module.course.controller.app.membergrouplesson;

import com.nnnmkj.thai.framework.common.pojo.CommonResult;
import com.nnnmkj.thai.framework.security.core.util.SecurityFrameworkUtils;
import com.nnnmkj.thai.module.course.controller.app.membergrouplesson.vo.*;
import com.nnnmkj.thai.module.course.service.membergrouplesson.AppMemberGroupLessonService;
import com.nnnmkj.thai.module.member.api.user.MemberUserApi;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

import static com.nnnmkj.thai.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.nnnmkj.thai.framework.common.pojo.CommonResult.success;
import static com.nnnmkj.thai.module.course.enums.ErrorCodeConstants.USER_NOT_IN_CLASS;
import static com.nnnmkj.thai.module.course.enums.ErrorCodeConstants.USER_NOT_IN_CURRENT_CLASS_GROUP;

@Tag(name = "用户 APP - 班级-课程关联")
@RestController
@RequestMapping("/course/member-group-lesson")
@Validated
public class AppMemberGroupLessonController {

    @Resource
    private AppMemberGroupLessonService memberGroupLessonService;
    
    @Resource
    private MemberUserApi memberUserApi;

    @GetMapping("/course-classes")
    @Operation(summary = "获取课程关联的班级列表")
    @Parameter(name = "courseId", description = "课程编号", required = true)
    public CommonResult<List<AppMemberGroupLessonRespVO>> getClassesByCourseId(@RequestParam("courseId") Long courseId) {
        List<AppMemberGroupLessonRespVO> list = memberGroupLessonService.getClassesWithDetailsByCourseId(courseId);
        return success(list);
    }
    
    @GetMapping("/search")
    @Operation(summary = "搜索课程关联的班级列表")
    public CommonResult<List<AppMemberGroupLessonRespVO>> searchCourseClasses(@Validated AppMemberGroupLessonSearchReqVO reqVO) {
        List<AppMemberGroupLessonRespVO> list = memberGroupLessonService.searchClassesWithDetails(reqVO);
        return success(list);
    }
    
    @PostMapping("/create")
    @Operation(summary = "创建班级-课程关联")
    public CommonResult<Long> createMemberGroupLesson(@Valid @RequestBody AppMemberGroupLessonSaveReqVO createReqVO) {
        return success(memberGroupLessonService.createMemberGroupLesson(createReqVO));
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除班级-课程关联")
    @Parameter(name = "id", description = "编号", required = true)
    public CommonResult<Boolean> deleteMemberGroupLesson(@RequestParam("id") Long id) {
        memberGroupLessonService.deleteMemberGroupLesson(id);
        return success(true);
    }
    
    @PostMapping("/batch-share")
    @Operation(summary = "批量分享课程到班级")
    public CommonResult<Integer> batchShareClassCourses(@Valid @RequestBody AppMemberGroupLessonBatchShareReqVO reqVO) {
        Long userId = SecurityFrameworkUtils.getLoginUserId();
        // 验证用户是否在班级中
        List<Long> groupIds = memberUserApi.getGroupIdsById(userId);
        if (groupIds == null || groupIds.isEmpty()) {
            throw exception(USER_NOT_IN_CLASS);
        }
        if (!groupIds.contains(reqVO.getClassId())) {
            throw exception(USER_NOT_IN_CURRENT_CLASS_GROUP);
        }
        
        // 批量创建班级-课程关联
        int count = memberGroupLessonService.batchCreateMemberGroupLesson(reqVO.getCourseIds(), reqVO.getClassId());
        return success(count);
    }
    
    @PostMapping("/batch-delete")
    @Operation(summary = "批量删除班级中的课程")
    public CommonResult<Integer> batchDeleteClassCourses(@Valid @RequestBody AppMemberGroupLessonBatchDeleteReqVO reqVO) {
        Long userId = SecurityFrameworkUtils.getLoginUserId();
        // 验证用户是否在班级中
        List<Long> groupIds = memberUserApi.getGroupIdsById(userId);
        if (groupIds == null || groupIds.isEmpty()) {
            throw exception(USER_NOT_IN_CLASS);
        }
        if (!groupIds.contains(reqVO.getClassId())) {
            throw exception(USER_NOT_IN_CURRENT_CLASS_GROUP);
        }
        
        // 批量删除班级-课程关联
        int count = memberGroupLessonService.batchDeleteMemberGroupLesson(reqVO.getCourseIds(), reqVO.getClassId());
        return success(count);
    }

    @PostMapping("/batch-create-groups")
    @Operation(summary = "批量为课程添加班级（已存在的不重复添加）")
    public CommonResult<Integer> batchCreateGroupsInCourse(@Valid @RequestBody AppMemberGroupLessonBatchCreateGroupsReqVO reqVO) {
        // 批量创建课程-班级关联
        int count = memberGroupLessonService.batchCreateGroupsInCourse(reqVO.getCourseId(), reqVO.getGroupIds());
        return success(count);
    }

    @PostMapping("/batch-delete-groups")
    @Operation(summary = "批量删除课程中的班级")
    public CommonResult<Integer> batchDeleteGroupsInCourse(@Valid @RequestBody AppMemberGroupLessonBatchDeleteGroupsReqVO reqVO) {
        // 批量删除课程-班级关联
        int count = memberGroupLessonService.batchDeleteGroupsInCourse(reqVO.getCourseId(), reqVO.getGroupIds());
        return success(count);
    }
}