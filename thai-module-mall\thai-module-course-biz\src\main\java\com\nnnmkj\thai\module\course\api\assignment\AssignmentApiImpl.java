package com.nnnmkj.thai.module.course.api.assignment;

import com.nnnmkj.thai.module.course.dal.mysql.membergroupassignmentrelease.MemberGroupAssignmentReleaseMapper;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 作业 API 接口实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class AssignmentApiImpl implements AssignmentApi {

    @Resource
    private MemberGroupAssignmentReleaseMapper memberGroupAssignmentReleaseMapper;

    @Override
    public int getAssignmentCount(Long groupId) {
        return memberGroupAssignmentReleaseMapper.selectCountByGroupId(groupId);
    }

    @Override
    public Map<Long, Integer> getAssignmentCountMap(List<Long> groupIds) {
        // 如果分组ID列表为空，返回空的Map
        if (groupIds == null || groupIds.isEmpty()) {
            return new HashMap<>();
        }
        
        // 批量查询各分组下的作业数量
        Map<Long, Integer> countMap = new HashMap<>();
        for (Long groupId : groupIds) {
            int count = getAssignmentCount(groupId);
            countMap.put(groupId, count);
        }
        return countMap;
    }
} 