package com.nnnmkj.thai.module.course.service.assignmentanswerpaper;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.nnnmkj.thai.framework.common.exception.enums.GlobalErrorCodeConstants;
import com.nnnmkj.thai.framework.common.pojo.PageResult;
import com.nnnmkj.thai.framework.common.util.collection.CollectionUtils;
import com.nnnmkj.thai.framework.common.util.object.BeanUtils;
import com.nnnmkj.thai.module.course.controller.admin.assignmentanswerpaper.vo.AssignmentAnswerPaperPageReqVO;
import com.nnnmkj.thai.module.course.controller.admin.assignmentanswerpaper.vo.AssignmentAnswerPaperSaveReqVO;
import com.nnnmkj.thai.module.course.controller.app.assignmentanswerpaper.vo.AppAssignmentAnswerPaperRespVO;
import com.nnnmkj.thai.module.course.controller.app.assignmentanswerpaper.vo.AppAssignmentAnswerPaperRespVO.UserInfo;
import com.nnnmkj.thai.module.course.dal.dataobject.assignmentanswerpaper.AssignmentAnswerPaperDO;
import com.nnnmkj.thai.module.course.dal.dataobject.assignmentrelease.AssignmentReleaseDO;
import com.nnnmkj.thai.module.course.dal.dataobject.assignmentscore.AssignmentScoreDO;
import com.nnnmkj.thai.module.course.dal.dataobject.membergroupassignmentrelease.MemberGroupAssignmentReleaseDO;
import com.nnnmkj.thai.module.course.dal.mysql.assignmentanswerpaper.AssignmentAnswerPaperMapper;
import com.nnnmkj.thai.module.course.enums.AssignmentDistributionTargetEnum;
import com.nnnmkj.thai.module.course.service.assignmentrelease.AssignmentReleaseService;
import com.nnnmkj.thai.module.course.service.assignmentscore.AssignmentScoreService;
import com.nnnmkj.thai.module.course.service.membergroupassignmentrelease.MemberGroupAssignmentReleaseService;
import com.nnnmkj.thai.module.member.api.user.MemberUserApi;
import com.nnnmkj.thai.module.member.api.user.dto.MemberUserRespDTO;
import jakarta.annotation.Resource;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import java.util.*;

import static com.nnnmkj.thai.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.nnnmkj.thai.module.course.enums.ErrorCodeConstants.*;
import static com.nnnmkj.thai.module.member.enums.ErrorCodeConstants.GROUP_NO_USERS;

/**
 * 课程作业作答试卷 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class AssignmentAnswerPaperServiceImpl implements AssignmentAnswerPaperService {

    @Resource
    private AssignmentAnswerPaperMapper assignmentAnswerPaperMapper;

    @Resource
    private MemberGroupAssignmentReleaseService memberGroupAssignmentReleaseService;
    @Resource
    @Lazy
    private AssignmentReleaseService assignmentReleaseService;
    @Resource
    private AssignmentScoreService assignmentScoreService;

    @Resource
    private MemberUserApi memberUserApi;

    @Override
    public Long createAssignmentAnswerPaper(AssignmentAnswerPaperSaveReqVO createReqVO) {
        // 插入
        AssignmentAnswerPaperDO assignmentAnswerPaper = BeanUtils.toBean(createReqVO, AssignmentAnswerPaperDO.class);
        assignmentAnswerPaperMapper.insert(assignmentAnswerPaper);
        // 返回
        return assignmentAnswerPaper.getId();
    }

    @Override
    public void updateAssignmentAnswerPaper(AssignmentAnswerPaperSaveReqVO updateReqVO) {
        // 校验存在
        validateAssignmentAnswerPaperExists(updateReqVO.getId());
        // 更新
        AssignmentAnswerPaperDO updateObj = BeanUtils.toBean(updateReqVO, AssignmentAnswerPaperDO.class);
        assignmentAnswerPaperMapper.updateById(updateObj);
    }

    @Override
    public void deleteAssignmentAnswerPaper(Long id) {
        // 校验存在
        validateAssignmentAnswerPaperExists(id);
        // 删除
        assignmentAnswerPaperMapper.deleteById(id);
    }

    private void validateAssignmentAnswerPaperExists(Long id) {
        if (assignmentAnswerPaperMapper.selectById(id) == null) {
            throw exception(ASSIGNMENT_ANSWER_PAPER_NOT_EXISTS);
        }
    }

    @Override
    public AssignmentAnswerPaperDO getAssignmentAnswerPaper(Long id) {
        return assignmentAnswerPaperMapper.selectById(id);
    }

    @Override
    public AssignmentAnswerPaperDO getAssignmentAnswerPaperLast(Long userId, Long assignmentReleaseId) {
        return assignmentAnswerPaperMapper.selectLimit1(new LambdaQueryWrapper<AssignmentAnswerPaperDO>()
                        .eq(AssignmentAnswerPaperDO::getAssignmentReleaseId, assignmentReleaseId)
                        .eq(AssignmentAnswerPaperDO::getUserId, userId),
                AssignmentAnswerPaperDO::getCreateTime
        );
    }

    @Override
    public List<AssignmentAnswerPaperDO> getAssignmentAnswerPaperListLast(Long userId, Collection<Long> assignmentReleaseIds) {
        return assignmentAnswerPaperMapper.selectListDistinct(
                new LambdaQueryWrapper<AssignmentAnswerPaperDO>()
                        .eq(AssignmentAnswerPaperDO::getUserId, userId)
                        .in(AssignmentAnswerPaperDO::getAssignmentReleaseId, assignmentReleaseIds),
                AssignmentAnswerPaperDO::getCreateTime,
                AssignmentAnswerPaperDO::getAssignmentReleaseId
        );
    }

    @Override
    public List<AssignmentAnswerPaperDO> getAssignmentAnswerPaperListLast(Collection<Long> userIds, Long assignmentReleaseId) {
        return assignmentAnswerPaperMapper.selectListDistinct(
                new LambdaQueryWrapper<AssignmentAnswerPaperDO>()
                        .in(AssignmentAnswerPaperDO::getUserId, userIds)
                        .eq(AssignmentAnswerPaperDO::getAssignmentReleaseId, assignmentReleaseId),
                AssignmentAnswerPaperDO::getCreateTime,
                AssignmentAnswerPaperDO::getUserId
        );
    }

    @Override
    public PageResult<AssignmentAnswerPaperDO> getAssignmentAnswerPaperPage(AssignmentAnswerPaperPageReqVO pageReqVO) {
        return assignmentAnswerPaperMapper.selectPage(pageReqVO);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public AppAssignmentAnswerPaperRespVO getCompletionStatus(Long userId, Long groupId, Long assignmentReleaseId) {
        // 获取班级作业发布关联信息
        MemberGroupAssignmentReleaseDO groupAssignmentRelease = memberGroupAssignmentReleaseService.getMemberGroupAssignmentRelease(groupId, assignmentReleaseId);
        if (groupAssignmentRelease == null) {
            throw exception(ASSIGNMENT_RELEASE_NOT_EXISTS);
        }

        // 获取作业发布详情
        AssignmentReleaseDO assignmentRelease = assignmentReleaseService.getAssignmentRelease(assignmentReleaseId);
        if (assignmentRelease == null) {
            throw exception(ASSIGNMENT_RELEASE_NOT_EXISTS);
        }

        // 根据发布对象确定目标用户
        AssignmentDistributionTargetEnum distributionType = AssignmentDistributionTargetEnum.valueOf(assignmentRelease.getDistributionTarget());
        List<Long> targetUserIds;

        switch (distributionType) {
            case CLASS -> {
                // 班级发布：获取所有班级中的用户
                List<Long> groupIds = groupAssignmentRelease.getGroupIds();
                if (CollUtil.isEmpty(groupIds)) {
                    throw exception(GROUP_NO_USERS);
                }
                List<MemberUserRespDTO> allUsersInGroups = memberUserApi.getUserListByGroupIds(groupIds);
                targetUserIds = CollectionUtils.convertList(allUsersInGroups, MemberUserRespDTO::getId);
            }
            case PERSON -> {
                // 个人发布：直接使用指定的用户ID列表
                targetUserIds = groupAssignmentRelease.getUserIds();
            }
            default -> throw exception(GlobalErrorCodeConstants.BAD_REQUEST);
        }

        if (CollUtil.isEmpty(targetUserIds)) {
            throw exception(ASSIGNMENT_ANSWER_PAPER_TARGET_USER_EMPTY);
        }

        // 查询作答试卷记录
        List<AssignmentAnswerPaperDO> answerPapers = getAssignmentAnswerPaperListLast(targetUserIds, assignmentReleaseId);
        Map<Long, AssignmentAnswerPaperDO> answerPaperMap = CollectionUtils.convertMap(answerPapers, AssignmentAnswerPaperDO::getUserId);

        // 查询成绩记录
        List<AssignmentScoreDO> scores = assignmentScoreService.getAssignmentScoreListLast(assignmentReleaseId, targetUserIds);
        Map<Long, Double> scoreMap = CollUtil.isEmpty(scores) 
            ? new HashMap<>()
            : CollectionUtils.convertMap(scores, AssignmentScoreDO::getUserId, AssignmentScoreDO::getScore);

        // 获取目标用户的详细信息
        List<MemberUserRespDTO> targetUserList = memberUserApi.getUserList(targetUserIds);
        Map<Long, MemberUserRespDTO> userMap = CollectionUtils.convertMap(targetUserList, MemberUserRespDTO::getId);

        // 构建响应数据
        AppAssignmentAnswerPaperRespVO respVO = new AppAssignmentAnswerPaperRespVO();
        List<UserInfo> submittedUsers = new ArrayList<>();
        List<UserInfo> unsubmittedUsers = new ArrayList<>();

        for (Long currentUserId : targetUserIds) {
            MemberUserRespDTO user = userMap.get(currentUserId);
            if (user == null) {
                continue; // 跳过不存在的用户
            }
            
            AssignmentAnswerPaperDO answerPaper = answerPaperMap.get(currentUserId);

            UserInfo userInfo = new UserInfo();
            userInfo.setNickname(user.getNickname());

            if (answerPaper == null || !Boolean.TRUE.equals(answerPaper.getIsSubmit())) {
                unsubmittedUsers.add(userInfo);
            } else {
                userInfo.setSubmissionTime(answerPaper.getUpdateTime());
                userInfo.setScore(scoreMap.getOrDefault(currentUserId, null));
                submittedUsers.add(userInfo);
            }
        }

        respVO.setSubmittedUsers(submittedUsers);
        respVO.setUnsubmittedUsers(unsubmittedUsers);

        return respVO;
    }

}