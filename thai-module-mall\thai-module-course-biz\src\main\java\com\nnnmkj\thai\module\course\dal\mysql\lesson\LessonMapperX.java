package com.nnnmkj.thai.module.course.dal.mysql.lesson;

import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.nnnmkj.thai.framework.common.enums.CommonConstants;
import com.nnnmkj.thai.framework.common.pojo.PageResult;
import com.nnnmkj.thai.framework.common.util.common.CommonUtils;
import com.nnnmkj.thai.framework.mybatis.core.mapper.BaseMapperX;
import com.nnnmkj.thai.framework.mybatis.core.query.MPJLambdaWrapperX;
import com.nnnmkj.thai.module.course.controller.admin.lesson.vo.LessonPageReqVO;
import com.nnnmkj.thai.module.course.controller.app.lesson.vo.AppLessonPageReqVO;
import com.nnnmkj.thai.module.course.dal.dataobject.lesson.LessonDO;
import com.nnnmkj.thai.module.course.dal.dataobject.lesson.LessonDTO;
import com.nnnmkj.thai.module.course.dal.dataobject.lessonchapter.LessonChapterDO;
import com.nnnmkj.thai.module.course.dal.dataobject.lessonclickstatistic.LessonClickStatisticDO;
import com.nnnmkj.thai.module.course.dal.dataobject.lessoncollection.LessonCollectionDO;
import com.nnnmkj.thai.module.course.dal.dataobject.lessoncollectionstatistic.LessonCollectionStatisticDO;
import com.nnnmkj.thai.module.course.dal.dataobject.lessonstudystatistic.LessonStudyStatisticDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * 课程 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface LessonMapperX extends BaseMapperX<LessonDTO> {

    default PageResult<LessonDTO> selectPage(LessonPageReqVO reqVO) {
        MPJLambdaWrapper<LessonDTO> wrapper = buildBaseWrapper(reqVO);
        return selectPage(reqVO, wrapper);
    }

    @SuppressWarnings("all")
    private MPJLambdaWrapper<LessonDTO> buildBaseWrapper(LessonPageReqVO reqVO) {
        return new MPJLambdaWrapperX<LessonDTO>()
                .selectAll(LessonDO.class)
                .selectAs(CommonConstants.SYSTEM_NICKNAME, LessonDTO::getNickname)
                .likeIfPresent(LessonDO::getTitle, reqVO.getTitle())
                .likeIfPresent(LessonDO::getDescription, reqVO.getDescription())
                .likeIfPresent(LessonDO::getCoverImage, reqVO.getCoverImage())
                .eqIfPresent(LessonDO::getVisibility, reqVO.getVisibility())
                .betweenIfPresent(LessonDO::getCreateTime, reqVO.getCreateTime())
                .leftJoin(CommonConstants.SYSTEM_USER_JOIN_SQL)
                .orderByDesc(LessonDO::getId);
    }

    /**
     * 查询优质课程
     *
     * @param pageReqVO 分页请求参数
     * @return 优质课程列表分页结果
     */
    default PageResult<LessonDTO> selectQualityLessons(AppLessonPageReqVO pageReqVO) {
        String creatorQuery = CommonUtils.getCreatorQuery(pageReqVO.getUserId(), pageReqVO.getUserType());
        // 构建查询条件
        MPJLambdaWrapper<LessonDTO> wrapper = new MPJLambdaWrapperX<LessonDTO>()
                // 基础字段查询
                .selectAll(LessonDO.class)
                .selectAs(CommonConstants.MEMBER_NICKNAME, LessonDTO::getNickname)
                .selectAs(CommonConstants.MEMBER_AVATAR, LessonDTO::getAvatar)
                .leftJoin(CommonConstants.MEMBER_USER_JOIN_SQL)
                .likeIfExists(LessonDO::getTitle, pageReqVO.getTitle())
                .eqIfExists(LessonDO::getVisibility, pageReqVO.getVisibility())
                // 关联收藏如果存在数据，是否收藏则返回true。是否收藏则返回false
                .leftJoin(LessonCollectionDO.class,
                        "t10",
                        on -> on.eq(LessonCollectionDO::getLessonId, LessonDO::getId)
                                .likeIfExists(LessonCollectionDO::getCreator, creatorQuery))
                .select("CASE WHEN COUNT(t10.id) > 0 THEN TRUE ELSE FALSE END AS isStore")
                // 关联章节数据，获取章节数量
                .leftJoin(LessonChapterDO.class,
                        "t11",
                        on -> on.eq(LessonChapterDO::getCourseId, LessonDO::getId))
                .selectCount(LessonChapterDO::getId, LessonDTO::getChapterCount)
                // 关联收藏统计
                .leftJoin(LessonCollectionStatisticDO.class,
                        "t1",
                        LessonCollectionStatisticDO::getCourseLessonId, LessonDO::getId)
                .selectAs(LessonCollectionStatisticDO::getCount, LessonDTO::getCollectionCount)
                // 关联学习次数统计
                .leftJoin(LessonStudyStatisticDO.class,
                        "t2",
                        LessonStudyStatisticDO::getCourseLessonId, LessonDTO::getId)
                .selectAs(LessonStudyStatisticDO::getCount, LessonDTO::getStudyCount)
                // 关联点击次数统计
                .leftJoin(LessonClickStatisticDO.class,
                        "t3",
                        LessonClickStatisticDO::getCourseLessonId, LessonDTO::getId)
                .selectAs(LessonClickStatisticDO::getCount, LessonDTO::getClickCount)
                // 加权分数计算 (学习次数×0.3 + 收藏量×0.5 + 点击量×0.2)
                .select("IFNULL(t2.count, 0) * 0.3 + IFNULL(t1.count, 0) * 0.5 + IFNULL(t3.count, 0) * 0.2 AS weightedScore")
                .groupBy(LessonDO::getId);

        // 排序处理
        Integer sortField = pageReqVO.getSortField();
        if (sortField == null || sortField == 0) {
            wrapper.orderByDesc("weightedScore");
        } else {
            boolean isAsc = "asc".equalsIgnoreCase(pageReqVO.getSortOrder());
            switch (sortField) {
                case 1 -> wrapper.orderBy(true, isAsc, "t1.count");
                case 2 -> wrapper.orderBy(true, isAsc, "t2.count");
                case 3 -> wrapper.orderBy(true, isAsc, "t3.count");
                case 4 -> wrapper.orderBy(true, isAsc, "t.update_time");
                default -> wrapper.orderByDesc("weightedScore");
            }
        }

        return selectPage(pageReqVO, wrapper);
    }
}