package com.nnnmkj.thai.module.course.dal.dataobject.lessonprocess;

import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.nnnmkj.thai.framework.mybatis.core.dataobject.BaseDO;
import lombok.*;

import java.math.BigDecimal;

/**
 * 课程进度 DO
 *
 * <AUTHOR>
 */
@TableName("course_lesson_process")
@KeySequence("course_lesson_process_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class LessonProcessDO extends BaseDO {

    /**
     * 编号
     */
    @TableId
    private Long id;
    /**
     * 课程ID
     */
    private Long courseId;
    /**
     * 用户ID
     */
    private Long userId;
    /**
     * 总章节数
     */
    private Integer allCount;
    /**
     * 已学习章节数
     */
    private Integer learnedCount;
    /**
     * 未学习章节数
     */
    private Integer notLearnedCount;
    /**
     * 当前学习章节ID
     */
    private Long currentChapterId;
    /**
     * 学习进度百分比
     */
    private BigDecimal progressPercentage;
    /**
     * 状态
     *
     * 枚举 {@link TODO lesson_process_status 对应的类}
     */
    private Integer status;

}