package com.nnnmkj.thai.module.learning.controller.admin.wordset.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.List;
@Schema(description = "管理后台 - 学习集分享 Request VO")
@Data
public class WordSetShareGroupReqVo {

    @Schema(description = "编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "26749")
    private Long id;

    @Schema(description = "分享班级列表")
    private List<Long> groupIds;

    @Schema(description = "学习集编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "3513")
    private Long wordSetId;
}
