package com.nnnmkj.thai.module.course.controller.admin.lessonclickstatistic;

import com.nnnmkj.thai.framework.apilog.core.annotation.ApiAccessLog;
import com.nnnmkj.thai.framework.common.pojo.CommonResult;
import com.nnnmkj.thai.framework.common.pojo.PageParam;
import com.nnnmkj.thai.framework.common.pojo.PageResult;
import com.nnnmkj.thai.framework.common.util.object.BeanUtils;
import com.nnnmkj.thai.framework.excel.core.util.ExcelUtils;
import com.nnnmkj.thai.module.course.controller.admin.lessonclickstatistic.vo.LessonClickStatisticPageReqVO;
import com.nnnmkj.thai.module.course.controller.admin.lessonclickstatistic.vo.LessonClickStatisticRespVO;
import com.nnnmkj.thai.module.course.controller.admin.lessonclickstatistic.vo.LessonClickStatisticSaveReqVO;
import com.nnnmkj.thai.module.course.dal.dataobject.lessonclickstatistic.LessonClickStatisticDO;
import com.nnnmkj.thai.module.course.service.lessonclickstatistic.LessonClickStatisticService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.util.List;

import static com.nnnmkj.thai.framework.apilog.core.enums.OperateTypeEnum.EXPORT;
import static com.nnnmkj.thai.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 课程点击量统计")
@RestController
@RequestMapping("/course/lesson-click-statistic")
@Validated
public class LessonClickStatisticController {

    @Resource
    private LessonClickStatisticService lessonClickStatisticService;

    @PostMapping("/create")
    @Operation(summary = "创建课程点击量统计")
    @PreAuthorize("@ss.hasPermission('course:lesson-click-statistic:create')")
    public CommonResult<Long> createLessonClickStatistic(@Valid @RequestBody LessonClickStatisticSaveReqVO createReqVO) {
        return success(lessonClickStatisticService.createLessonClickStatistic(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新课程点击量统计")
    @PreAuthorize("@ss.hasPermission('course:lesson-click-statistic:update')")
    public CommonResult<Boolean> updateLessonClickStatistic(@Valid @RequestBody LessonClickStatisticSaveReqVO updateReqVO) {
        lessonClickStatisticService.updateLessonClickStatistic(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除课程点击量统计")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('course:lesson-click-statistic:delete')")
    public CommonResult<Boolean> deleteLessonClickStatistic(@RequestParam("id") Long id) {
        lessonClickStatisticService.deleteLessonClickStatistic(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得课程点击量统计")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('course:lesson-click-statistic:query')")
    public CommonResult<LessonClickStatisticRespVO> getLessonClickStatistic(@RequestParam("id") Long id) {
        LessonClickStatisticDO lessonClickStatistic = lessonClickStatisticService.getLessonClickStatistic(id);
        return success(BeanUtils.toBean(lessonClickStatistic, LessonClickStatisticRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得课程点击量统计分页")
    @PreAuthorize("@ss.hasPermission('course:lesson-click-statistic:query')")
    public CommonResult<PageResult<LessonClickStatisticRespVO>> getLessonClickStatisticPage(@Valid LessonClickStatisticPageReqVO pageReqVO) {
        PageResult<LessonClickStatisticDO> pageResult = lessonClickStatisticService.getLessonClickStatisticPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, LessonClickStatisticRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出课程点击量统计 Excel")
    @PreAuthorize("@ss.hasPermission('course:lesson-click-statistic:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportLessonClickStatisticExcel(@Valid LessonClickStatisticPageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<LessonClickStatisticDO> list = lessonClickStatisticService.getLessonClickStatisticPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "课程点击量统计.xls", "数据", LessonClickStatisticRespVO.class,
                        BeanUtils.toBean(list, LessonClickStatisticRespVO.class));
    }

}