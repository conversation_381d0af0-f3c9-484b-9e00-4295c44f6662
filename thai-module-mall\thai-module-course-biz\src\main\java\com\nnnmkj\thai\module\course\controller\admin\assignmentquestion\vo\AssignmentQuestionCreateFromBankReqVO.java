package com.nnnmkj.thai.module.course.controller.admin.assignmentquestion.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.List;

@Schema(description = "管理后台 - 作业题库选题 Request VO")
@Data
public class AssignmentQuestionCreateFromBankReqVO {

    @Schema(description = "作业ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "12524")
    @NotNull(message = "作业ID不能为空")
    private Long assignmentId;

    @Schema(description = "课程ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "27036")
    @NotNull(message = "课程ID不能为空")
    private Long courseId;

    @Schema(description = "选取的题目及分数列表", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "题目列表不能为空")
    private List<QuestionScoreItem> questionItems;

    @Data
    public static class QuestionScoreItem {
        @Schema(description = "题目ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "12524")
        @NotNull(message = "题目ID不能为空")
        private Long questionId;

        @Schema(description = "分数（仅自定义类型时使用）")
        @NotNull(message = "分数不能为空")
        private Double score;
    }

}