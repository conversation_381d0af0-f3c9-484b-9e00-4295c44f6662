package com.nnnmkj.thai.module.course.service.lessonchapter;

import cn.hutool.core.collection.CollUtil;
import com.nnnmkj.thai.framework.common.pojo.PageResult;
import com.nnnmkj.thai.framework.common.util.object.BeanUtils;
import com.nnnmkj.thai.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.nnnmkj.thai.module.course.controller.app.lessonchapter.vo.AppLessonChapterPageReqVO;
import com.nnnmkj.thai.module.course.controller.app.lessonchapter.vo.AppLessonChapterRespVO;
import com.nnnmkj.thai.module.course.controller.app.lessonchapter.vo.AppLessonChapterSaveReqVO;
import com.nnnmkj.thai.module.course.convert.lessonchapter.LessonChapterConvert;
import com.nnnmkj.thai.module.course.dal.dataobject.lesson.LessonDO;
import com.nnnmkj.thai.module.course.dal.dataobject.lessonchapter.LessonChapterDO;
import com.nnnmkj.thai.module.course.dal.dataobject.lessonchapter.LessonChatperAttachmentDO;
import com.nnnmkj.thai.module.course.dal.dataobject.lessonprocess.LessonChapterProcessDO;
import com.nnnmkj.thai.module.course.dal.mysql.lessonchapter.LessonChapterMapper;
import com.nnnmkj.thai.module.course.dal.mysql.lessonchapter.LessonChatperAttachmentMapper;
import com.nnnmkj.thai.module.course.service.lesson.AppLessonService;
import com.nnnmkj.thai.module.course.service.lessonprocess.LessonProcessService;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import java.util.*;
import java.util.stream.Collectors;

import static com.nnnmkj.thai.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.nnnmkj.thai.framework.security.core.util.SecurityFrameworkUtils.getLoginUserId;
import static com.nnnmkj.thai.module.course.enums.ErrorCodeConstants.LESSON_CHAPTER_NOT_EXISTS;

/**
 * 课程章节 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class AppLessonChapterServiceImpl implements AppLessonChapterService {

    @Resource
    private LessonChapterMapper lessonChapterMapper;
    @Resource
    private LessonChatperAttachmentMapper lessonChatperAttachmentMapper;
    @Resource
    private AppLessonService lessonService;
    @Resource
    private LessonProcessService lessonProcessService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long createLessonChapter(AppLessonChapterSaveReqVO createReqVO) {
        // 插入
        LessonChapterDO lessonChapter = BeanUtils.toBean(createReqVO, LessonChapterDO.class);
        lessonChapterMapper.insert(lessonChapter);

        // 插入子表
        createLessonChatperAttachmentList(lessonChapter.getId(), createReqVO.getLessonChatperAttachments());
        // 返回
        return lessonChapter.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateLessonChapter(AppLessonChapterSaveReqVO updateReqVO) {
        // 校验存在
        validateLessonChapterExists(updateReqVO.getId());
        // 更新
        LessonChapterDO updateObj = BeanUtils.toBean(updateReqVO, LessonChapterDO.class);
        lessonChapterMapper.updateById(updateObj);

        // 更新子表
        updateLessonChatperAttachmentList(updateReqVO.getId(), updateReqVO.getLessonChatperAttachments());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteLessonChapter(Long id) {
        // 校验存在
        validateLessonChapterExists(id);
        // 删除
        lessonChapterMapper.deleteById(id);

        // 删除子表
        deleteLessonChatperAttachmentByChapterId(id);
    }

    private void validateLessonChapterExists(Long id) {
        if (lessonChapterMapper.selectById(id) == null) {
            throw exception(LESSON_CHAPTER_NOT_EXISTS);
        }
    }

    @Override
    public LessonChapterDO getLessonChapter(Long id) {
        return lessonChapterMapper.selectById(id);
    }
    
    @Override
    public AppLessonChapterRespVO getLessonChapterWithRelatedInfo(Long id) {
        LessonChapterDO lessonChapter = getLessonChapter(id);
        if (lessonChapter == null) {
            return null;
        }
        // 处理课程返显
        LessonDO lesson = lessonService.getLesson(lessonChapter.getCourseId());
        LessonChapterDO parentChapter = lessonChapter.getParentId() != null && lessonChapter.getParentId() > 0 
                ? getLessonChapter(lessonChapter.getParentId()) : null;
        return LessonChapterConvert.INSTANCE.convert1(lessonChapter, lesson, parentChapter);
    }

    @Override
    public PageResult<LessonChapterDO> getLessonChapterPage(AppLessonChapterPageReqVO pageReqVO) {
        return lessonChapterMapper.selectPage(pageReqVO);
    }
    
    @Override
    public PageResult<AppLessonChapterRespVO> getLessonChapterPageWithRelatedInfo(AppLessonChapterPageReqVO pageReqVO) {
        PageResult<LessonChapterDO> pageResult = getLessonChapterPage(pageReqVO);
        if (CollUtil.isEmpty(pageResult.getList())) {
            return PageResult.empty();
        }
        
        // 处理课程返显
        Set<Long> lessonChapterIds = pageResult.getList().stream().map(LessonChapterDO::getId).collect(Collectors.toSet());
        Set<Long> courseIds = pageResult.getList().stream().map(LessonChapterDO::getCourseId).collect(Collectors.toSet());
        List<LessonChapterDO> lessonChapterList = getLessonChapterList(lessonChapterIds);
        List<LessonDO> lessonList = lessonService.getLessonList(courseIds);
        
        return LessonChapterConvert.INSTANCE.convertPage1(pageResult, lessonList, lessonChapterList);
    }
    
    @Override
    public List<LessonChapterDO> getLessonChapterList(Collection<Long> ids) {
        if (ids == null || ids.isEmpty()) {
            return new ArrayList<>();
        }
        return lessonChapterMapper.selectList(new LambdaQueryWrapperX<LessonChapterDO>().in(LessonChapterDO::getId, ids));
    }
    
    @Override
    public List<AppLessonChapterRespVO> getLessonChapterListForExport(AppLessonChapterPageReqVO pageReqVO) {
        pageReqVO.setPageSize(Integer.MAX_VALUE); // 确保获取所有数据
        List<LessonChapterDO> list = getLessonChapterPage(pageReqVO).getList();
        
        // 处理课程返显
        Set<Long> courseIds = list.stream().map(LessonChapterDO::getCourseId).collect(Collectors.toSet());
        List<LessonDO> courseList = lessonService.getLessonList(courseIds);
        
        Set<Long> courseChapterIds = list.stream().map(LessonChapterDO::getId).collect(Collectors.toSet());
        List<LessonChapterDO> courseChapterList = getLessonChapterList(courseChapterIds);
        
        return LessonChapterConvert.INSTANCE.convertList1(list, courseList, courseChapterList);
    }

    // ==================== 子表（课程章节附件） ====================

    @Override
    public List<LessonChatperAttachmentDO> getLessonChatperAttachmentListByChapterId(Long chapterId) {
        return lessonChatperAttachmentMapper.selectListByChapterId(chapterId);
    }

    private void createLessonChatperAttachmentList(Long chapterId, List<LessonChatperAttachmentDO> list) {
        list.forEach(o -> o.setChapterId(chapterId));
        lessonChatperAttachmentMapper.insertBatch(list);
    }

    private void updateLessonChatperAttachmentList(Long chapterId, List<LessonChatperAttachmentDO> list) {
        deleteLessonChatperAttachmentByChapterId(chapterId);
		list.forEach(o -> o.setId(null).setUpdater(null).setUpdateTime(null)); // 解决更新情况下：1）id 冲突；2）updateTime 不更新
        createLessonChatperAttachmentList(chapterId, list);
    }

    private void deleteLessonChatperAttachmentByChapterId(Long chapterId) {
        lessonChatperAttachmentMapper.deleteByChapterId(chapterId);
    }

    @Override
    public List<LessonChapterDO> getLessonChapterListByCourseId(Long courseId) {
        return lessonChapterMapper.selectList(new LambdaQueryWrapperX<LessonChapterDO>()
                .eq(LessonChapterDO::getCourseId, courseId));
    }
    
    @Override
    public List<AppLessonChapterRespVO> getLessonChapterListByCourseIdWithStatus(Long courseId) {
        // 获取课程下所有章节
        List<LessonChapterDO> chapterList = getLessonChapterListByCourseId(courseId);
        if (CollUtil.isEmpty(chapterList)) {
            return Collections.emptyList();
        }
        
        // 获取课程信息
        LessonDO lesson = lessonService.getLesson(courseId);
        
        // 获取所有章节ID，用于查询父章节信息
        Set<Long> chapterIds = chapterList.stream().map(LessonChapterDO::getId).collect(Collectors.toSet());
        List<LessonChapterDO> allChapters = getLessonChapterList(chapterIds);
        
        // 获取当前登录用户ID
        Long currentUserId = getLoginUserId();
        
        // 获取章节完成状态
        List<LessonChapterProcessDO> chapterProcessList = lessonProcessService.getLessonChapterProcessListByCourseIdAndUserId(courseId, currentUserId);
        Map<Long, Boolean> chapterCompletionMap = new HashMap<>();
        
        // 处理每个章节的完成状态
        for (LessonChapterDO chapter : chapterList) {
            boolean isCompleted = false;
            if (CollUtil.isNotEmpty(chapterProcessList)) {
                // 检查该章节是否有完成记录，并且所有任务点都已完成
                isCompleted = chapterProcessList.stream()
                        .anyMatch(process -> process.getChapterId().equals(chapter.getId()) 
                                && process.getCompletedCount() != null 
                                && process.getAllCount() != null 
                                && process.getCompletedCount() >= process.getAllCount());
            }
            chapterCompletionMap.put(chapter.getId(), isCompleted);
        }
        
        // 转换并返回数据
        List<AppLessonChapterRespVO> resultList = LessonChapterConvert.INSTANCE.convertList1(chapterList, Collections.singletonList(lesson), allChapters);
        
        // 设置完成状态
        resultList.forEach(vo -> vo.setStatus(chapterCompletionMap.getOrDefault(vo.getId(), false) ? 1 : 0));
        
        return resultList;
    }

    @Override
    public PageResult<LessonChapterDO> getTopLessonChapterPage(AppLessonChapterPageReqVO pageReqVO) {
        // 构建查询条件并执行查询
        return lessonChapterMapper.selectPage(pageReqVO, new LambdaQueryWrapperX<LessonChapterDO>()
                .eqIfPresent(LessonChapterDO::getCourseId, pageReqVO.getCourseId())
                .likeIfPresent(LessonChapterDO::getName, pageReqVO.getName())
                .betweenIfPresent(LessonChapterDO::getCreateTime, pageReqVO.getCreateTime())
                .and(wrapper -> wrapper.isNull(LessonChapterDO::getParentId).or().eq(LessonChapterDO::getParentId, 0L))
                .orderByDesc(LessonChapterDO::getId));
    }
    
    @Override
    public PageResult<AppLessonChapterRespVO> getParentLessonChapterPageWithStatus(AppLessonChapterPageReqVO pageReqVO) {
        // 设置只查询父章节为null或为0的数据，这些都是顶级章节
        pageReqVO.setParentId(0L);
        PageResult<LessonChapterDO> pageResult = getTopLessonChapterPage(pageReqVO);

        if (CollUtil.isEmpty(pageResult.getList())) {
            return PageResult.empty();
        }
        
        // 获取当前登录用户ID
        Long currentUserId = getLoginUserId();
        
        // 处理课程返显
        Set<Long> lessonChapterIds = pageResult.getList().stream().map(LessonChapterDO::getId).collect(Collectors.toSet());
        Set<Long> courseIds = pageResult.getList().stream().map(LessonChapterDO::getCourseId).collect(Collectors.toSet());
        List<LessonChapterDO> lessonChapterList = getLessonChapterList(lessonChapterIds);
        List<LessonDO> lessonList = lessonService.getLessonList(courseIds);
        
        // 获取所有子章节
        List<LessonChapterDO> allChildChapters = new ArrayList<>();
        for (Long parentId : lessonChapterIds) {
            allChildChapters.addAll(getLessonChapterListByParentId(parentId));
        }
        Set<Long> allChildChapterIds = allChildChapters.stream().map(LessonChapterDO::getId).collect(Collectors.toSet());
        
        // 获取所有章节的进度状态
        Map<Long, List<LessonChapterProcessDO>> chapterProcessMap = new HashMap<>();
        for (Long courseId : courseIds) {
            List<LessonChapterProcessDO> processList = lessonProcessService.getLessonChapterProcessListByCourseIdAndUserId(courseId, currentUserId);
            if (CollUtil.isNotEmpty(processList)) {
                processList.forEach(process -> chapterProcessMap.computeIfAbsent(process.getChapterId(), k -> new ArrayList<>()).add(process));
            }
        }
        
        // 转换并返回数据
        PageResult<AppLessonChapterRespVO> result = LessonChapterConvert.INSTANCE.convertPage1(pageResult, lessonList, lessonChapterList);
        
        // 设置顶级章节状态（根据子章节完成情况）
        result.getList().forEach(vo -> {
            // 获取该顶级章节的所有子章节
            List<LessonChapterDO> childChapters = allChildChapters.stream()
                    .filter(chapter -> chapter.getParentId().equals(vo.getId()))
                    .collect(Collectors.toList());
            
            if (CollUtil.isEmpty(childChapters)) {
                // 如果没有子章节，则根据任务点完成情况判断
                List<LessonChapterProcessDO> processList = chapterProcessMap.getOrDefault(vo.getId(), Collections.emptyList());
                boolean isCompleted = processList.stream()
                        .anyMatch(process -> process.getCompletedCount() != null 
                                && process.getAllCount() != null 
                                && process.getCompletedCount() >= process.getAllCount());
                vo.setStatus(isCompleted ? 1 : 0);
            } else {
                // 如果有子章节，则判断所有子章节是否都已完成
                boolean allChildrenCompleted = childChapters.stream().allMatch(childChapter -> {
                    List<LessonChapterProcessDO> processList = chapterProcessMap.getOrDefault(childChapter.getId(), Collections.emptyList());
                    return processList.stream()
                            .anyMatch(process -> process.getCompletedCount() != null 
                                    && process.getAllCount() != null 
                                    && process.getCompletedCount() >= process.getAllCount());
                });
                vo.setStatus(allChildrenCompleted ? 1 : 0);
            }
        });
        
        return result;
    }

    @Override
    public List<LessonChapterDO> getLessonChapterListByParentId(Long parentId) {
        return lessonChapterMapper.selectList(new LambdaQueryWrapperX<LessonChapterDO>()
                .eq(LessonChapterDO::getParentId, parentId)
                .orderByAsc(LessonChapterDO::getSort));
    }
    
    @Override
    public List<AppLessonChapterRespVO> getChildLessonChapterListWithStatus(Long parentId) {
        List<LessonChapterDO> childList = getLessonChapterListByParentId(parentId);
        
        if (CollUtil.isEmpty(childList)) {
            return Collections.emptyList();
        }
        
        // 获取当前登录用户ID
        Long currentUserId = getLoginUserId();
        
        // 处理课程返显
        Set<Long> courseIds = childList.stream().map(LessonChapterDO::getCourseId).collect(Collectors.toSet());
        List<LessonDO> lessonList = lessonService.getLessonList(courseIds);
        
        // 获取父章节
        LessonChapterDO parentChapter = getLessonChapter(parentId);
        
        // 获取所有章节的进度状态
        Map<Long, List<LessonChapterProcessDO>> chapterProcessMap = new HashMap<>();
        for (Long courseId : courseIds) {
            List<LessonChapterProcessDO> processList = lessonProcessService.getLessonChapterProcessListByCourseIdAndUserId(courseId, currentUserId);
            if (CollUtil.isNotEmpty(processList)) {
                processList.forEach(process -> chapterProcessMap.computeIfAbsent(process.getChapterId(), k -> new ArrayList<>()).add(process));
            }
        }
        
        // 转换并返回数据
        List<AppLessonChapterRespVO> resultList = LessonChapterConvert.INSTANCE.convertList1(childList, lessonList, 
                Collections.singletonList(parentChapter));
        
        // 设置子章节状态（根据任务点完成情况）
        resultList.forEach(vo -> {
            List<LessonChapterProcessDO> processList = chapterProcessMap.getOrDefault(vo.getId(), Collections.emptyList());
            boolean isCompleted = processList.stream()
                    .anyMatch(process -> process.getCompletedCount() != null 
                            && process.getAllCount() != null 
                            && process.getCompletedCount() >= process.getAllCount());
            vo.setStatus(isCompleted ? 1 : 0);
        });
        
        return resultList;
    }
    
    @Override
    public List<AppLessonChapterRespVO> searchLessonChaptersByKeyword(String keyword, Long courseId) {
        if (keyword == null || keyword.trim().isEmpty()) {
            return Collections.emptyList();
        }
        
        // 根据关键词搜索章节
        List<LessonChapterDO> allMatchedChapters = lessonChapterMapper.selectList(
                new LambdaQueryWrapperX<LessonChapterDO>()
                        .eq(courseId != null, LessonChapterDO::getCourseId, courseId)
                        .like(LessonChapterDO::getName, keyword)
                        .orderByDesc(LessonChapterDO::getId));
        
        if (CollUtil.isEmpty(allMatchedChapters)) {
            return Collections.emptyList();
        }
        
        // 区分顶级章节和子章节
        List<LessonChapterDO> topChapters = new ArrayList<>();
        List<LessonChapterDO> childChapters = new ArrayList<>();
        
        for (LessonChapterDO chapter : allMatchedChapters) {
            if (chapter.getParentId() == null || chapter.getParentId() == 0L) {
                topChapters.add(chapter);
            } else {
                childChapters.add(chapter);
            }
        }
        
        // 收集需要返回的顶级章节
        Set<Long> topChapterIds = new HashSet<>();
        
        // 添加直接匹配的顶级章节ID
        topChapters.forEach(chapter -> topChapterIds.add(chapter.getId()));
        
        // 添加子章节对应的顶级章节ID
        for (LessonChapterDO childChapter : childChapters) {
            Long parentId = childChapter.getParentId();
            if (parentId != null && parentId > 0) {
                topChapterIds.add(parentId);
            }
        }
        
        if (topChapterIds.isEmpty()) {
            return Collections.emptyList();
        }
        
        // 获取所有顶级章节
        List<LessonChapterDO> resultChapters = getLessonChapterList(topChapterIds);
        
        // 获取当前登录用户ID
        Long currentUserId = getLoginUserId();
        
        // 获取课程信息
        Set<Long> courseIds = resultChapters.stream()
                .map(LessonChapterDO::getCourseId)
                .collect(Collectors.toSet());
        List<LessonDO> lessonList = lessonService.getLessonList(courseIds);
        
        // 获取所有子章节
        List<LessonChapterDO> allChildChapters = new ArrayList<>();
        for (Long parentId : topChapterIds) {
            allChildChapters.addAll(getLessonChapterListByParentId(parentId));
        }
        
        // 获取所有章节的进度状态
        Map<Long, List<LessonChapterProcessDO>> chapterProcessMap = new HashMap<>();
        for (Long id : courseIds) {
            List<LessonChapterProcessDO> processList = lessonProcessService.getLessonChapterProcessListByCourseIdAndUserId(id, currentUserId);
            if (CollUtil.isNotEmpty(processList)) {
                processList.forEach(process -> chapterProcessMap.computeIfAbsent(process.getChapterId(), k -> new ArrayList<>()).add(process));
            }
        }
        
        // 转换顶级章节数据
        List<AppLessonChapterRespVO> resultList = LessonChapterConvert.INSTANCE.convertList1(
                resultChapters, lessonList, new ArrayList<>());
        
        // 设置各章节状态
        resultList.forEach(vo -> {
            // 获取该顶级章节的所有子章节
            List<LessonChapterDO> childChaptersList = allChildChapters.stream()
                    .filter(chapter -> chapter.getParentId().equals(vo.getId()))
                    .collect(Collectors.toList());
            
            if (CollUtil.isEmpty(childChaptersList)) {
                // 如果没有子章节，则根据任务点完成情况判断
                List<LessonChapterProcessDO> processList = chapterProcessMap.getOrDefault(vo.getId(), Collections.emptyList());
                boolean isCompleted = processList.stream()
                        .anyMatch(process -> process.getCompletedCount() != null 
                                && process.getAllCount() != null 
                                && process.getCompletedCount() >= process.getAllCount());
                vo.setStatus(isCompleted ? 1 : 0);
            } else {
                // 如果有子章节，则判断所有子章节是否都已完成
                boolean allChildrenCompleted = childChaptersList.stream().allMatch(childChapter -> {
                    List<LessonChapterProcessDO> processList = chapterProcessMap.getOrDefault(childChapter.getId(), Collections.emptyList());
                    return processList.stream()
                            .anyMatch(process -> process.getCompletedCount() != null 
                                    && process.getAllCount() != null 
                                    && process.getCompletedCount() >= process.getAllCount());
                });
                vo.setStatus(allChildrenCompleted ? 1 : 0);
            }
        });
        
        return resultList;
    }
}