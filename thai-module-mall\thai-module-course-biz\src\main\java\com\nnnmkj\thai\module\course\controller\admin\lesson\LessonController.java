package com.nnnmkj.thai.module.course.controller.admin.lesson;

import cn.hutool.core.collection.CollUtil;
import com.nnnmkj.thai.framework.apilog.core.annotation.ApiAccessLog;
import com.nnnmkj.thai.framework.common.pojo.CommonResult;
import com.nnnmkj.thai.framework.common.pojo.PageParam;
import com.nnnmkj.thai.framework.common.pojo.PageResult;
import com.nnnmkj.thai.framework.common.util.object.BeanUtils;
import com.nnnmkj.thai.framework.excel.core.util.ExcelUtils;
import com.nnnmkj.thai.module.course.controller.admin.lesson.vo.LessonPageReqVO;
import com.nnnmkj.thai.module.course.controller.admin.lesson.vo.LessonRespVO;
import com.nnnmkj.thai.module.course.controller.admin.lesson.vo.LessonSaveReqVO;
import com.nnnmkj.thai.module.course.controller.admin.lesson.vo.LessonSimpleRespVO;
import com.nnnmkj.thai.module.course.dal.dataobject.lesson.LessonAttachmentDO;
import com.nnnmkj.thai.module.course.dal.dataobject.lesson.LessonDO;
import com.nnnmkj.thai.module.course.dal.dataobject.lesson.LessonDTO;
import com.nnnmkj.thai.module.course.dal.dataobject.lesson.LessonHasGroupDTO;
import com.nnnmkj.thai.module.course.service.lesson.LessonService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.util.List;

import static com.nnnmkj.thai.framework.apilog.core.enums.OperateTypeEnum.EXPORT;
import static com.nnnmkj.thai.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 课程")
@RestController
@RequestMapping("/course/lesson")
@Validated
public class LessonController {

    @Resource
    private LessonService lessonService;

    @PostMapping("/create")
    @Operation(summary = "创建课程")
    @PreAuthorize("@ss.hasPermission('course:lesson:create')")
    public CommonResult<Long> createLesson(@Valid @RequestBody LessonSaveReqVO createReqVO) {
        return success(lessonService.createLesson(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新课程")
    @PreAuthorize("@ss.hasPermission('course:lesson:update')")
    public CommonResult<Boolean> updateLesson(@Valid @RequestBody LessonSaveReqVO updateReqVO) {
        lessonService.updateLesson(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除课程")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('course:lesson:delete')")
    public CommonResult<Boolean> deleteLesson(@RequestParam("id") Long id) {
        lessonService.deleteLesson(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得课程")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('course:lesson:query')")
    public CommonResult<LessonRespVO> getLesson(@RequestParam("id") Long id) {
        LessonDO lesson = lessonService.getLesson(id);
        LessonRespVO bean = BeanUtils.toBean(lesson, LessonRespVO.class);
        bean.setGroupIds(lessonService.getGroupIdsByCourseId(id));
        return success(bean);
    }

    @GetMapping("/page")
    @Operation(summary = "获得课程分页")
    @PreAuthorize("@ss.hasPermission('course:lesson:query')")
    public CommonResult<PageResult<LessonRespVO>> getLessonPage(@Valid LessonPageReqVO pageReqVO) {
        PageResult<LessonHasGroupDTO> pageResult = lessonService.getLessonPages(pageReqVO);
        if (CollUtil.isEmpty(pageResult.getList())) {
            return success(PageResult.empty());
        }
        return success(BeanUtils.toBean(pageResult, LessonRespVO.class));
    }

    @GetMapping("/simple-list")
    @Operation(summary = "获得课程精简列表")
    @PreAuthorize("@ss.hasPermission('course:lesson:query')")
    public CommonResult<List<LessonSimpleRespVO>> getLessonSimpleList(@Valid LessonPageReqVO pageReqVO) {
        List<LessonDO> list = lessonService.getLessonList(pageReqVO);
        return success(BeanUtils.toBean(list, LessonSimpleRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出课程 Excel")
    @PreAuthorize("@ss.hasPermission('course:lesson:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportLessonExcel(@Valid LessonPageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<LessonDTO> list = lessonService.getLessonPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "课程.xls", "数据", LessonRespVO.class,
                        BeanUtils.toBean(list, LessonRespVO.class));
    }

    // ==================== 子表（课程附件） ====================

    @GetMapping("/lesson-attachment/list-by-course-id")
    @Operation(summary = "获得课程附件列表")
    @Parameter(name = "courseId", description = "课程ID")
    @PreAuthorize("@ss.hasPermission('course:lesson:query')")
    public CommonResult<List<LessonAttachmentDO>> getLessonAttachmentListByCourseId(@RequestParam("courseId") Long courseId) {
        return success(lessonService.getLessonAttachmentListByCourseId(courseId));
    }

}