package com.nnnmkj.thai.module.learning.controller.admin.wordset;

import cn.hutool.core.collection.CollUtil;
import com.nnnmkj.thai.framework.apilog.core.annotation.ApiAccessLog;
import com.nnnmkj.thai.framework.common.enums.UserTypeEnum;
import com.nnnmkj.thai.framework.common.pojo.CommonResult;
import com.nnnmkj.thai.framework.common.pojo.PageParam;
import com.nnnmkj.thai.framework.common.pojo.PageResult;
import com.nnnmkj.thai.framework.common.util.object.BeanUtils;
import com.nnnmkj.thai.framework.excel.core.util.ExcelUtils;
import com.nnnmkj.thai.module.learning.controller.admin.wordset.vo.*;
import com.nnnmkj.thai.module.learning.dal.dataobject.wordcard.WordCardDO;
import com.nnnmkj.thai.module.learning.dal.dataobject.wordset.WordSetDO;
import com.nnnmkj.thai.module.learning.dal.dataobject.wordset.WordSetPageDTO;
import com.nnnmkj.thai.module.learning.service.wordset.WordSetService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.util.Collections;
import java.util.List;

import static com.nnnmkj.thai.framework.apilog.core.enums.OperateTypeEnum.EXPORT;
import static com.nnnmkj.thai.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 学习集")
@RestController
@RequestMapping("/learning/word-set")
@Validated
public class WordSetController {

    @Resource
    private WordSetService wordSetService;

    @PostMapping("/create")
    @Operation(summary = "创建学习集")
    @PreAuthorize("@ss.hasPermission('learning:word-set:create')")
    public CommonResult<Long> createWordSet(@Valid @RequestBody WordSetSaveReqVO createReqVO) {
        return success(wordSetService.createWordSet(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新学习集")
    @PreAuthorize("@ss.hasPermission('learning:word-set:update')")
    public CommonResult<Boolean> updateWordSet(@Valid @RequestBody WordSetSaveReqVO updateReqVO) {
        wordSetService.updateWordSet(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除学习集")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('learning:word-set:delete')")
    public CommonResult<Boolean> deleteWordSet(@RequestParam("id") Long id) {
        wordSetService.deleteWordSet(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得学习集")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('learning:word-set:query')")
    public CommonResult<WordSetRespVO> getWordSet(@RequestParam("id") Long id) {
        // 查询学习集
        WordSetRespVO wordSetWithCardsAndDefinitions = wordSetService.getWordSetWithCardsAndDefinitions(id);
        return success(wordSetWithCardsAndDefinitions);
    }

    @GetMapping("/count")
    @Operation(summary = "获得学习集数")
    @PreAuthorize("@ss.hasPermission('learning:word-set:query')")
    public CommonResult<Long> getWordSetCount() {
        return success(wordSetService.getWordSetCount());
    }

    @GetMapping("/page")
    @Operation(summary = "获得学习集分页")
    @PreAuthorize("@ss.hasPermission('learning:word-set:query')")
    public CommonResult<PageResult<WordSetRespVO>> getWordSetPage(@Valid WordSetPageReqVO pageReqVO) {
        if (pageReqVO.getUserType() == null) {
            pageReqVO.setUserType(UserTypeEnum.ADMIN.getValue());
        }
        PageResult<WordSetPageDTO> pageResult = wordSetService.getWordSetPage(pageReqVO);
        if (CollUtil.isEmpty(pageResult.getList())) {
            return success(PageResult.empty());
        }
        return success(BeanUtils.toBean(pageResult, WordSetRespVO.class));
    }

    @GetMapping("/simple-list")
    @Operation(summary = "获得学习集简易列表")
    public CommonResult<List<WordSetSimpleRespVO>> getWordSetSimpleList(@Valid WordSetPageReqVO reqVO) {
        if (reqVO.getUserType() == null) {
            reqVO.setUserType(UserTypeEnum.ADMIN.getValue());
        }
        List<WordSetDO> listResult = wordSetService.getWordSetList(reqVO);
        if (CollUtil.isEmpty(listResult)) {
            return success(Collections.emptyList());
        }
        return success(BeanUtils.toBean(listResult, WordSetSimpleRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出学习集 Excel")
    @PreAuthorize("@ss.hasPermission('learning:word-set:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportWordSetExcel(@Valid WordSetPageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<WordSetPageDTO> list = wordSetService.getWordSetPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "学习集.xls", "数据", WordSetRespVO.class,
                        BeanUtils.toBean(list, WordSetRespVO.class));
    }

    // ==================== 子表（单词卡） ====================

    @GetMapping("/word-card/list-by-word-set-id")
    @Operation(summary = "获得单词卡列表")
    @Parameter(name = "wordSetId", description = "学习集编号")
    @PreAuthorize("@ss.hasPermission('learning:word-set:query')")
    public CommonResult<List<WordCardDO>> getWordCardListByWordSetId(@RequestParam("wordSetId") Long wordSetId) {
        return success(wordSetService.getWordCardListByWordSetId(wordSetId));
    }

    @PostMapping("/wordSetShareGroup")
    @Operation(summary = "学习集分享班级")
    public CommonResult<Boolean> wordSetShareGroup(@RequestBody WordSetShareGroupReqVo reqVO) {
        return success(wordSetService.wordSetShareGroup(reqVO));
    }



}