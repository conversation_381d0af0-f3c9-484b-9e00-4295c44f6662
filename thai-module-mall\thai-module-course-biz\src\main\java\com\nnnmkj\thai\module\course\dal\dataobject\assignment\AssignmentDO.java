package com.nnnmkj.thai.module.course.dal.dataobject.assignment;

import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.nnnmkj.thai.framework.mybatis.core.dataobject.BaseDO;
import lombok.*;

/**
 * 课程作业 DO
 *
 * <AUTHOR>
 */
@TableName("course_assignment")
@KeySequence("course_assignment_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AssignmentDO extends BaseDO {

    /**
     * 编号
     */
    @TableId
    private Long id;
    /**
     * 课程ID
     */
    private Long courseId;
    /**
     * 作业名称
     */
    private String name;
    /**
     * 评分机制
     */
    private Integer scoringMechanism;
    /**
     * 用户ID
     */
    private Long userId;

}