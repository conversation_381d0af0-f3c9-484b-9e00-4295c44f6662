package com.nnnmkj.thai.module.course.controller.app.lesson.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Schema(description = "用户 App - 课程 Response VO")
@Data
@ExcelIgnoreUnannotated
public class AppLessonRespVO {

    @Schema(description = "编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "6445")
    @ExcelProperty("编号")
    private Long id;

    @Schema(description = "课程标题", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("课程标题")
    private String title;

    @Schema(description = "课程简介", example = "你猜")
    @ExcelProperty("课程简介")
    private String description;

    @Schema(description = "课程封面图URL")
    @ExcelProperty("课程封面图URL")
    private String coverImage;

    @Schema(description = "用户ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "14855")
    @ExcelProperty("用户ID")
    private Long userId;

    @Schema(description = "用户昵称", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("用户昵称")
    private String nickname;

    @Schema(description = "用户头像")
    @ExcelProperty("用户头像")
    private String avatar;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

    @Schema(description = "可见范围", example = "1")
    @ExcelProperty("可见范围")
    private Integer visibility;
    
    @Schema(description = "是否已收藏")
    private Boolean isStore;

    @Schema(description = "收藏数量")
    @ExcelProperty("收藏数量")
    private Integer collectionCount;

    @Schema(description = "学习次数")
    @ExcelProperty("学习次数")
    private Integer studyCount;

    @Schema(description = "课程章节数量")
    @ExcelProperty("课程章节数量")
    private Integer chapterCount;

    @Schema(description = "点击量")
    @ExcelProperty("点击量")
    private Integer viewCount;

}