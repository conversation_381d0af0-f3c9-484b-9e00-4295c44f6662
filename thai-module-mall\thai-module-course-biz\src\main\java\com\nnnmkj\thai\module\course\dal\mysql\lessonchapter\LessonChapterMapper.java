package com.nnnmkj.thai.module.course.dal.mysql.lessonchapter;

import com.nnnmkj.thai.framework.common.pojo.PageResult;
import com.nnnmkj.thai.framework.mybatis.core.mapper.BaseMapperX;
import com.nnnmkj.thai.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.nnnmkj.thai.module.course.controller.admin.lessonchapter.vo.LessonChapterPageReqVO;
import com.nnnmkj.thai.module.course.controller.app.lessonchapter.vo.AppLessonChapterPageReqVO;
import com.nnnmkj.thai.module.course.dal.dataobject.lessonchapter.LessonChapterDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * 课程章节 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface LessonChapterMapper extends BaseMapperX<LessonChapterDO> {

    default PageResult<LessonChapterDO> selectPage(LessonChapterPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<LessonChapterDO>()
                .eqIfPresent(LessonChapterDO::getCourseId, reqVO.getCourseId())
                .inIfPresent(LessonChapterDO::getCourseId, reqVO.getCourseIds())
                .eqIfPresent(LessonChapterDO::getParentId, reqVO.getParentId())
                .likeIfPresent(LessonChapterDO::getName, reqVO.getName())
                .betweenIfPresent(LessonChapterDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(LessonChapterDO::getId));
    }

    default PageResult<LessonChapterDO> selectPage(AppLessonChapterPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<LessonChapterDO>()
                .eqIfPresent(LessonChapterDO::getCourseId, reqVO.getCourseId())
                .eqIfPresent(LessonChapterDO::getParentId, reqVO.getParentId())
                .likeIfPresent(LessonChapterDO::getName, reqVO.getName())
                .betweenIfPresent(LessonChapterDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(LessonChapterDO::getId));
    }

}