package com.nnnmkj.thai.module.course.controller.admin.lessonchapter.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Schema(description = "管理后台 - 课程章节 Response VO")
@Data
@ExcelIgnoreUnannotated
public class LessonChapterRespVO {

    @Schema(description = "编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "200")
    @ExcelProperty("编号")
    private Long id;

    @Schema(description = "课程ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "100")
    @ExcelProperty("课程ID")
    private Long courseId;

    @Schema(description = "父章节ID", example = "10086")
    @ExcelProperty("父章节ID")
    private Long parentId;

    @Schema(description = "章节名称", example = "王五")
    @ExcelProperty("章节名称")
    private String name;

    @Schema(description = "章节简介")
    @ExcelProperty("章节简介")
    private String blurb;

    @Schema(description = "排序")
    @ExcelProperty("排序")
    private Integer sort;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

    @Schema(description = "课程标题", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("课程标题")
    private String title;

    @Schema(description = "父章节名称", example = "王五")
    @ExcelProperty("父章节名称")
    private String parentName;

}