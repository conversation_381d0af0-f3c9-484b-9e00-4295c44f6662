package com.nnnmkj.thai.module.course.service.assignmentrelease;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.nnnmkj.thai.framework.common.enums.UserTypeEnum;
import com.nnnmkj.thai.framework.common.exception.enums.GlobalErrorCodeConstants;
import com.nnnmkj.thai.framework.common.pojo.PageResult;
import com.nnnmkj.thai.framework.common.util.collection.CollectionUtils;
import com.nnnmkj.thai.framework.common.util.common.CommonUtils;
import com.nnnmkj.thai.framework.common.util.object.BeanUtils;
import com.nnnmkj.thai.framework.tenant.core.aop.TenantIgnore;
import com.nnnmkj.thai.framework.web.core.util.WebFrameworkUtils;
import com.nnnmkj.thai.module.course.controller.admin.assignmentanswerpaper.vo.AssignmentAnswerPaperSaveReqVO;
import com.nnnmkj.thai.module.course.controller.admin.assignmentanswerrecord.vo.AssignmentAnswerRecordSaveReqVO;
import com.nnnmkj.thai.module.course.controller.admin.assignmentrelease.vo.AssignmentReleaseCreateReqVO;
import com.nnnmkj.thai.module.course.controller.admin.assignmentrelease.vo.AssignmentReleasePageReqVO;
import com.nnnmkj.thai.module.course.controller.admin.assignmentrelease.vo.AssignmentReleaseUpdateReqVO;
import com.nnnmkj.thai.module.course.controller.admin.assignmentreleasequestion.vo.AssignmentReleaseQuestionSaveReqVO;
import com.nnnmkj.thai.module.course.controller.admin.assignmentscore.vo.AssignmentScoreSaveReqVO;
import com.nnnmkj.thai.module.course.controller.admin.membergroupassignmentrelease.vo.MemberGroupAssignmentReleaseSaveReqVO;
import com.nnnmkj.thai.module.course.controller.app.assignmentrelease.vo.*;
import com.nnnmkj.thai.module.course.controller.app.assignmentrelease.vo.AppAssignmentReleaseAnswerSubmitReqVO.QuestionAnswerItem;
import com.nnnmkj.thai.module.course.controller.app.assignmentreleasequestion.vo.AppAssignmentReleaseQuestionRespVO;
import com.nnnmkj.thai.module.course.dal.dataobject.assignment.AssignmentDO;
import com.nnnmkj.thai.module.course.dal.dataobject.assignmentanswerpaper.AssignmentAnswerPaperDO;
import com.nnnmkj.thai.module.course.dal.dataobject.assignmentanswerrecord.AssignmentAnswerRecordDO;
import com.nnnmkj.thai.module.course.dal.dataobject.assignmentquestion.AssignmentQuestionDO;
import com.nnnmkj.thai.module.course.dal.dataobject.assignmentquestion.AssignmentQuestionOptionDO;
import com.nnnmkj.thai.module.course.dal.dataobject.assignmentrelease.AssignmentReleaseDO;
import com.nnnmkj.thai.module.course.dal.dataobject.assignmentreleasequestion.AssignmentReleaseQuestionDO;
import com.nnnmkj.thai.module.course.dal.dataobject.assignmentreleasequestion.AssignmentReleaseQuestionOptionDO;
import com.nnnmkj.thai.module.course.dal.dataobject.assignmentscore.AssignmentScoreDO;
import com.nnnmkj.thai.module.course.dal.dataobject.lesson.LessonDO;
import com.nnnmkj.thai.module.course.dal.dataobject.membergroupassignmentrelease.MemberGroupAssignmentReleaseDO;
import com.nnnmkj.thai.module.course.dal.mysql.assignmentrelease.AssignmentReleaseMapper;
import com.nnnmkj.thai.module.course.dal.redis.AssignmentReminderRedisDAO;
import com.nnnmkj.thai.module.course.enums.AssignmentDistributionTargetEnum;
import com.nnnmkj.thai.module.course.enums.AssignmentScoringMechanismEnum;
import com.nnnmkj.thai.module.course.enums.ErrorCodeConstants;
import com.nnnmkj.thai.module.course.service.assignment.AssignmentService;
import com.nnnmkj.thai.module.course.service.assignmentanswerpaper.AssignmentAnswerPaperService;
import com.nnnmkj.thai.module.course.service.assignmentanswerrecord.AssignmentAnswerRecordService;
import com.nnnmkj.thai.module.course.service.assignmentquestion.AssignmentQuestionService;
import com.nnnmkj.thai.module.course.service.assignmentreleasequestion.AssignmentReleaseQuestionService;
import com.nnnmkj.thai.module.course.service.assignmentscore.AssignmentScoreService;
import com.nnnmkj.thai.module.course.service.lesson.LessonService;
import com.nnnmkj.thai.module.course.service.membergroupassignmentrelease.MemberGroupAssignmentReleaseService;
import com.nnnmkj.thai.module.member.api.group.MemberGroupApi;
import com.nnnmkj.thai.module.member.api.group.dto.MemberGroupRespDTO;
import com.nnnmkj.thai.module.member.api.user.MemberUserApi;
import com.nnnmkj.thai.module.member.api.user.dto.MemberUserRespDTO;
import com.nnnmkj.thai.module.system.api.social.SocialClientApi;
import com.nnnmkj.thai.module.system.api.social.dto.SocialWxaSubscribeMessageSendReqDTO;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.extern.slf4j.Slf4j;
import jakarta.annotation.Resource;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

import static com.nnnmkj.thai.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.nnnmkj.thai.module.course.enums.ErrorCodeConstants.*;
import static com.nnnmkj.thai.module.course.enums.MessageTemplateConstants.WXA_ASSIGNMENT;
import static com.nnnmkj.thai.module.course.enums.MessageTemplateConstants.WXA_ASSIGNMENT_SUBMISSION_REMINDER;

/**
 * 课程作业发布 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
@Slf4j
public class AssignmentReleaseServiceImpl implements AssignmentReleaseService {

    @Resource
    private AssignmentReleaseMapper assignmentReleaseMapper;
    @Resource
    private AssignmentService assignmentService;
    @Resource
    private AssignmentQuestionService assignmentQuestionService;
    @Resource
    private AssignmentReleaseQuestionService assignmentReleaseQuestionService;
    @Resource
    private MemberGroupAssignmentReleaseService memberGroupAssignmentReleaseService;
    @Resource
    private AssignmentAnswerPaperService assignmentAnswerPaperService;
    @Resource
    private AssignmentAnswerRecordService assignmentAnswerRecordService;
    @Resource
    private AssignmentScoreService assignmentScoreService;
    @Resource
    private LessonService lessonService;

    @Resource
    private MemberUserApi memberUserApi;
    @Resource
    private MemberGroupApi memberGroupApi;
    @Resource
    private SocialClientApi socialClientApi;

    @Resource
    private AssignmentReminderRedisDAO assignmentReminderRedisDAO;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long createAssignmentRelease(AssignmentReleaseCreateReqVO createReqVO) {
        // 插入
        AssignmentReleaseDO assignmentRelease = BeanUtils.toBean(createReqVO, AssignmentReleaseDO.class);
        assignmentReleaseMapper.insert(assignmentRelease);

        // 获取原题目
        Long assignmentId = createReqVO.getAssignmentId();
        if (assignmentId == null) {
            throw exception(GlobalErrorCodeConstants.BAD_REQUEST);
        }
        List<AssignmentQuestionDO> questionDOS = assignmentQuestionService.getAssignmentQuestionListByAssignmentId(assignmentId);

        if (CollUtil.isEmpty(questionDOS)) {
            throw exception(ErrorCodeConstants.ASSIGNMENT_QUESTION_EMPTY);
        }

        // 随机抽题
        if (createReqVO.getIsRandom()) {
            Integer randomCount = createReqVO.getRandomCount();
            // 如果题目数量不足，抛出异常
            if (questionDOS.size() < randomCount) {
                throw exception(ErrorCodeConstants.ASSIGNMENT_RANDOM_QUESTION_INSUFFICIENT);
            }
            // 打乱题目顺序并截取指定数量
            Collections.shuffle(questionDOS);
            questionDOS = questionDOS.subList(0, randomCount);
        }

        // 分配成绩
        AssignmentDO assignment = assignmentService.getAssignment(assignmentId);
        Integer scoringMechanism = assignment.getScoringMechanism();
        AssignmentScoringMechanismEnum anEnum = AssignmentScoringMechanismEnum.valueOf(scoringMechanism);

        // 如果是百分制评分机制，平均分配每个题目的分数
        if (Objects.requireNonNull(anEnum) == AssignmentScoringMechanismEnum.PERCENTAGE) {
            int totalQuestions = questionDOS.size();

            // 使用 BigDecimal 避免精度丢失
            BigDecimal totalScore = BigDecimal.valueOf(100);
            if (totalQuestions <= 0) {
                throw exception(GlobalErrorCodeConstants.BAD_REQUEST, "题目数量不能为零");
            }
            BigDecimal perQuestionScore = totalScore.divide(BigDecimal.valueOf(totalQuestions), 4, RoundingMode.DOWN); // 多保留几位用于补差

            BigDecimal distributed = BigDecimal.ZERO;
            for (int i = 0; i < totalQuestions; i++) {
                BigDecimal scoreToAssign = perQuestionScore;

                // 如果是最后一题，补差
                if (i == totalQuestions - 1) {
                    scoreToAssign = totalScore.subtract(distributed);
                }

                // 保留两位小数，四舍五入
                scoreToAssign = scoreToAssign.setScale(2, RoundingMode.HALF_UP);

                // 设置分数到对应对象
                AssignmentQuestionDO questionDO = questionDOS.get(i);
                questionDO.setScore(scoreToAssign.doubleValue());

                distributed = distributed.add(scoreToAssign);
            }
        }

        // 查询题目选项
        List<Long> questionIds = questionDOS.stream()
                .map(AssignmentQuestionDO::getId)
                .collect(Collectors.toList());
        Map<Long, List<AssignmentQuestionOptionDO>> questionOptionsMap = assignmentQuestionService.getAssignmentQuestionOptionMapByQuestionIds(questionIds);

        questionDOS.forEach(questionDO -> {
            // 插入题目
            AssignmentReleaseQuestionSaveReqVO releaseQuestionSaveReq = BeanUtils.toBean(questionDO, AssignmentReleaseQuestionSaveReqVO.class);
            releaseQuestionSaveReq.setId(null);
            releaseQuestionSaveReq.setAssignmentReleaseId(assignmentRelease.getId());

            Long releaseQuestionId = assignmentReleaseQuestionService.createAssignmentReleaseQuestion(releaseQuestionSaveReq);

            // 插入选项
            List<AssignmentQuestionOptionDO> assignmentQuestionOptionDOS = questionOptionsMap.get(questionDO.getId());
            if (assignmentQuestionOptionDOS != null) {
                List<AssignmentReleaseQuestionOptionDO> releaseQuestionOptionDOS = BeanUtils.toBean(assignmentQuestionOptionDOS, AssignmentReleaseQuestionOptionDO.class);
                assignmentReleaseQuestionService.updateAssignmentReleaseQuestionOptions(releaseQuestionId, releaseQuestionOptionDOS);
            }
        });

        // 处理发放对象关联
        MemberGroupAssignmentReleaseSaveReqVO saveReqVO = new MemberGroupAssignmentReleaseSaveReqVO()
                .setCourseId(createReqVO.getCourseId())
                .setAssignmentReleaseId(assignmentRelease.getId());
        if (CollUtil.isEmpty(createReqVO.getGroupIds()) && CollUtil.isEmpty(createReqVO.getUserIds())) {
            throw exception(GlobalErrorCodeConstants.BAD_REQUEST);
        }
        saveReqVO.setGroupIds(createReqVO.getGroupIds())
                .setUserIds(createReqVO.getUserIds());
        memberGroupAssignmentReleaseService.createMemberGroupAssignmentRelease(saveReqVO);

        // 获取发放对象类型（班级或个人）
        Integer target = createReqVO.getDistributionTarget();
        AssignmentDistributionTargetEnum distributionTargetEnum = AssignmentDistributionTargetEnum.valueOf(target);

        // 根据发放对象类型获取目标用户列表
        List<MemberUserRespDTO> targetUserList = switch (distributionTargetEnum) {
            case CLASS -> memberUserApi.getUserListByGroupIds(createReqVO.getGroupIds());  // 如果是班级，获取班级内所有用户
            case PERSON -> memberUserApi.getUserList(createReqVO.getUserIds());            // 如果是个人，直接获取指定用户
        };

        // 获取所有相关班级信息
        List<MemberGroupRespDTO> allGroups = memberGroupApi.getGroupList(createReqVO.getGroupIds());

        // 构建班级ID到班级名称的映射，方便后续快速查找班级名称
        Map<Long, String> groupIdToNameMap = CollectionUtils.convertMap(allGroups, MemberGroupRespDTO::getId, MemberGroupRespDTO::getName);

        // 构建用户ID到其加入的分组列表的映射
        // 对每个用户，获取其所属班级ID列表，然后转换为班级名称列表
        Map<Long, List<String>> userGroupNamesMap = CollectionUtils.convertMap(
                targetUserList,
                MemberUserRespDTO::getId,
                user -> CollectionUtils.convertList(user.getGroupIds(), groupIdToNameMap::get, Objects::nonNull)
        );

        // 异步发送作业发布通知
        sendAssignmentReleaseNotificationsAsync(assignmentRelease, createReqVO.getCourseId(), targetUserList, userGroupNamesMap);
        
        // 异步处理督促提醒
        if (Boolean.TRUE.equals(createReqVO.getIsUrge()) && createReqVO.getUrgeDeadline() != null && createReqVO.getUrgeDeadline() > 0) {
            saveAssignmentReminderAsync(assignmentRelease, createReqVO, allGroups);
        }

        // 返回
        return assignmentRelease.getId();
    }

    @Override
    public void updateAssignmentRelease(AssignmentReleaseUpdateReqVO updateReqVO) {
        // 校验存在
        validateAssignmentReleaseExists(updateReqVO.getId());
        // 更新
        AssignmentReleaseDO updateObj = BeanUtil.copyProperties(updateReqVO, AssignmentReleaseDO.class);
        assignmentReleaseMapper.updateById(updateObj);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteAssignmentRelease(Long id) {
        // 校验存在
        validateAssignmentReleaseExists(id);

        // 删除
        assignmentReleaseMapper.deleteById(id);
        assignmentReleaseQuestionService.deleteQuestionByAssignmentReleaseId(id);
        memberGroupAssignmentReleaseService.deleteMemberGroupAssignmentReleaseByAssignmentReleaseId(id);
    }

    private void validateAssignmentReleaseExists(Long id) {
        if (assignmentReleaseMapper.selectById(id) == null) {
            throw exception(ASSIGNMENT_RELEASE_NOT_EXISTS);
        }
    }

    @Override
    public AssignmentReleaseDO getAssignmentRelease(Long id) {
        return assignmentReleaseMapper.selectById(id);
    }

    @Override
    public AppAssignmentReleaseDetailRespVO getAssignmentReleaseDetail(Long id, Long userId) {
        // 查询作业发布信息，仅根据ID查询，去掉创建者限制
        AssignmentReleaseDO releaseDO = assignmentReleaseMapper.selectById(id);
        if (releaseDO == null) {
            throw exception(ASSIGNMENT_RELEASE_NOT_EXISTS);
        }

        // 转换为返回对象
        AppAssignmentReleaseDetailRespVO result = BeanUtil.copyProperties(releaseDO, AppAssignmentReleaseDetailRespVO.class);

        // 查询题目总数
        List<AssignmentQuestionDO> questionList = assignmentQuestionService.getAssignmentQuestionListByAssignmentId(releaseDO.getAssignmentId());
        result.setQuestionCount(CollectionUtil.size(questionList));

        // 获取关联的班级信息
        MemberGroupAssignmentReleaseDO groupAssignment = memberGroupAssignmentReleaseService.getMemberGroupAssignmentReleaseByAssignmentReleaseId(id);
        if (groupAssignment == null) {
            throw exception(MEMBER_GROUP_ASSIGNMENT_RELEASE_NOT_EXISTS);
        }
        List<Long> groupIds = groupAssignment.getGroupIds();

        // 查询班级DTO并转换为响应DTO
        List<MemberGroupRespDTO> groupRespList = memberGroupApi.getGroupList(groupIds);
        result.setGroups(groupRespList);

        // 获取关联用户列表
        List<Long> userIds = groupAssignment.getUserIds();
        if (CollUtil.isEmpty(userIds)) {
            // 若无显式指定用户，则从班级中获取全部用户
            List<MemberUserRespDTO> userInGroups = memberUserApi.getUserListByGroupIds(groupIds);
            userIds = CollectionUtils.convertList(userInGroups, MemberUserRespDTO::getId);
        }

        // 查询用户信息并设置到结果中
        List<MemberUserRespDTO> userList = memberUserApi.getUserList(userIds);
        result.setUsers(userList);
        return result;
    }

    @Override
    public PageResult<AssignmentReleaseDO> getAssignmentReleaseVOPage(AssignmentReleasePageReqVO pageReqVO) {
        return assignmentReleaseMapper.selectPage(pageReqVO);
    }

    @Override
    public PageResult<AppAssignmentReleaseRespVO> getAssignmentReleaseVOPage(AppAssignmentReleasePageReqVO pageReqVO) {
        return assignmentReleaseMapper.selectPage(pageReqVO);
    }

    @Override
    public PageResult<AppAssignmentReleaseSimpleRespVO> getAssignmentReleaseSimpleVOPage(Long userId, Long groupId, AppAssignmentReleasePageReqVO pageReqVO) {
        // 获取分页数据
        PageResult<AppAssignmentReleaseRespVO> pageResult = getAssignmentReleaseVOPage(pageReqVO);
        PageResult<AppAssignmentReleaseSimpleRespVO> result = BeanUtils.toBean(pageResult, AppAssignmentReleaseSimpleRespVO.class);

        // 提取作业发布ID列表
        List<Long> assignmentReleaseIds = CollectionUtils.convertList(result.getList(), AppAssignmentReleaseSimpleRespVO::getId);

        // 校验用户对这些作业发布的班级访问权限，过滤出用户有权限访问的作业
        List<AppAssignmentReleaseSimpleRespVO> filteredList = new ArrayList<>();
        for (AppAssignmentReleaseSimpleRespVO item : result.getList()) {
            try {
                // 检查当前用户是否有权限访问指定班级的作业发布
                List<MemberUserRespDTO> publishedUsers = memberGroupAssignmentReleaseService.getPublishedUsersByReleaseAndGroup(userId, groupId, item.getId());
                if (CollUtil.isNotEmpty(publishedUsers)) {
                    filteredList.add(item);
                }
            } catch (Exception e) {
                // 如果没有权限或发生异常，则跳过该作业发布
                continue;
            }
        }

        // 更新过滤后的作业发布ID列表
        assignmentReleaseIds = CollectionUtils.convertList(filteredList, AppAssignmentReleaseSimpleRespVO::getId);

        // 查询关联的答题记录（只有当作业发布ID列表非空时才查询）
        List<AssignmentAnswerPaperDO> papers = new ArrayList<>();
        if (CollUtil.isNotEmpty(assignmentReleaseIds)) {
            papers = assignmentAnswerPaperService.getAssignmentAnswerPaperListLast(userId, assignmentReleaseIds);
        }

        // 构建是否提交的映射表
        Map<Long, Boolean> submitMap = CollectionUtils.convertMap(papers, AssignmentAnswerPaperDO::getAssignmentReleaseId, AssignmentAnswerPaperDO::getIsSubmit);

        // 填充提交状态
        filteredList.forEach(item -> item.setIsSubmit(submitMap.getOrDefault(item.getId(), Boolean.FALSE)));

        // 重新设置分页结果
        result.setList(filteredList);

        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void answerSubmitAssignmentRelease(Long userId, AppAssignmentReleaseAnswerSubmitReqVO reqVO) {
        Long assignmentReleaseId = reqVO.getAssignmentReleaseId();
        Long courseId = reqVO.getCourseId();
        Boolean isSubmit = reqVO.getIsSubmit();

        List<QuestionAnswerItem> questionAnswerItems = reqVO.getQuestionAnswerItems();
        List<AssignmentReleaseQuestionDO> questions = assignmentReleaseQuestionService.getAssignmentReleaseQuestionByAssignmentReleaseId(assignmentReleaseId);
        Set<Long> questionIds = CollectionUtils.convertSet(questions, AssignmentReleaseQuestionDO::getId);

        // 获取或创建作答试卷记录
        AssignmentAnswerPaperDO paper = assignmentAnswerPaperService.getAssignmentAnswerPaperLast(userId, assignmentReleaseId);
        Long paperId;
        if (paper != null) {
            paperId = paper.getId();
            // 检查是否需要更新"是否提交"的状态
            if (!Objects.equals(paper.getIsSubmit(), Boolean.TRUE) && !Objects.equals(paper.getIsSubmit(), isSubmit)) {
                AssignmentAnswerPaperSaveReqVO updateReqVO = new AssignmentAnswerPaperSaveReqVO();
                updateReqVO.setId(paperId);
                updateReqVO.setCourseId(courseId);
                updateReqVO.setAssignmentReleaseId(assignmentReleaseId);
                updateReqVO.setUserId(userId);
                updateReqVO.setIsSubmit(isSubmit);
                assignmentAnswerPaperService.updateAssignmentAnswerPaper(updateReqVO);
            }
        } else {
            AssignmentAnswerPaperSaveReqVO saveReqVO = new AssignmentAnswerPaperSaveReqVO();
            saveReqVO.setCourseId(courseId);
            saveReqVO.setAssignmentReleaseId(assignmentReleaseId);
            saveReqVO.setUserId(userId);
            saveReqVO.setIsSubmit(isSubmit);
            paperId = assignmentAnswerPaperService.createAssignmentAnswerPaper(saveReqVO);
        }

        // 处理作答记录：先删除旧数据，再创建新数据
        assignmentAnswerRecordService.deleteAssignmentAnswerRecord(assignmentReleaseId, paperId, questionIds, userId);

        // 构建标准答案映射关系
        Map<Long, String> answerMap = CollectionUtils.convertMap(questions, AssignmentReleaseQuestionDO::getId, AssignmentReleaseQuestionDO::getAnswer);

        // 构建作答记录保存参数
        List<AssignmentAnswerRecordSaveReqVO> saveReqVOs = new ArrayList<>();
        for (QuestionAnswerItem item : questionAnswerItems) {
            AssignmentAnswerRecordSaveReqVO saveReqVO = new AssignmentAnswerRecordSaveReqVO();
            saveReqVO.setCourseId(courseId);
            saveReqVO.setAssignmentReleaseId(assignmentReleaseId);
            saveReqVO.setAnswerPaperId(paperId);
            saveReqVO.setQuestionId(item.getQuestionId());
            saveReqVO.setUserId(userId);
            saveReqVO.setAnswer(item.getUserAnswer());

            // 判断用户答案是否正确
            String correctAnswer = answerMap.get(item.getQuestionId());
            saveReqVO.setIsCorrect(correctAnswer != null && correctAnswer.equals(item.getUserAnswer()));

            saveReqVOs.add(saveReqVO);
        }

        // 批量保存作答记录
        assignmentAnswerRecordService.createBatchAssignmentAnswerRecord(saveReqVOs);

        // 如果是提交操作，则计算成绩并保存
        if (isSubmit) {
            // 建立题目ID与分值的映射关系
            Map<Long, Double> scoreMap = CollectionUtils.convertMap(questions, AssignmentReleaseQuestionDO::getId, AssignmentReleaseQuestionDO::getScore);

            // 统计答题情况
            int allCount = saveReqVOs.size();
            if (allCount == 0) {
                throw exception(ASSIGNMENT_ANSWER_ITEMS_EMPTY);
            }
            int correctCount = CollectionUtil.count(saveReqVOs, AssignmentAnswerRecordSaveReqVO::getIsCorrect);
            int errorCount = allCount - correctCount;

            // 根据每道题的得分累加总分
            double score = 0.0;
            for (AssignmentAnswerRecordSaveReqVO record : saveReqVOs) {
                if (record.getIsCorrect()) {
                    Long questionId = record.getQuestionId();
                    Double questionScore = scoreMap.get(questionId);
                    if (questionScore != null) {
                        score += questionScore;
                    }
                }
            }
            // 使用 BigDecimal 进行四舍五入处理并保持 double 类型
            BigDecimal scoreBD = BigDecimal.valueOf(score).setScale(2, RoundingMode.HALF_UP);
            score = scoreBD.doubleValue();

            // 构建成绩保存参数
            AssignmentScoreSaveReqVO saveReqVO = new AssignmentScoreSaveReqVO();
            saveReqVO.setCourseId(courseId);
            saveReqVO.setAssignmentReleaseId(assignmentReleaseId);
            saveReqVO.setAnswerPaperId(paperId);
            saveReqVO.setUserId(userId);
            saveReqVO.setAllCount(allCount);
            saveReqVO.setCorrectCount(correctCount);
            saveReqVO.setErrorCount(errorCount);
            saveReqVO.setScore(score);
            assignmentScoreService.createAssignmentScore(saveReqVO);
        }
    }

    @Override
    public AppAssignmentReleaseAnswerReportRespVO answerReportAssignmentRelease(Long id, Long userId) {
        AppAssignmentReleaseAnswerReportRespVO respVO = new AppAssignmentReleaseAnswerReportRespVO();

        // 查询学生最近一次作业成绩
        AssignmentScoreDO assignmentScore = assignmentScoreService.getAssignmentScoreLast(id, userId);
        if (ObjectUtils.anyNull(assignmentScore, assignmentScore.getScore())) {
            throw exception(ErrorCodeConstants.ASSIGNMENT_SCORE_NOT_EXISTS);
        }
        respVO.setStudentScore(assignmentScore.getScore());

        // 获取本次作业的所有题目
        List<AssignmentReleaseQuestionDO> questionList = assignmentReleaseQuestionService.getAssignmentReleaseQuestionByAssignmentReleaseId(id);
        if (CollectionUtil.isEmpty(questionList)) {
            throw exception(ASSIGNMENT_RELEASE_QUESTION_NOT_EXISTS);
        }
        Map<Long, AssignmentReleaseQuestionDO> questionMap = CollectionUtils.convertMap(questionList, AssignmentReleaseQuestionDO::getId);

        // 获取用户最近一次作答记录
        List<AssignmentAnswerRecordDO> answerRecords = assignmentAnswerRecordService.getAssignmentAnswerRecordListLast(id, userId);
        if (CollectionUtil.isEmpty(answerRecords)) {
            throw exception(ASSIGNMENT_ANSWER_RECORD_NOT_EXISTS);
        }

        // 获取题目选项信息
        List<AppAssignmentReleaseQuestionRespVO> questionRespVOS = assignmentReleaseQuestionService.getAssignmentReleaseQuestionList(id);
        if (CollectionUtil.isEmpty(questionRespVOS)) {
            throw exception(ErrorCodeConstants.ASSIGNMENT_RELEASE_QUESTION_OPTION_NOT_EXISTS);
        }
        Map<Long, List<AssignmentReleaseQuestionOptionDO>> optionMap = CollectionUtils.convertMap(
                questionRespVOS,
                AppAssignmentReleaseQuestionRespVO::getId,
                AppAssignmentReleaseQuestionRespVO::getOptionList);

        List<AppAssignmentReleaseAnswerReportRespVO.QuestionItem> questionItems = new ArrayList<>();

        // 组装答题报告详情
        for (AssignmentAnswerRecordDO record : answerRecords) {
            Long questionId = record.getQuestionId();

            AssignmentReleaseQuestionDO question = questionMap.get(questionId);
            if (question == null) {
                continue;
            }

            AppAssignmentReleaseAnswerReportRespVO.QuestionItem item = new AppAssignmentReleaseAnswerReportRespVO.QuestionItem();
            item.setStem(question.getQuestionStem());
            item.setQuestionType(question.getQuestionType());
            item.setCorrectAnswer(question.getAnswer());
            item.setUserSelectedAnswer(record.getAnswer());
            item.setIsCorrect(record.getIsCorrect());
            item.setScore(question.getScore());

            // 设置题目选项
            List<AssignmentReleaseQuestionOptionDO> options = optionMap.get(questionId);
            item.setOptions(options);

            questionItems.add(item);
        }

        respVO.setQuestionList(questionItems);
        return respVO;
    }

    @Override
    public AppAssignmentReleaseAnswerStatisticsRespVO statisticsReportAssignmentRelease(Long userId, Long groupId, Long assignmentReleaseId) {
        AppAssignmentReleaseAnswerStatisticsRespVO respVO = new AppAssignmentReleaseAnswerStatisticsRespVO();
        respVO.setAverageScore(0.0);
        respVO.setQuestionList(new ArrayList<>());

        // 获取当前班级中已发布该作业的可见用户列表
        List<MemberUserRespDTO> publishedUsers = memberGroupAssignmentReleaseService.getPublishedUsersByReleaseAndGroup(userId, groupId, assignmentReleaseId);
        if (CollUtil.isEmpty(publishedUsers)) {
            throw exception(MEMBER_GROUP_ASSIGNMENT_RELEASE_NOT_EXISTS);
        }
        List<Long> userIds = CollectionUtils.convertList(publishedUsers, MemberUserRespDTO::getId);

        // 获取所有用户的作答试卷并过滤出已提交的
        List<AssignmentAnswerPaperDO> submittedPapers = assignmentAnswerPaperService.getAssignmentAnswerPaperListLast(userIds, assignmentReleaseId);
        if (CollUtil.isEmpty(submittedPapers)) {
            return respVO;
        }

        List<Long> submittedUserIds = CollectionUtils.convertList(
                CollectionUtils.filterList(submittedPapers, AssignmentAnswerPaperDO::getIsSubmit),
                AssignmentAnswerPaperDO::getUserId
        );

        // 获取发布的题目列表
        List<AppAssignmentReleaseQuestionRespVO> questionList = assignmentReleaseQuestionService.getAssignmentReleaseQuestionList(assignmentReleaseId);
        if (CollUtil.isEmpty(questionList)) {
            return respVO;
        }

        // 获取所有用户的最新作答记录并按题目分组
        List<AssignmentAnswerRecordDO> answerRecords = assignmentAnswerRecordService.getAssignmentAnswerRecordListLast(assignmentReleaseId, submittedUserIds);
        if (CollUtil.isEmpty(answerRecords)) {
            return respVO;
        }
        Map<Long, List<AssignmentAnswerRecordDO>> questionRecordMap = CollectionUtils.convertMultiMap(answerRecords, AssignmentAnswerRecordDO::getQuestionId);

        // 查询成绩数据用于计算平均分
        List<AssignmentScoreDO> scoreList = assignmentScoreService.getAssignmentScoreListLast(assignmentReleaseId, userIds);
        double averageScore = 0.0;
        if (CollUtil.isNotEmpty(scoreList)) {
            int totalScores = scoreList.size();
            if (totalScores > 0) {
                double sumScore = scoreList.stream()
                        .mapToDouble(AssignmentScoreDO::getScore)
                        .sum();
                averageScore = sumScore / totalScores;
            }
        }

        List<AppAssignmentReleaseAnswerStatisticsRespVO.QuestionItem> questionStatistics = new ArrayList<>();
        for (AppAssignmentReleaseQuestionRespVO question : questionList) {
            Long questionId = question.getId();
            List<AssignmentAnswerRecordDO> questionAnswers = questionRecordMap.get(questionId);

            AppAssignmentReleaseAnswerStatisticsRespVO.QuestionItem item = new AppAssignmentReleaseAnswerStatisticsRespVO.QuestionItem();
            item.setStem(question.getQuestionStem());
            item.setQuestionType(question.getQuestionType());

            if (CollUtil.isNotEmpty(questionAnswers)) {
                // 计算正确率
                int totalAnswers = questionAnswers.size();
                int correctCount = CollectionUtil.count(questionAnswers, AssignmentAnswerRecordDO::getIsCorrect);
                double correctRate = ((double) correctCount / totalAnswers) * 100;
                item.setCorrectRate(correctRate);
            } else {
                item.setCorrectRate(0.0);
            }
            questionStatistics.add(item);
        }

        respVO.setAverageScore(averageScore);
        respVO.setQuestionList(questionStatistics);
        return respVO;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteAssignmentRelease(Long id, Long userId) {
        // 校验存在
        validateAssignmentReleaseExists(id, userId);

        // 删除
        assignmentReleaseMapper.deleteById(id);
        assignmentReleaseQuestionService.deleteQuestionByAssignmentReleaseId(id);
        memberGroupAssignmentReleaseService.deleteMemberGroupAssignmentReleaseByAssignmentReleaseId(id);
    }

    private void validateAssignmentReleaseExists(Long id, Long userId) {
        String creatorQuery = CommonUtils.getCreatorQuery(userId, WebFrameworkUtils.getLoginUserType());
        AssignmentReleaseDO exists = assignmentReleaseMapper.selectOne(AssignmentReleaseDO::getId, id, AssignmentReleaseDO::getCreator, creatorQuery);
        if (exists == null) {
            throw exception(ASSIGNMENT_RELEASE_NOT_EXISTS);
        }
    }


    /**
     * 发送作业发布消息
     *
     * @param userId     用户编号
     * @param title      作业标题
     * @param courseName 课程名称
     * @param groupName  班级名称
     * @param startTime  开始时间
     * @param endTile    截止时间
     */
    @Async
    public void sendAssignmentReleaseMessage(Long userId, String title, String courseName, String groupName, LocalDateTime startTime, LocalDateTime endTile) {
        // 格式化时间为 "2019-11-01 15:00" 格式
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm");
        String formattedStartTime = startTime.format(formatter);
        String formattedEndTime = endTile.format(formatter);

        socialClientApi.sendWxaSubscribeMessage(new SocialWxaSubscribeMessageSendReqDTO()
                .setUserId(userId)
                .setUserType(UserTypeEnum.MEMBER.getValue())
                .setTemplateTitle(WXA_ASSIGNMENT)
                .setPage("pages/course/assignment/index")
                .addMessage("thing15", title)
                .addMessage("thing5", courseName)
                .addMessage("thing12", groupName)
                .addMessage("time16", formattedStartTime)
                .addMessage("date4", formattedEndTime)
        );
    }

    /**
     * 发送作业提交提醒消息
     *
     * @param userId     用户编号
     * @param title      作业标题
     * @param courseName 课程名称
     * @param groupName  班级名称
     * @param deadline   截止时间
     */
    @Async
    public void sendAssignmentSubmissionReminderMessage(Long userId, String title, String courseName, String groupName, LocalDateTime deadline) {
        try {
            // 格式化时间为 "yyyy-MM-dd HH:mm" 格式
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm");
            String formattedDeadline = deadline.format(formatter);
            
            // 固定备注为"请及时完成作业提交"
            String notes = "请及时完成作业提交";

            socialClientApi.sendWxaSubscribeMessage(new SocialWxaSubscribeMessageSendReqDTO()
                    .setUserId(userId)
                    .setUserType(UserTypeEnum.MEMBER.getValue())
                    .setTemplateTitle(WXA_ASSIGNMENT_SUBMISSION_REMINDER)
                    .setPage("pages/course/assignment/index")
                    .addMessage("thing9", title)
                    .addMessage("thing7", courseName)
                    .addMessage("thing11", groupName)
                    .addMessage("time12", formattedDeadline)
                    .addMessage("thing5", notes)
            );
        } catch (Exception e) {
            // 对于用户未订阅等情况，记录日志但不抛出异常，避免中断批量发送流程
            log.info("[sendAssignmentSubmissionReminderMessage][userId({}) 跳过发送作业提交提醒，原因：{}]", userId, e.getMessage());
        }
    }

    /**
     * 定时检查并发送作业提交提醒
     * 每小时执行一次，检查是否有需要发送提醒的作业
     */
    @Scheduled(cron = "0 0 * * * ?")
    @TenantIgnore
    public void checkAndSendAssignmentReminders() {
        try {
            // 获取当前时间的小时格式
            LocalDateTime now = LocalDateTime.now();
            String currentHour = now.format(DateTimeFormatter.ofPattern("yyyyMMddHH"));
            
            // 从Redis获取当前小时需要发送提醒的作业ID列表
            List<Long> assignmentReleaseIds = assignmentReminderRedisDAO.getAssignmentRemindersByTime(currentHour);
            
            if (CollUtil.isEmpty(assignmentReleaseIds)) {
                log.info("[checkAndSendAssignmentReminders][当前时间{}没有需要发送的作业提醒]", currentHour);
                return;
            }
            
            log.info("[checkAndSendAssignmentReminders][当前时间{}有{}个作业需要发送提醒]", currentHour, assignmentReleaseIds.size());
            
            // 处理每个需要发送提醒的作业
            for (Long assignmentReleaseId : assignmentReleaseIds) {
                sendAssignmentRemindersFromRedis(assignmentReleaseId);
            }
        } catch (Exception e) {
            log.error("定时发送作业提交提醒失败", e);
        }
    }

    /**
     * 从Redis获取作业提醒信息并发送提醒消息
     *
     * @param assignmentReleaseId 作业发布ID
     */
    private void sendAssignmentRemindersFromRedis(Long assignmentReleaseId) {
        try {
            // 从Redis获取作业提醒信息
            Map<String, Object> reminderInfo = assignmentReminderRedisDAO.getAssignmentReminderInfo(assignmentReleaseId);
            if (reminderInfo == null) {
                log.warn("[sendAssignmentRemindersFromRedis][作业{}的提醒信息不存在]", assignmentReleaseId);
                return;
            }
            
            // 提取基本提醒信息
            String title = (String) reminderInfo.get("title");
            String courseName = (String) reminderInfo.get("courseName");
            
            // 修复类型转换问题：将endTime从字符串转换为LocalDateTime
            LocalDateTime endTime;
            Object endTimeObj = reminderInfo.get("endTime");
            if (endTimeObj instanceof LocalDateTime) {
                endTime = (LocalDateTime) endTimeObj;
            } else if (endTimeObj instanceof String) {
                endTime = LocalDateTime.parse((String) endTimeObj);
            } else {
                log.error("[sendAssignmentRemindersFromRedis][作业{}的截止时间格式错误: {}]", assignmentReleaseId, endTimeObj);
                return;
            }
            
            Integer urgeDeadline = Integer.valueOf(reminderInfo.get("urgeDeadline").toString());
            
            // 获取班级信息列表
            List<Map<String, Object>> groups = (List<Map<String, Object>>) reminderInfo.get("groups");
            if (CollUtil.isEmpty(groups)) {
                log.warn("[sendAssignmentRemindersFromRedis][作业{}没有班级信息]", assignmentReleaseId);
                return;
            }
            
            // 记录所有用户总数和已发送提醒的用户总数
            int totalUserCount = 0;
            int totalSentCount = 0;
            
            // 遍历每个班级，为其中的用户发送提醒
            for (Map<String, Object> groupInfo : groups) {
                Long groupId = Long.valueOf(groupInfo.get("groupId").toString());
                String groupName = (String) groupInfo.get("groupName");
                
                // 获取用户组中的所有用户
                List<MemberUserRespDTO> users = memberUserApi.getUserListByGroupId(groupId);
                if (CollUtil.isEmpty(users)) {
                    log.warn("[sendAssignmentRemindersFromRedis][组{}没有用户]", groupId);
                    continue;
                }
                
                totalUserCount += users.size();
                
                // 获取用户ID列表
                List<Long> userIds = users.stream().map(MemberUserRespDTO::getId).collect(Collectors.toList());
                
                // 过滤掉已发送提醒的用户
                Set<Long> alreadySentUsers = assignmentReminderRedisDAO.getAlreadySentUsers(assignmentReleaseId, userIds);
                totalSentCount += alreadySentUsers.size();
                
                List<MemberUserRespDTO> usersToSend = users.stream()
                        .filter(user -> !alreadySentUsers.contains(user.getId()))
                        .collect(Collectors.toList());
                
                if (CollUtil.isEmpty(usersToSend)) {
                    log.info("[sendAssignmentRemindersFromRedis][组{}的所有用户都已发送过提醒，跳过]", groupId);
                    continue;
                }
                
                // 为需要发送的用户发送提醒消息
                List<Long> successSentUserIds = new ArrayList<>();
                for (MemberUserRespDTO user : usersToSend) {
                    try {
                        sendAssignmentSubmissionReminderMessage(
                                user.getId(),      // 用户ID
                                title,             // 作业标题
                                courseName,        // 课程名称
                                groupName,         // 用户所在班级名称
                                endTime            // 作业截止时间
                        );
                        successSentUserIds.add(user.getId());
                    } catch (Exception e) {
                        log.warn("[sendAssignmentRemindersFromRedis][用户{}发送提醒失败: {}]", user.getId(), e.getMessage());
                    }
                }
                
                // 批量标记成功发送的用户
                if (CollUtil.isNotEmpty(successSentUserIds)) {
                    assignmentReminderRedisDAO.batchMarkReminderSent(assignmentReleaseId, successSentUserIds);
                    log.info("[sendAssignmentRemindersFromRedis][作业{}组{}成功发送提醒给{}个用户]",
                            assignmentReleaseId, groupId, successSentUserIds.size());
                    totalSentCount += successSentUserIds.size();
                }
            }
            
            // 检查是否所有用户都已发送提醒，如果是则删除Redis中的提醒信息
            if (totalSentCount >= totalUserCount) {
                // 获取提醒时间
                LocalDateTime reminderTime = endTime.minusHours(urgeDeadline);
                assignmentReminderRedisDAO.deleteAssignmentReminderInfo(assignmentReleaseId, reminderTime);
                log.info("[sendAssignmentRemindersFromRedis][作业{}的所有用户都已发送提醒，删除提醒信息]", assignmentReleaseId);
            }
        } catch (Exception e) {
            log.error("发送作业提交提醒失败，作业发布ID: {}", assignmentReleaseId, e);
        }
    }

    /**
     * 异步发送作业发布通知
     *
     * @param assignmentRelease 作业发布信息
     * @param courseId 课程ID
     * @param targetUserList 目标用户列表
     * @param userGroupNamesMap 用户班级名称映射
     */
    @Async
    public void sendAssignmentReleaseNotificationsAsync(AssignmentReleaseDO assignmentRelease, Long courseId, 
                                                        List<MemberUserRespDTO> targetUserList, 
                                                        Map<Long, List<String>> userGroupNamesMap) {
        try {
            // 获取课程信息，用于消息通知
            LessonDO lesson = lessonService.getLesson(courseId);
            
            targetUserList.forEach(user -> {
                try {
                    // 获取用户所在的班级名称列表
                    List<String> groupNames = userGroupNamesMap.get(user.getId());
                    // 拼接班级名称
                    String userGroupNames = String.join("、", groupNames);

                    // 发送作业发布消息通知
                    sendAssignmentReleaseMessage(
                            user.getId(),                     // 用户ID
                            assignmentRelease.getTitle(),     // 作业标题
                            lesson.getTitle(),                // 课程名称
                            userGroupNames,                   // 用户所在班级名称
                            assignmentRelease.getStartTime(), // 作业开始时间
                            assignmentRelease.getEndTime()    // 作业截止时间
                    );
                } catch (Exception e) {
                    log.error("发送作业发布通知失败，用户ID: {}, 作业ID: {}", user.getId(), assignmentRelease.getId(), e);
                }
            });
            
            log.info("异步发送作业发布通知完成，作业ID: {}, 通知用户数: {}", assignmentRelease.getId(), targetUserList.size());
        } catch (Exception e) {
            log.error("异步发送作业发布通知失败，作业ID: {}", assignmentRelease.getId(), e);
        }
    }

    /**
     * 异步保存作业督促提醒信息
     *
     * @param assignmentRelease 作业发布信息
     * @param createReqVO 创建请求VO
     * @param allGroups 所有班级信息
     */
    @Async
    public void saveAssignmentReminderAsync(AssignmentReleaseDO assignmentRelease, 
                                           AssignmentReleaseCreateReqVO createReqVO, 
                                           List<MemberGroupRespDTO> allGroups) {
        try {
            // 计算提醒时间：截止时间减去督促截止时间（小时）
            LocalDateTime reminderTime = assignmentRelease.getEndTime().minusHours(createReqVO.getUrgeDeadline());
            
            // 如果提醒时间已经过了，则不存储
            if (reminderTime.isAfter(LocalDateTime.now())) {
                // 获取课程信息
                LessonDO lesson = lessonService.getLesson(createReqVO.getCourseId());
                
                // 将所有班级信息合并到一个提醒记录中
                List<Map<String, Object>> groupInfoList = new ArrayList<>();
                for (MemberGroupRespDTO group : allGroups) {
                    Map<String, Object> groupInfo = new HashMap<>();
                    groupInfo.put("groupId", group.getId());
                    groupInfo.put("groupName", group.getName());
                    groupInfoList.add(groupInfo);
                }
                
                // 构建提醒信息
                Map<String, Object> reminderInfo = new HashMap<>();
                reminderInfo.put("assignmentReleaseId", assignmentRelease.getId());
                reminderInfo.put("title", assignmentRelease.getTitle());
                reminderInfo.put("courseId", assignmentRelease.getCourseId());
                reminderInfo.put("courseName", lesson.getTitle());
                reminderInfo.put("endTime", assignmentRelease.getEndTime());
                reminderInfo.put("urgeDeadline", createReqVO.getUrgeDeadline());
                reminderInfo.put("notes", "请及时完成作业提交");
                reminderInfo.put("groups", groupInfoList);
                
                // 保存到Redis
                assignmentReminderRedisDAO.saveAssignmentReminderInfo(assignmentRelease.getId(), reminderTime, reminderInfo);
                
                log.info("异步保存作业督促提醒信息完成，作业ID: {}, 提醒时间: {}", assignmentRelease.getId(), reminderTime);
            } else {
                log.info("作业督促提醒时间已过期，不保存提醒信息，作业ID: {}", assignmentRelease.getId());
            }
        } catch (Exception e) {
            log.error("异步保存作业督促提醒信息失败，作业ID: {}", assignmentRelease.getId(), e);
        }
    }
}
