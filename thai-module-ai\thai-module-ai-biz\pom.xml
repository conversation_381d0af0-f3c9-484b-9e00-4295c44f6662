<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.nnnmkj</groupId>
        <artifactId>thai-module-ai</artifactId>
        <version>${revision}</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <artifactId>thai-module-ai-biz</artifactId>

    <name>${project.artifactId}</name>
    <description>
        ai 模块下，接入 LLM 大模型，支持聊天、绘图、音乐、写作、思维导图等功能。
        目前已接入各种模型，不限于：
        国内：通义千问、文心一言、讯飞星火、智谱 GLM、DeepSeek
        国外：OpenAI、Ollama、Midjourney、StableDiffusion、Suno
    </description>

    <dependencies>
        <dependency>
            <groupId>com.nnnmkj</groupId>
            <artifactId>thai-module-ai-api</artifactId>
            <version>${revision}</version>
        </dependency>

        <!-- 基础设施模块 -->
        <dependency>
            <groupId>com.nnnmkj</groupId>
            <artifactId>thai-module-infra-api</artifactId>
            <version>${revision}</version>
        </dependency>

        <!-- 业务组件 -->
        <dependency>
            <groupId>com.nnnmkj</groupId>
            <artifactId>thai-spring-boot-starter-ai</artifactId>
            <version>${revision}</version>
        </dependency>

        <dependency>
            <groupId>com.nnnmkj</groupId>
            <artifactId>thai-spring-boot-starter-biz-tenant</artifactId>
        </dependency>

        <!-- Web 相关 -->
        <dependency>
            <groupId>com.nnnmkj</groupId>
            <artifactId>thai-spring-boot-starter-security</artifactId>
        </dependency>

        <!-- Excel 相关 -->
        <dependency>
            <groupId>com.nnnmkj</groupId>
            <artifactId>thai-spring-boot-starter-excel</artifactId>
        </dependency>

        <!-- DB 相关 -->
        <dependency>
            <groupId>com.nnnmkj</groupId>
            <artifactId>thai-spring-boot-starter-mybatis</artifactId>
        </dependency>

        <!-- Job 相关 -->
        <dependency>
            <groupId>com.nnnmkj</groupId>
            <artifactId>thai-spring-boot-starter-job</artifactId>
        </dependency>

        <!-- Test 测试相关 -->
        <dependency>
            <groupId>com.nnnmkj</groupId>
            <artifactId>thai-spring-boot-starter-test</artifactId>
        </dependency>

        <!-- 语音识别 -->
        <dependency>
            <groupId>com.aliyun</groupId>
            <artifactId>aliyun-java-sdk-core</artifactId>
        </dependency>

        <!-- 语音合成 -->
        <dependency>
            <groupId>com.alibaba.nls</groupId>
            <artifactId>nls-sdk-tts</artifactId>
        </dependency>

        <!-- 引用腾讯云 OCR SDK -->
        <dependency>
            <groupId>com.tencentcloudapi</groupId>
            <artifactId>tencentcloud-sdk-java-ocr</artifactId>
        </dependency>

    </dependencies>
</project>