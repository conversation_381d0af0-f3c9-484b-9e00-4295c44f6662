package com.nnnmkj.thai.module.course.dal.dataobject.lesson;

import lombok.*;

/**
 * 课程 DTO
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
public class LessonDTO extends LessonDO {

    /**
     * 用户昵称
     */
    private String nickname;
    /**
     * 用户头像
     */
    private String avatar;
    /**
     * 是否已收藏
     */
    private Boolean isStore;
    /**
     * 收藏数
     */
    private Long collectionCount;
    /**
     * 学习数
     */
    private Long studyCount;
    /**
     * 点击数
     */
    private Long clickCount;
    /**
     * 章节数量
     */
    private Long chapterCount;

}