package com.nnnmkj.thai.module.ai.util.nls;

import com.alibaba.nls.client.AccessToken;
import com.alibaba.nls.client.protocol.NlsClient;
import com.alibaba.nls.client.protocol.OutputFormatEnum;
import com.alibaba.nls.client.protocol.SampleRateEnum;
import com.alibaba.nls.client.protocol.tts.SpeechSynthesizer;
import com.alibaba.nls.client.protocol.tts.SpeechSynthesizerListener;
import com.alibaba.nls.client.protocol.tts.SpeechSynthesizerResponse;
import com.nnnmkj.thai.module.ai.api.nls.vo.AiNlsTtsConfigVO;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.nio.ByteBuffer;

/**
 * 语音合成工具类
 */
@Component
@Slf4j
public class AliyunNlsUtils {

    @Value("${aliyun.app.key}")
    private String appKey;
    @Value("${aliyun.access.key.id}")
    private String accessKeyId;
    @Value("${aliyun.access.key.secret}")
    private String accessKeySecret;

    private volatile AccessToken accessToken;

    public NlsClient getNlsClient() {
        long currentTimeSeconds = System.currentTimeMillis() / 1000;
        if (accessToken == null || accessToken.getExpireTime() <= currentTimeSeconds) {
            synchronized (this) {
                // 双重检查锁定，重新计算当前时间
                currentTimeSeconds = System.currentTimeMillis() / 1000;
                if (accessToken == null || accessToken.getExpireTime() <= currentTimeSeconds) {
                    accessToken = new AccessToken(accessKeyId, accessKeySecret);
                    try {
                        accessToken.apply();
                        log.debug("成功获取新的AccessToken，过期时间: {}", accessToken.getExpireTime());
                    } catch (IOException e) {
                        log.error("获取 AccessToken 失败", e);
                        throw new RuntimeException("Failed to get access token", e);
                    }
                }
            }
        }
        return new NlsClient(accessToken.getToken());
    }

    /**
     * 文字转语音（返回音频流）
     *
     * @param configVO 配置VO
     * @return 音频字节数组（MP3格式）
     */
    public byte[] processTTS(@Valid AiNlsTtsConfigVO configVO) {
        return this.processTTS(configVO.getText(), configVO.getSpeaker(), configVO.getSpeechRate(), configVO.getPitchRate(), configVO.getDisplayCaptions());
    }

    /**
     * 文字转语音（返回音频流）
     *
     * @param text            文本内容
     * @param speaker         发音人
     * @param speechRate      语速（-500~500）
     * @param pitchRate       语调（-500~500）
     * @param displayCaptions 是否开启字幕
     * @return 音频字节数组（MP3格式）
     */
    public byte[] processTTS(String text, String speaker, Integer speechRate, Integer pitchRate, Boolean displayCaptions) {
        ByteArrayOutputStream audioStream = new ByteArrayOutputStream();
        SpeechSynthesizer synthesizer = null;
        NlsClient client = this.getNlsClient();

        try {
            synthesizer = new SpeechSynthesizer(client, createSynthesizerListener(audioStream));
            synthesizer.setAppKey(appKey);
            synthesizer.setFormat(OutputFormatEnum.MP3);
            synthesizer.setSampleRate(SampleRateEnum.SAMPLE_RATE_16K);
            synthesizer.setVoice(speaker);
            synthesizer.setPitchRate(pitchRate);
            synthesizer.setSpeechRate(speechRate);
            // 如果text文件超过300，自动截取
            if (text.length() > 300) {
                text = text.substring(0, 300);
            }
            // 将text转为SSML标记语言并根据空白字符添加停顿标识
            synthesizer.setText(textToSSML(text));
            if (displayCaptions != null) {
                synthesizer.addCustomedParam("enable_subtitle", displayCaptions);
            }

            synthesizer.start();
            synthesizer.waitForComplete();

            return audioStream.toByteArray();
        } catch (Exception e) {
            log.error("语音合成失败", e);
            throw new RuntimeException("语音合成失败", e);
        } finally {
            client.shutdown();
            if (synthesizer != null) {
                synthesizer.close();
            }
            try {
                audioStream.close();
            } catch (IOException e) {
                log.warn("关闭音频流失败", e);
            }
        }
    }

    /**
     * 文本转为SSML标记语言
     *
     * @param text 文本
     * @return SSML标记语言
     */
    private String textToSSML(String text) {
        StringBuilder ssml = new StringBuilder();
        ssml.append("<speak>");
        // 去除连续的空白字符，统一为单个空格
        text = text.replaceAll("\\s+", " ");
        for (int i = 0; i < text.length(); i++) {
            char c = text.charAt(i);
            if (c == ' ') {
                ssml.append("<break time=\"500ms\"/>");
                // 跳过后续的连续空白字符
                while (i + 1 < text.length() && text.charAt(i + 1) == ' ') {
                    i++;
                }
            } else {
                ssml.append(c);
            }
        }
        ssml.append("</speak>");
        return ssml.toString();
    }

    private SpeechSynthesizerListener createSynthesizerListener(ByteArrayOutputStream outputStream) {
        return new SpeechSynthesizerListener() {
            private boolean firstRecvBinary = true;

            @Override
            public void onComplete(SpeechSynthesizerResponse response) {
                log.info("语音合成完成, status: {}, task_id: {}", response.getStatus(), response.getTaskId());
            }

            @Override
            public void onMessage(ByteBuffer message) {
                try {
                    if (firstRecvBinary) {
                        firstRecvBinary = false;
                        log.debug("收到首包语音数据");
                    }
                    byte[] bytesArray = new byte[message.remaining()];
                    message.get(bytesArray, 0, bytesArray.length);
                    outputStream.write(bytesArray);
                } catch (IOException e) {
                    log.error("写入音频流失败", e);
                    throw new RuntimeException("写入音频流失败", e);
                }
            }

            @Override
            public void onFail(SpeechSynthesizerResponse response) {
                log.error("语音合成失败: task_id={}, status={}, status_text={}",
                        response.getTaskId(),
                        response.getStatus(),
                        response.getStatusText());
                throw new RuntimeException("语音合成失败: " + response.getStatusText());
            }
        };
    }
}