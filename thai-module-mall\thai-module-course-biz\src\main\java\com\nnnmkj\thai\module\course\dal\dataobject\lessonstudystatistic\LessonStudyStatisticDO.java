package com.nnnmkj.thai.module.course.dal.dataobject.lessonstudystatistic;

import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.nnnmkj.thai.framework.mybatis.core.dataobject.BaseDO;
import lombok.*;

/**
 * 课程学习统计 DO
 *
 * <AUTHOR>
 */
@TableName("course_lesson_study_statistic")
@KeySequence("course_lesson_study_statistic_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class LessonStudyStatisticDO extends BaseDO {

    /**
     * 编号
     */
    @TableId
    private Long id;
    /**
     * 课程编号
     */
    private Long courseLessonId;
    /**
     * 学习次数
     */
    private Integer count;

}