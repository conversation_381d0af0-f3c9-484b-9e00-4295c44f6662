package com.nnnmkj.thai.module.course.service.lessoncollection;

import com.nnnmkj.thai.framework.common.pojo.PageResult;
import com.nnnmkj.thai.framework.common.util.object.BeanUtils;
import com.nnnmkj.thai.module.course.controller.admin.lessoncollection.vo.LessonCollectionPageReqVO;
import com.nnnmkj.thai.module.course.controller.admin.lessoncollection.vo.LessonCollectionSaveReqVO;
import com.nnnmkj.thai.module.course.dal.dataobject.lessoncollection.LessonCollectionDO;
import com.nnnmkj.thai.module.course.dal.mysql.lessoncollection.LessonCollectionMapper;
import com.nnnmkj.thai.module.course.dal.mysql.lessoncollectionstatistic.LessonCollectionStatisticMapper;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import static com.nnnmkj.thai.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.nnnmkj.thai.module.course.enums.ErrorCodeConstants.LESSON_COLLECTION_NOT_EXISTS;

/**
 * 课程收藏 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class LessonCollectionServiceImpl implements LessonCollectionService {

    @Resource
    private LessonCollectionMapper lessonCollectionMapper;

    @Resource
    private LessonCollectionStatisticMapper lessonCollectionStatisticMapper;

    @Override
    public Long createLessonCollection(LessonCollectionSaveReqVO createReqVO) {
        // 插入
        LessonCollectionDO lessonCollection = BeanUtils.toBean(createReqVO, LessonCollectionDO.class);
        lessonCollectionMapper.insert(lessonCollection);
        Long collectionId = lessonCollection.getId();
        // 更新收藏统计
        lessonCollectionStatisticMapper.atomicUpdateCount(lessonCollection.getLessonId(), 1);
        // 返回
        return collectionId;
    }

    @Override
    public void updateLessonCollection(LessonCollectionSaveReqVO updateReqVO) {
        // 校验存在
        validateLessonCollectionExists(updateReqVO.getId());
        // 更新
        LessonCollectionDO updateObj = BeanUtils.toBean(updateReqVO, LessonCollectionDO.class);
        lessonCollectionMapper.updateById(updateObj);
    }

    @Override
    public void deleteLessonCollection(Long id) {
        // 校验存在
        LessonCollectionDO lessonCollectionDO = validateLessonCollectionExists(id);
        // 删除
        lessonCollectionMapper.deleteById(id);
        // 更新收藏统计
        lessonCollectionStatisticMapper.atomicUpdateCount(lessonCollectionDO.getLessonId(), -1);
    }

    private LessonCollectionDO validateLessonCollectionExists(Long id) {
        LessonCollectionDO lessonCollectionDO = lessonCollectionMapper.selectById(id);
        if (lessonCollectionMapper.selectById(id) == null) {
            throw exception(LESSON_COLLECTION_NOT_EXISTS);
        }
        return lessonCollectionDO;
    }

    @Override
    public LessonCollectionDO getLessonCollection(Long id) {
        return lessonCollectionMapper.selectById(id);
    }

    @Override
    public PageResult<LessonCollectionDO> getLessonCollectionPage(LessonCollectionPageReqVO pageReqVO) {
        return lessonCollectionMapper.selectPage(pageReqVO);
    }

}