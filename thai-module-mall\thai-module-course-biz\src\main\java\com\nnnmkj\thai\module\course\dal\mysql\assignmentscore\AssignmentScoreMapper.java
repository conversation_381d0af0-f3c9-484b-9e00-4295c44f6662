package com.nnnmkj.thai.module.course.dal.mysql.assignmentscore;

import com.nnnmkj.thai.framework.common.pojo.PageResult;
import com.nnnmkj.thai.framework.mybatis.core.mapper.BaseMapperX;
import com.nnnmkj.thai.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.nnnmkj.thai.module.course.controller.admin.assignmentscore.vo.AssignmentScorePageReqVO;
import com.nnnmkj.thai.module.course.dal.dataobject.assignmentscore.AssignmentScoreDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * 课程作业成绩 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface AssignmentScoreMapper extends BaseMapperX<AssignmentScoreDO> {

    default PageResult<AssignmentScoreDO> selectPage(AssignmentScorePageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<AssignmentScoreDO>()
                .eqIfPresent(AssignmentScoreDO::getCourseId, reqVO.getCourseId())
                .eqIfPresent(AssignmentScoreDO::getAssignmentReleaseId, reqVO.getAssignmentReleaseId())
                .eqIfPresent(AssignmentScoreDO::getAnswerPaperId, reqVO.getAnswerPaperId())
                .eqIfPresent(AssignmentScoreDO::getUserId, reqVO.getUserId())
                .betweenIfPresent(AssignmentScoreDO::getScore, reqVO.getScore())
                .betweenIfPresent(AssignmentScoreDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(AssignmentScoreDO::getId));
    }

}