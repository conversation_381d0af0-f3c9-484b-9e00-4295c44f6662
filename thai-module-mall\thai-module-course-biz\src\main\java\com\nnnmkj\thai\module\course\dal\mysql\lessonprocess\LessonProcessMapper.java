package com.nnnmkj.thai.module.course.dal.mysql.lessonprocess;

import com.nnnmkj.thai.framework.common.pojo.PageResult;
import com.nnnmkj.thai.framework.mybatis.core.mapper.BaseMapperX;
import com.nnnmkj.thai.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.nnnmkj.thai.module.course.controller.admin.lessonprocess.vo.LessonProcessPageReqVO;
import com.nnnmkj.thai.module.course.dal.dataobject.lessonprocess.LessonProcessDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * 课程进度 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface LessonProcessMapper extends BaseMapperX<LessonProcessDO> {

    default PageResult<LessonProcessDO> selectPage(LessonProcessPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<LessonProcessDO>()
                .eqIfPresent(LessonProcessDO::getCourseId, reqVO.getCourseId())
                .eqIfPresent(LessonProcessDO::getUserId, reqVO.getUserId())
                .eqIfPresent(LessonProcessDO::getStatus, reqVO.getStatus())
                .betweenIfPresent(LessonProcessDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(LessonProcessDO::getId));
    }

}