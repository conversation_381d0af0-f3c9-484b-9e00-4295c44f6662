package com.nnnmkj.thai.module.ai.job;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.nnnmkj.thai.framework.tenant.core.aop.TenantIgnore;
import com.nnnmkj.thai.module.infra.api.file.FileApi;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.Set;

/**
 * FastGPT Excel文件清理定时任务
 * 每天凌晨2点执行，清理过期的Excel文件
 *
 * <AUTHOR>
 */
@Component
@Slf4j
public class FastGPTExcelCleanJob {

    @Resource
    private StringRedisTemplate stringRedisTemplate;

    @Resource
    private FileApi fileApi;

    /**
     * Redis中存储文件信息的键前缀
     */
    private static final String REDIS_KEY_PREFIX = "fast_gpt_excel:*";

    /**
     * 每天凌晨2点执行清理任务
     */
    @Scheduled(cron = "0 0 2 * * ?")
    @TenantIgnore
    public void cleanExpiredExcelFiles() {
        log.info("[cleanExpiredExcelFiles][开始执行FastGPT Excel文件清理任务]");

        try {
            // 1. 获取所有FastGPT Excel文件的Redis键
            Set<String> redisKeys = stringRedisTemplate.keys(REDIS_KEY_PREFIX);
            
            if (CollUtil.isEmpty(redisKeys)) {
                log.info("[cleanExpiredExcelFiles][没有找到需要清理的文件]");
                return;
            }

            int cleanedCount = 0;
            int errorCount = 0;

            // 2. 遍历所有键，检查是否过期
            for (String redisKey : redisKeys) {
                try {
                    // 检查键是否还存在（可能已经过期被Redis自动删除）
                    String fileInfo = stringRedisTemplate.opsForValue().get(redisKey);
                    
                    if (StrUtil.isBlank(fileInfo)) {
                        // 键已过期，无需处理
                        continue;
                    }

                    // 解析文件信息：格式为 "fileUrl|filePath"
                    String[] parts = fileInfo.split("\\|");
                    if (parts.length != 2) {
                        log.warn("[cleanExpiredExcelFiles][文件信息格式错误，键：{}，值：{}]", redisKey, fileInfo);
                        continue;
                    }

                    String filePath = parts[1];

                    // 3. 删除OSS文件
                    deleteFileFromOSS(filePath);

                    // 4. 删除Redis记录
                    stringRedisTemplate.delete(redisKey);

                    cleanedCount++;
                    log.debug("[cleanExpiredExcelFiles][成功清理文件，路径：{}]", filePath);

                } catch (Exception e) {
                    errorCount++;
                    log.error("[cleanExpiredExcelFiles][清理文件失败，键：{}]", redisKey, e);
                }
            }

            log.info("[cleanExpiredExcelFiles][FastGPT Excel文件清理任务完成，成功清理：{}个，失败：{}个]", cleanedCount, errorCount);

        } catch (Exception e) {
            log.error("[cleanExpiredExcelFiles][FastGPT Excel文件清理任务执行异常]", e);
        }
    }

    /**
     * 从OSS删除文件
     */
    private void deleteFileFromOSS(String filePath) {
        try {
            fileApi.deleteFile(filePath);
            log.debug("[deleteFileFromOSS][成功删除OSS文件，路径：{}]", filePath);
        } catch (Exception e) {
            log.error("[deleteFileFromOSS][删除OSS文件失败，路径：{}]", filePath, e);
            throw e;
        }
    }

}
