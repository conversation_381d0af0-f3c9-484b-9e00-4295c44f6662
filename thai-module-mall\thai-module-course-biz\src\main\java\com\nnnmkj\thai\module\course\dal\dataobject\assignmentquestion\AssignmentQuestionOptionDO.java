package com.nnnmkj.thai.module.course.dal.dataobject.assignmentquestion;

import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.nnnmkj.thai.framework.mybatis.core.dataobject.BaseDO;
import lombok.*;

/**
 * 课程作业题目选项 DO
 *
 * <AUTHOR>
 */
@TableName("course_assignment_question_option")
@KeySequence("course_assignment_question_option_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AssignmentQuestionOptionDO extends BaseDO {

    /**
     * 编号
     */
    @TableId
    private Long id;
    /**
     * 题目ID
     */
    private Long questionId;
    /**
     * 选项编号
     */
    private String serial;
    /**
     * 选项内容
     */
    private String content;

}