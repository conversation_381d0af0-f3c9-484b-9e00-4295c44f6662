package com.nnnmkj.thai.module.course.controller.admin.lessonchapter;

import cn.hutool.core.collection.CollUtil;
import com.nnnmkj.thai.framework.apilog.core.annotation.ApiAccessLog;
import com.nnnmkj.thai.framework.common.pojo.CommonResult;
import com.nnnmkj.thai.framework.common.pojo.PageParam;
import com.nnnmkj.thai.framework.common.pojo.PageResult;
import com.nnnmkj.thai.framework.excel.core.util.ExcelUtils;
import com.nnnmkj.thai.module.course.controller.admin.lessonchapter.vo.LessonChapterPageReqVO;
import com.nnnmkj.thai.module.course.controller.admin.lessonchapter.vo.LessonChapterRespVO;
import com.nnnmkj.thai.module.course.controller.admin.lessonchapter.vo.LessonChapterSaveReqVO;
import com.nnnmkj.thai.module.course.convert.lessonchapter.LessonChapterConvert;
import com.nnnmkj.thai.module.course.dal.dataobject.lesson.LessonDO;
import com.nnnmkj.thai.module.course.dal.dataobject.lessonchapter.LessonChapterDO;
import com.nnnmkj.thai.module.course.dal.dataobject.lessonchapter.LessonChatperAttachmentDO;
import com.nnnmkj.thai.module.course.service.lesson.LessonService;
import com.nnnmkj.thai.module.course.service.lessonchapter.LessonChapterService;
import com.nnnmkj.thai.module.system.api.permission.PermissionApi;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

import static com.nnnmkj.thai.framework.apilog.core.enums.OperateTypeEnum.EXPORT;
import static com.nnnmkj.thai.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 课程章节")
@RestController
@RequestMapping("/course/lesson-chapter")
@Validated
public class LessonChapterController {

    @Resource
    private LessonChapterService lessonChapterService;

    @Resource
    private LessonService lessonService;
    
    @PostMapping("/create")
    @Operation(summary = "创建课程章节")
    @PreAuthorize("@ss.hasPermission('course:lesson-chapter:create')")
    public CommonResult<Long> createLessonChapter(@Valid @RequestBody LessonChapterSaveReqVO createReqVO) {
        return success(lessonChapterService.createLessonChapter(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新课程章节")
    @PreAuthorize("@ss.hasPermission('course:lesson-chapter:update')")
    public CommonResult<Boolean> updateLessonChapter(@Valid @RequestBody LessonChapterSaveReqVO updateReqVO) {
        lessonChapterService.updateLessonChapter(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除课程章节")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('course:lesson-chapter:delete')")
    public CommonResult<Boolean> deleteLessonChapter(@RequestParam("id") Long id) {
        lessonChapterService.deleteLessonChapter(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得课程章节")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('course:lesson-chapter:query')")
    public CommonResult<LessonChapterRespVO> getLessonChapter(@RequestParam("id") Long id) {
        LessonChapterDO lessonChapter = lessonChapterService.getLessonChapter(id);

        if (lessonChapter == null) {
            return success(null);
        }

        // 处理课程返显
        LessonDO lesson = lessonService.getLesson(lessonChapter.getCourseId());

        // 增加父章节为空的判断
        LessonChapterDO parentChapter = null;
        if (lessonChapter.getParentId() != null) {
            parentChapter = lessonChapterService.getLessonChapter(lessonChapter.getParentId());
        }
        return success(LessonChapterConvert.INSTANCE.convert(lessonChapter, lesson, parentChapter));
    }

    @GetMapping("/page")
    @Operation(summary = "获得课程章节分页")
    @PreAuthorize("@ss.hasPermission('course:lesson-chapter:query')")
    public CommonResult<PageResult<LessonChapterRespVO>> getLessonChapterPage(@Valid LessonChapterPageReqVO pageReqVO) {
        PageResult<LessonChapterDO> pageResult = lessonChapterService.getLessonChapterPage(pageReqVO);

        if (CollUtil.isEmpty(pageResult.getList())) {
            return success(PageResult.empty());
        }
        // 处理课程返显
        Set<Long> lessonChapterIds = pageResult.getList().stream().map(LessonChapterDO::getId).collect(Collectors.toSet());
        Set<Long> courseIds = pageResult.getList().stream().map(LessonChapterDO::getCourseId).collect(Collectors.toSet());
        List<LessonChapterDO> lessonChapterList = lessonChapterService.getLessonChapterList(lessonChapterIds);
        List<LessonDO> lessonList = lessonService.getLessonList(courseIds);
        return success(LessonChapterConvert.INSTANCE.convertPage(pageResult, lessonList, lessonChapterList));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出课程章节 Excel")
    @PreAuthorize("@ss.hasPermission('course:lesson-chapter:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportLessonChapterExcel(@Valid LessonChapterPageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<LessonChapterDO> list = lessonChapterService.getLessonChapterPage(pageReqVO).getList();
        // 处理课程返显
        Set<Long> courseIds = list.stream().map(LessonChapterDO::getCourseId).collect(Collectors.toSet());
        List<LessonDO> courseList = lessonService.getLessonList(courseIds);
        Set<Long> courseChapterIds = list.stream().map(LessonChapterDO::getId).collect(Collectors.toSet());
        List<LessonChapterDO> courseChapterList = lessonChapterService.getLessonChapterList(courseChapterIds);
        // 导出 Excel
        ExcelUtils.write(response, "课程章节.xls", "数据", LessonChapterRespVO.class,
                LessonChapterConvert.INSTANCE.convertList(list, courseList, courseChapterList));
    }

    // ==================== 子表（课程章节附件） ====================

    @GetMapping("/lesson-chatper-attachment/list-by-chapter-id")
    @Operation(summary = "获得课程章节附件列表")
    @Parameter(name = "chapterId", description = "章节ID")
    @PreAuthorize("@ss.hasPermission('course:lesson-chapter:query')")
    public CommonResult<List<LessonChatperAttachmentDO>> getLessonChatperAttachmentListByChapterId(@RequestParam("chapterId") Long chapterId) {
        // 获取章节信息
        LessonChapterDO chapter = lessonChapterService.getLessonChapter(chapterId);
        if (chapter == null) {
            return success(Collections.emptyList());
        }
        return success(lessonChapterService.getLessonChatperAttachmentListByChapterId(chapterId));
    }

    @GetMapping("/parent-page")
    @Operation(summary = "获取顶级章节分页")
    @PreAuthorize("@ss.hasPermission('course:lesson-chapter:query')")
    public CommonResult<PageResult<LessonChapterRespVO>> getParentLessonChapterPage(@Valid LessonChapterPageReqVO pageReqVO) {
        // 设置只查询父章节为null或为0的数据，这些都是顶级章节
        pageReqVO.setParentId(0L);
        PageResult<LessonChapterDO> pageResult = lessonChapterService.getTopLessonChapterPage(pageReqVO);

        if (CollUtil.isEmpty(pageResult.getList())) {
            return success(PageResult.empty());
        }
        // 处理课程返显
        Set<Long> lessonChapterIds = pageResult.getList().stream().map(LessonChapterDO::getId).collect(Collectors.toSet());
        Set<Long> courseIds = pageResult.getList().stream().map(LessonChapterDO::getCourseId).collect(Collectors.toSet());
        List<LessonChapterDO> lessonChapterList = lessonChapterService.getLessonChapterList(lessonChapterIds);
        List<LessonDO> lessonList = lessonService.getLessonList(courseIds);
        return success(LessonChapterConvert.INSTANCE.convertPage(pageResult, lessonList, lessonChapterList));
    }

    @GetMapping("/list-by-parent-id")
    @Operation(summary = "获取子章节列表")
    @Parameter(name = "parentId", description = "父章节ID", required = true)
    @PreAuthorize("@ss.hasPermission('course:lesson-chapter:query')")
    public CommonResult<List<LessonChapterRespVO>> getChildLessonChapterListByParentId(@RequestParam("parentId") Long parentId) {
        // 获取父章节信息
        LessonChapterDO parentChapter = lessonChapterService.getLessonChapter(parentId);
        if (parentChapter == null) {
            return success(Collections.emptyList());
        }

        List<LessonChapterDO> childList = lessonChapterService.getLessonChapterListByParentId(parentId);
        
        if (CollUtil.isEmpty(childList)) {
            return success(Collections.emptyList());
        }
        
        // 处理课程返显
        Set<Long> courseIds = childList.stream().map(LessonChapterDO::getCourseId).collect(Collectors.toSet());
        List<LessonDO> lessonList = lessonService.getLessonList(courseIds);
        
        return success(LessonChapterConvert.INSTANCE.convertList(childList, lessonList, 
                Collections.singletonList(parentChapter)));
    }

    @GetMapping("/list-by-course-id")
    @Operation(summary = "根据课程ID获取章节列表")
    @Parameter(name = "courseId", description = "课程ID", required = true)
    @PreAuthorize("@ss.hasPermission('course:lesson-chapter:query')")
    public CommonResult<List<LessonChapterRespVO>> getLessonChapterListByCourseId(@RequestParam("courseId") Long courseId) {
        // 获取课程信息
        LessonDO lesson = lessonService.getLesson(courseId);

        // 获取课程下所有章节
        List<LessonChapterDO> chapterList = lessonChapterService.getLessonChapterListByCourseId(courseId);
        if (CollUtil.isEmpty(chapterList)) {
            return success(Collections.emptyList());
        }
        
        // 获取所有章节ID，用于查询父章节信息
        Set<Long> chapterIds = chapterList.stream().map(LessonChapterDO::getId).collect(Collectors.toSet());
        List<LessonChapterDO> allChapters = lessonChapterService.getLessonChapterList(chapterIds);
        
        // 转换并返回数据
        Map<Long, LessonDO> lessonMap = Collections.singletonMap(lesson.getId(), lesson);
        Map<Long, LessonChapterDO> chapterMap = allChapters.stream()
                .collect(Collectors.toMap(LessonChapterDO::getId, chapter -> chapter, (old, now) -> now));
        
        // 转换为响应VO
        List<LessonChapterRespVO> result = new ArrayList<>();
        for (LessonChapterDO chapter : chapterList) {
            LessonDO chapterLesson = lessonMap.get(chapter.getCourseId());
            LessonChapterDO parentChapter = chapter.getParentId() != null ? chapterMap.get(chapter.getParentId()) : null;
            result.add(LessonChapterConvert.INSTANCE.convert(chapter, chapterLesson, parentChapter));
        }
        
        return success(result);
    }
}