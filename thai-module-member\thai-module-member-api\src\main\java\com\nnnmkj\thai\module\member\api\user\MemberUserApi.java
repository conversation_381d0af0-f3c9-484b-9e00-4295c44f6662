package com.nnnmkj.thai.module.member.api.user;

import com.nnnmkj.thai.module.member.api.user.dto.MemberUserRespDTO;

import java.util.Collection;
import java.util.List;
import java.util.Map;

import static com.nnnmkj.thai.framework.common.util.collection.CollectionUtils.convertMap;

/**
 * 会员用户的 API 接口
 *
 * <AUTHOR>
 */
public interface MemberUserApi {

    /**
     * 获得会员用户信息
     *
     * @param id 用户编号
     * @return 用户信息
     */
    MemberUserRespDTO getUser(Long id);

    /**
     * 基于手机号，精准匹配用户
     *
     * @param mobile 手机号
     * @return 用户信息
     */
    MemberUserRespDTO getUserByMobile(String mobile);

    /**
     * 获得用户信息列表
     *
     * @param ids 用户编号集合
     * @return 用户信息列表
     */
    List<MemberUserRespDTO> getUserList(Collection<Long> ids);

    /**
     * 获得用户信息列表
     *
     * @param groupId 分组编号
     * @return 用户列表
     */
    List<MemberUserRespDTO> getUserListByGroupId(Long groupId);

    /**
     * 获得用户信息列表
     *
     * @param groupIds 分组编号集合
     * @return 用户列表
     */
    List<MemberUserRespDTO> getUserListByGroupIds(Collection<Long> groupIds);

    /**
     * 基于用户昵称，模糊匹配用户列表
     *
     * @param nickname 用户昵称，模糊匹配
     * @return 用户信息的列表
     */
    List<MemberUserRespDTO> getUserListByNickname(String nickname);

    /**
     * 校验用户是否存在
     *
     * @param id 用户编号
     */
    void validateUser(Long id);

    /**
     * 获得会员用户 Map
     *
     * @param ids 用户编号的数组
     * @return 会员用户 Map
     */
    default Map<Long, MemberUserRespDTO> getUserMap(Collection<Long> ids) {
        List<MemberUserRespDTO> list = getUserList(ids);
        return convertMap(list, MemberUserRespDTO::getId);
    }

    /**
     * 获得当前登录会员用户信息
     *
     * @return 用户信息
     */
    MemberUserRespDTO getLoginUser();

    /**
     * 获得当前登录会员用户编号
     *
     * @return 用户编号
     */
    Long getLoginUserId();

    /**
     * 获得当前登录会员用户对应的后台用户编号
     *
     * @return 后台用户编号
     */
    Long getLoginUserSystemId();

    /**
     * 根据系统用户编号获得会员用户编号
     *
     * @param systemUserId 系统用户编号
     * @return 会员用户编号
     */
    Long getUserIdBySystemUserId(Long systemUserId);

    /**
     * 根据系统用户编号集合获得会员用户列表
     *
     * @param systemUserIds 系统用户编号列表
     * @return 会员用户列表
     */
    List<MemberUserRespDTO> getUserListBySystemUserIds(Collection<Long> systemUserIds);

    /**
     * 根据用户编号获取分组编号列表
     *
     * @param id 用户编号
     * @return 分组编号列表
     */
    List<Long> getGroupIdsById(Long id);

}
