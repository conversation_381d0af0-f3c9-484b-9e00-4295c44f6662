package com.nnnmkj.thai.module.ai.api.nls;

import com.nnnmkj.thai.module.ai.api.nls.vo.AiNlsTtsConfigVO;
import com.nnnmkj.thai.module.ai.util.nls.AliyunNlsUtils;
import jakarta.annotation.Resource;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

/**
 * AI NLS API 实现类
 */
@Service("aliyunNlsTtsApi")
@Primary
@Validated
public class AliyunNlsTtsApiImpl implements AiNlsApi {

    @Resource
    private AliyunNlsUtils aliyunNlsUtils;

    public byte[] tts(AiNlsTtsConfigVO configVO) {
        return aliyunNlsUtils.processTTS(configVO);
    }

}
