package com.nnnmkj.thai.module.course.service.lessonclickstatistic;

import com.nnnmkj.thai.framework.common.pojo.PageResult;
import com.nnnmkj.thai.framework.common.util.object.BeanUtils;
import com.nnnmkj.thai.module.course.controller.admin.lessonclickstatistic.vo.LessonClickStatisticPageReqVO;
import com.nnnmkj.thai.module.course.controller.admin.lessonclickstatistic.vo.LessonClickStatisticSaveReqVO;
import com.nnnmkj.thai.module.course.dal.dataobject.lessonclickstatistic.LessonClickStatisticDO;
import com.nnnmkj.thai.module.course.dal.mysql.lessonclickstatistic.LessonClickStatisticMapper;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import static com.nnnmkj.thai.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.nnnmkj.thai.module.course.enums.ErrorCodeConstants.LESSON_CLICK_STATISTIC_NOT_EXISTS;

/**
 * 课程点击量统计 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class LessonClickStatisticServiceImpl implements LessonClickStatisticService {

    @Resource
    private LessonClickStatisticMapper lessonClickStatisticMapper;

    @Override
    public Long createLessonClickStatistic(LessonClickStatisticSaveReqVO createReqVO) {
        // 插入
        LessonClickStatisticDO lessonClickStatistic = BeanUtils.toBean(createReqVO, LessonClickStatisticDO.class);
        lessonClickStatisticMapper.insert(lessonClickStatistic);
        // 返回
        return lessonClickStatistic.getId();
    }

    @Override
    public void updateLessonClickStatistic(LessonClickStatisticSaveReqVO updateReqVO) {
        // 校验存在
        validateLessonClickStatisticExists(updateReqVO.getId());
        // 更新
        LessonClickStatisticDO updateObj = BeanUtils.toBean(updateReqVO, LessonClickStatisticDO.class);
        lessonClickStatisticMapper.updateById(updateObj);
    }

    @Override
    public void deleteLessonClickStatistic(Long id) {
        // 校验存在
        validateLessonClickStatisticExists(id);
        // 删除
        lessonClickStatisticMapper.deleteById(id);
    }

    private void validateLessonClickStatisticExists(Long id) {
        if (lessonClickStatisticMapper.selectById(id) == null) {
            throw exception(LESSON_CLICK_STATISTIC_NOT_EXISTS);
        }
    }

    @Override
    public LessonClickStatisticDO getLessonClickStatistic(Long id) {
        return lessonClickStatisticMapper.selectById(id);
    }

    @Override
    public PageResult<LessonClickStatisticDO> getLessonClickStatisticPage(LessonClickStatisticPageReqVO pageReqVO) {
        return lessonClickStatisticMapper.selectPage(pageReqVO);
    }

}