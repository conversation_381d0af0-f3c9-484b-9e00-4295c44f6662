package com.nnnmkj.thai.module.course.dal.mysql.assignmentanswerpaper;

import com.nnnmkj.thai.framework.common.pojo.PageResult;
import com.nnnmkj.thai.framework.mybatis.core.mapper.BaseMapperX;
import com.nnnmkj.thai.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.nnnmkj.thai.module.course.controller.admin.assignmentanswerpaper.vo.AssignmentAnswerPaperPageReqVO;
import com.nnnmkj.thai.module.course.dal.dataobject.assignmentanswerpaper.AssignmentAnswerPaperDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * 课程作业作答试卷 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface AssignmentAnswerPaperMapper extends BaseMapperX<AssignmentAnswerPaperDO> {

    default PageResult<AssignmentAnswerPaperDO> selectPage(AssignmentAnswerPaperPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<AssignmentAnswerPaperDO>()
                .eqIfPresent(AssignmentAnswerPaperDO::getCourseId, reqVO.getCourseId())
                .eqIfPresent(AssignmentAnswerPaperDO::getAssignmentReleaseId, reqVO.getAssignmentReleaseId())
                .eqIfPresent(AssignmentAnswerPaperDO::getUserId, reqVO.getUserId())
                .betweenIfPresent(AssignmentAnswerPaperDO::getAnswerTime, reqVO.getAnswerTime())
                .eqIfPresent(AssignmentAnswerPaperDO::getIsSubmit, reqVO.getIsSubmit())
                .betweenIfPresent(AssignmentAnswerPaperDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(AssignmentAnswerPaperDO::getId));
    }

}