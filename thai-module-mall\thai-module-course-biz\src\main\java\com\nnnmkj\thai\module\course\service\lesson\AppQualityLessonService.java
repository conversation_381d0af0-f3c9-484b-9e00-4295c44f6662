package com.nnnmkj.thai.module.course.service.lesson;

import com.nnnmkj.thai.framework.common.pojo.PageResult;
import com.nnnmkj.thai.module.course.controller.app.lesson.vo.AppLessonPageReqVO;
import com.nnnmkj.thai.module.course.controller.app.lesson.vo.AppLessonRespVO;

/**
 * 优质课程 Service 接口
 *
 * <AUTHOR>
 */
public interface AppQualityLessonService {


    PageResult<AppLessonRespVO> getQualityLessons(AppLessonPageReqVO pageReqVO);

    /**
     * 增加课程点击量
     *
     * @param lessonId 课程ID
     * @param userId 用户ID
     */
    void incrementViewCount(Long lessonId, Long userId);
}
