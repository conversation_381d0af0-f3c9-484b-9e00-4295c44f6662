package com.nnnmkj.thai.module.course.dal.mysql.lessonchapter;

import com.nnnmkj.thai.framework.mybatis.core.mapper.BaseMapperX;
import com.nnnmkj.thai.module.course.dal.dataobject.lessonchapter.LessonChatperAttachmentDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 课程章节附件 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface LessonChatperAttachmentMapper extends BaseMapperX<LessonChatperAttachmentDO> {

    default List<LessonChatperAttachmentDO> selectListByChapterId(Long chapterId) {
        return selectList(LessonChatperAttachmentDO::getChapterId, chapterId);
    }

    default void deleteByChapterId(Long chapterId) {
        delete(LessonChatperAttachmentDO::getChapterId, chapterId);
    }

    /**
     * 直接使用SQL查询章节附件
     * @param chapterId 章节ID
     * @return 章节附件列表
     */
    @Select("SELECT * FROM course_lesson_chatper_attachment WHERE chapter_id = #{chapterId} AND deleted = 0")
    List<LessonChatperAttachmentDO> selectAttachmentsByChapterId(Long chapterId);
}
