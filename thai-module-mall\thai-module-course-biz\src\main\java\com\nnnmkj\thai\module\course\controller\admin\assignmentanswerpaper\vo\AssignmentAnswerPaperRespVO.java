package com.nnnmkj.thai.module.course.controller.admin.assignmentanswerpaper.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Schema(description = "管理后台 - 课程作业作答试卷 Response VO")
@Data
@ExcelIgnoreUnannotated
public class AssignmentAnswerPaperRespVO {

    @Schema(description = "编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "10291")
    @ExcelProperty("编号")
    private Long id;

    @Schema(description = "课程ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "27224")
    @ExcelProperty("课程ID")
    private Long courseId;

    @Schema(description = "作业发布ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "20079")
    @ExcelProperty("作业发布ID")
    private Long assignmentReleaseId;

    @Schema(description = "用户ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "16442")
    @ExcelProperty("用户ID")
    private Long userId;

    @Schema(description = "答题时间（s）", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("答题时间（s）")
    private Long answerTime;

    @Schema(description = "是否提交", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("是否提交")
    private Boolean isSubmit;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}