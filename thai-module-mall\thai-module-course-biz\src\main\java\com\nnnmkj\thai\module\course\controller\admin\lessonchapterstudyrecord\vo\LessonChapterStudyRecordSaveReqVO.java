package com.nnnmkj.thai.module.course.controller.admin.lessonchapterstudyrecord.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.math.BigDecimal;

@Schema(description = "管理后台 - 课程章节学习记录新增/修改 Request VO")
@Data
public class LessonChapterStudyRecordSaveReqVO {

    @Schema(description = "编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "24851")
    private Long id;

    @Schema(description = "课程ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "23385")
    @NotNull(message = "课程ID不能为空")
    private Long courseId;

    @Schema(description = "章节ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "3322")
    @NotNull(message = "章节ID不能为空")
    private Long chapterId;

    @Schema(description = "用户ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "5489")
    private Long userId;

    @Schema(description = "附件ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "27388")
    @NotNull(message = "附件ID不能为空")
    private Long attachmentId;

    @Schema(description = "总时间(s)")
    private Integer allTime;

    @Schema(description = "当前时间(s)")
    private Integer currentTimeSec;

    @Schema(description = "学习进度百分比", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "学习进度百分比不能为空")
    private BigDecimal progressPercentage;

    @Schema(description = "状态", example = "1")
    private Integer status;

}