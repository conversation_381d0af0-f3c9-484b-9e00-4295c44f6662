package com.nnnmkj.thai.module.course.service.membergroupassignmentrelease;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.nnnmkj.thai.framework.common.pojo.PageResult;
import com.nnnmkj.thai.framework.common.util.object.BeanUtils;
import com.nnnmkj.thai.module.course.controller.admin.membergroupassignmentrelease.vo.MemberGroupAssignmentReleasePageReqVO;
import com.nnnmkj.thai.module.course.controller.admin.membergroupassignmentrelease.vo.MemberGroupAssignmentReleaseSaveReqVO;
import com.nnnmkj.thai.module.course.dal.dataobject.membergroupassignmentrelease.MemberGroupAssignmentReleaseDO;
import com.nnnmkj.thai.module.course.dal.mysql.membergroupassignmentrelease.MemberGroupAssignmentReleaseMapper;
import com.nnnmkj.thai.module.member.api.user.MemberUserApi;
import com.nnnmkj.thai.module.member.api.user.dto.MemberUserRespDTO;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import java.util.Collections;
import java.util.List;

import static com.nnnmkj.thai.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.nnnmkj.thai.module.course.enums.ErrorCodeConstants.MEMBER_GROUP_ASSIGNMENT_RELEASE_NOT_EXISTS;

/**
 * 课程作业发布班级关联 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class MemberGroupAssignmentReleaseServiceImpl implements MemberGroupAssignmentReleaseService {

    @Resource
    private MemberGroupAssignmentReleaseMapper memberGroupAssignmentReleaseMapper;

    @Resource
    private MemberUserApi memberUserApi;

    @Override
    public Long createMemberGroupAssignmentRelease(MemberGroupAssignmentReleaseSaveReqVO createReqVO) {
        // 插入
        MemberGroupAssignmentReleaseDO memberGroupAssignmentRelease = BeanUtils.toBean(createReqVO, MemberGroupAssignmentReleaseDO.class);
        memberGroupAssignmentReleaseMapper.insert(memberGroupAssignmentRelease);
        // 返回
        return memberGroupAssignmentRelease.getId();
    }

    @Override
    public void updateMemberGroupAssignmentRelease(MemberGroupAssignmentReleaseSaveReqVO updateReqVO) {
        // 校验存在
        validateMemberGroupAssignmentReleaseExists(updateReqVO.getId());
        // 更新
        MemberGroupAssignmentReleaseDO updateObj = BeanUtils.toBean(updateReqVO, MemberGroupAssignmentReleaseDO.class);
        memberGroupAssignmentReleaseMapper.updateById(updateObj);
    }

    @Override
    public void deleteMemberGroupAssignmentRelease(Long id) {
        // 校验存在
        validateMemberGroupAssignmentReleaseExists(id);
        // 删除
        memberGroupAssignmentReleaseMapper.deleteById(id);
    }

    @Override
    public void deleteMemberGroupAssignmentReleaseByAssignmentReleaseId(Long id) {
        // 删除
        memberGroupAssignmentReleaseMapper.delete(MemberGroupAssignmentReleaseDO::getAssignmentReleaseId, id);
    }

    private void validateMemberGroupAssignmentReleaseExists(Long id) {
        if (memberGroupAssignmentReleaseMapper.selectById(id) == null) {
            throw exception(MEMBER_GROUP_ASSIGNMENT_RELEASE_NOT_EXISTS);
        }
    }

    @Override
    public MemberGroupAssignmentReleaseDO getMemberGroupAssignmentRelease(Long id) {
        return memberGroupAssignmentReleaseMapper.selectByAssignmentReleaseId(id);
    }

    @Override
    public MemberGroupAssignmentReleaseDO getMemberGroupAssignmentRelease(Long groupId, Long assignmentReleaseId) {
        return memberGroupAssignmentReleaseMapper.selectOne(new LambdaQueryWrapper<MemberGroupAssignmentReleaseDO>()
                .eq(MemberGroupAssignmentReleaseDO::getAssignmentReleaseId, assignmentReleaseId)
                .apply("FIND_IN_SET(" + groupId + ", group_ids)"));
    }

    @Override
    public MemberGroupAssignmentReleaseDO getMemberGroupAssignmentReleaseByAssignmentReleaseId(Long id) {
        return memberGroupAssignmentReleaseMapper.selectByAssignmentReleaseId(id);
    }

    @Override
    public PageResult<MemberGroupAssignmentReleaseDO> getMemberGroupAssignmentReleasePage(MemberGroupAssignmentReleasePageReqVO pageReqVO) {
        return memberGroupAssignmentReleaseMapper.selectPage(pageReqVO);
    }

    @Override
    public List<MemberUserRespDTO> getPublishedUsersByReleaseAndGroup(Long userId, Long groupId, Long assignmentReleaseId) {
        MemberGroupAssignmentReleaseDO release = getMemberGroupAssignmentRelease(groupId, assignmentReleaseId);
        if (release == null) {
            return Collections.emptyList();
        }
        List<Long> userIds = release.getUserIds();
        if (CollUtil.isEmpty(userIds)) {
            // 发放对象为班级：获取班级内所有用户
            return memberUserApi.getUserListByGroupId(groupId);
        }
        // 发放对象为指定个人：获取对应用户列表
        return memberUserApi.getUserList(userIds);
    }

}