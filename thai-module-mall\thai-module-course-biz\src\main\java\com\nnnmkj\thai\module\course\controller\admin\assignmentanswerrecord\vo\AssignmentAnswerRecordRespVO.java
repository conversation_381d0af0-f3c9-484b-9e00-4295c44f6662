package com.nnnmkj.thai.module.course.controller.admin.assignmentanswerrecord.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.nnnmkj.thai.framework.excel.core.annotations.DictFormat;
import com.nnnmkj.thai.framework.excel.core.convert.DictConvert;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Schema(description = "管理后台 - 课程作业作答记录 Response VO")
@Data
@ExcelIgnoreUnannotated
public class AssignmentAnswerRecordRespVO {

    @Schema(description = "编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "20456")
    @ExcelProperty("编号")
    private Long id;

    @Schema(description = "课程ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "5434")
    @ExcelProperty("课程ID")
    private Long courseId;

    @Schema(description = "作业发布ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "12696")
    @ExcelProperty("作业发布ID")
    private Long assignmentReleaseId;

    @Schema(description = "作答试卷ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "615")
    @ExcelProperty("作答试卷ID")
    private Long answerPaperId;

    @Schema(description = "题目ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "379")
    @ExcelProperty("题目ID")
    private Long questionId;

    @Schema(description = "用户ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "31079")
    @ExcelProperty("用户ID")
    private Long userId;

    @Schema(description = "学生答案", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("学生答案")
    private String answer;

    @Schema(description = "是否正确", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty(value = "是否正确", converter = DictConvert.class)
    @DictFormat("is_correct_type") // TODO 代码优化：建议设置到对应的 DictTypeConstants 枚举类中
    private Boolean isCorrect;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}