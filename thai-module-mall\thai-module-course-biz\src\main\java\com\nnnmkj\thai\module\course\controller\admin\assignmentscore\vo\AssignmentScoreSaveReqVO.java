package com.nnnmkj.thai.module.course.controller.admin.assignmentscore.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

@Schema(description = "管理后台 - 课程作业成绩新增/修改 Request VO")
@Data
public class AssignmentScoreSaveReqVO {

    @Schema(description = "编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "16917")
    private Long id;

    @Schema(description = "课程ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "27291")
    @NotNull(message = "课程ID不能为空")
    private Long courseId;

    @Schema(description = "作业发布ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "11920")
    @NotNull(message = "作业发布ID不能为空")
    private Long assignmentReleaseId;

    @Schema(description = "作答试卷ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "13319")
    @NotNull(message = "作答试卷ID不能为空")
    private Long answerPaperId;

    @Schema(description = "用户ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "3160")
    @NotNull(message = "用户ID不能为空")
    private Long userId;

    @Schema(description = "总题目数量", example = "27895")
    private Integer allCount;

    @Schema(description = "正确题目数量", example = "31859")
    private Integer correctCount;

    @Schema(description = "错误题目数量", example = "5480")
    private Integer errorCount;

    @Schema(description = "成绩", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "成绩不能为空")
    private Double score;

    @Schema(description = "答题时间（s）")
    private Integer timeTaken;

}