package com.nnnmkj.thai.module.course.controller.admin.assignmentanswerrecord.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

@Schema(description = "管理后台 - 课程作业作答记录新增/修改 Request VO")
@Data
public class AssignmentAnswerRecordSaveReqVO {

    @Schema(description = "编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "20456")
    private Long id;

    @Schema(description = "课程ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "5434")
    @NotNull(message = "课程ID不能为空")
    private Long courseId;

    @Schema(description = "作业发布ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "12696")
    @NotNull(message = "作业发布ID不能为空")
    private Long assignmentReleaseId;

    @Schema(description = "作答试卷ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "615")
    @NotNull(message = "作答试卷ID不能为空")
    private Long answerPaperId;

    @Schema(description = "题目ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "379")
    @NotNull(message = "题目ID不能为空")
    private Long questionId;

    @Schema(description = "用户ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "31079")
    @NotNull(message = "用户ID不能为空")
    private Long userId;

    @Schema(description = "学生答案", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "学生答案不能为空")
    private String answer;

    @Schema(description = "是否正确", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "是否正确不能为空")
    private Boolean isCorrect;

}