package com.nnnmkj.thai.module.course.dal.mysql.lessonclickstatistic;

import com.nnnmkj.thai.framework.common.pojo.PageResult;
import com.nnnmkj.thai.framework.mybatis.core.mapper.BaseMapperX;
import com.nnnmkj.thai.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.nnnmkj.thai.module.course.controller.admin.lessonclickstatistic.vo.LessonClickStatisticPageReqVO;
import com.nnnmkj.thai.module.course.dal.dataobject.lessonclickstatistic.LessonClickStatisticDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * 课程点击量统计 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface LessonClickStatisticMapper extends BaseMapperX<LessonClickStatisticDO> {

    default PageResult<LessonClickStatisticDO> selectPage(LessonClickStatisticPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<LessonClickStatisticDO>()
                .eqIfPresent(LessonClickStatisticDO::getCourseLessonId, reqVO.getCourseLessonId())
                .eqIfPresent(LessonClickStatisticDO::getCount, reqVO.getCount())
                .betweenIfPresent(LessonClickStatisticDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(LessonClickStatisticDO::getId));
    }

}