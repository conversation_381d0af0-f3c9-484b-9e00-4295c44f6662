package com.nnnmkj.thai.module.course.dal.dataobject.membergrouplesson;

import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.nnnmkj.thai.framework.mybatis.core.dataobject.BaseDO;
import lombok.*;

/**
 * 班级-课程关联 DO
 *
 * <AUTHOR>
 */
@TableName("course_member_group_lesson")
@KeySequence("course_member_group_lesson_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MemberGroupLessonDO extends BaseDO {

    /**
     * 编号
     */
    @TableId
    private Long id;
    /**
     * 分组编号
     */
    private Long groupId;
    /**
     * 课程编号
     */
    private Long courseId;

}