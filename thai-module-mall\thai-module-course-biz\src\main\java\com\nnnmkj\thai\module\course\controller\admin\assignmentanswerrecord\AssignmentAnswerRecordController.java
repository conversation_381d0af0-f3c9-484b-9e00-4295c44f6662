package com.nnnmkj.thai.module.course.controller.admin.assignmentanswerrecord;

import com.nnnmkj.thai.framework.apilog.core.annotation.ApiAccessLog;
import com.nnnmkj.thai.framework.common.pojo.CommonResult;
import com.nnnmkj.thai.framework.common.pojo.PageParam;
import com.nnnmkj.thai.framework.common.pojo.PageResult;
import com.nnnmkj.thai.framework.common.util.object.BeanUtils;
import com.nnnmkj.thai.framework.excel.core.util.ExcelUtils;
import com.nnnmkj.thai.module.course.controller.admin.assignmentanswerrecord.vo.AssignmentAnswerRecordPageReqVO;
import com.nnnmkj.thai.module.course.controller.admin.assignmentanswerrecord.vo.AssignmentAnswerRecordRespVO;
import com.nnnmkj.thai.module.course.controller.admin.assignmentanswerrecord.vo.AssignmentAnswerRecordSaveReqVO;
import com.nnnmkj.thai.module.course.dal.dataobject.assignmentanswerrecord.AssignmentAnswerRecordDO;
import com.nnnmkj.thai.module.course.service.assignmentanswerrecord.AssignmentAnswerRecordService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.util.List;

import static com.nnnmkj.thai.framework.apilog.core.enums.OperateTypeEnum.EXPORT;
import static com.nnnmkj.thai.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 课程作业作答记录")
@RestController
@RequestMapping("/course/assignment-answer-record")
@Validated
public class AssignmentAnswerRecordController {

    @Resource
    private AssignmentAnswerRecordService assignmentAnswerRecordService;

    @PostMapping("/create")
    @Operation(summary = "创建课程作业作答记录")
    @PreAuthorize("@ss.hasPermission('course:assignment-answer-record:create')")
    public CommonResult<Long> createAssignmentAnswerRecord(@Valid @RequestBody AssignmentAnswerRecordSaveReqVO createReqVO) {
        return success(assignmentAnswerRecordService.createAssignmentAnswerRecord(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新课程作业作答记录")
    @PreAuthorize("@ss.hasPermission('course:assignment-answer-record:update')")
    public CommonResult<Boolean> updateAssignmentAnswerRecord(@Valid @RequestBody AssignmentAnswerRecordSaveReqVO updateReqVO) {
        assignmentAnswerRecordService.updateAssignmentAnswerRecord(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除课程作业作答记录")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('course:assignment-answer-record:delete')")
    public CommonResult<Boolean> deleteAssignmentAnswerRecord(@RequestParam("id") Long id) {
        assignmentAnswerRecordService.deleteAssignmentAnswerRecord(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得课程作业作答记录")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('course:assignment-answer-record:query')")
    public CommonResult<AssignmentAnswerRecordRespVO> getAssignmentAnswerRecord(@RequestParam("id") Long id) {
        AssignmentAnswerRecordDO assignmentAnswerRecord = assignmentAnswerRecordService.getAssignmentAnswerRecord(id);
        return success(BeanUtils.toBean(assignmentAnswerRecord, AssignmentAnswerRecordRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得课程作业作答记录分页")
    @PreAuthorize("@ss.hasPermission('course:assignment-answer-record:query')")
    public CommonResult<PageResult<AssignmentAnswerRecordRespVO>> getAssignmentAnswerRecordPage(@Valid AssignmentAnswerRecordPageReqVO pageReqVO) {
        PageResult<AssignmentAnswerRecordDO> pageResult = assignmentAnswerRecordService.getAssignmentAnswerRecordPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, AssignmentAnswerRecordRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出课程作业作答记录 Excel")
    @PreAuthorize("@ss.hasPermission('course:assignment-answer-record:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportAssignmentAnswerRecordExcel(@Valid AssignmentAnswerRecordPageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<AssignmentAnswerRecordDO> list = assignmentAnswerRecordService.getAssignmentAnswerRecordPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "课程作业作答记录.xls", "数据", AssignmentAnswerRecordRespVO.class,
                        BeanUtils.toBean(list, AssignmentAnswerRecordRespVO.class));
    }

}