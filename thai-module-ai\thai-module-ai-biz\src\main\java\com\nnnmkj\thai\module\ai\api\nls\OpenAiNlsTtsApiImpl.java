package com.nnnmkj.thai.module.ai.api.nls;

import com.nnnmkj.thai.module.ai.api.nls.vo.AiNlsTtsConfigVO;
import com.nnnmkj.thai.module.ai.util.nls.OpenAiNlsUtils;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

/**
 * OpenAI TTS API 实现类
 */
@Service("openAiNlsTtsApi")
@Validated
public class OpenAiNlsTtsApiImpl implements AiNlsApi {

    @Resource
    private OpenAiNlsUtils openAiNlsUtils;

    @Override
    public byte[] tts(AiNlsTtsConfigVO configVO) {
        return openAiNlsUtils.processTTS(configVO);
    }

}