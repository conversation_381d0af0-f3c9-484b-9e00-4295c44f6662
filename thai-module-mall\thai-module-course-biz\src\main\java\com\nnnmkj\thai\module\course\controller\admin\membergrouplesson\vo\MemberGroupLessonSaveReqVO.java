package com.nnnmkj.thai.module.course.controller.admin.membergrouplesson.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

@Schema(description = "管理后台 - 班级-课程关联新增/修改 Request VO")
@Data
public class MemberGroupLessonSaveReqVO {

    @Schema(description = "编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "7")
    private Long id;

    @Schema(description = "分组编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "8")
    @NotNull(message = "分组编号不能为空")
    private Long groupId;

    @Schema(description = "课程编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "9527")
    @NotNull(message = "课程编号不能为空")
    private Long courseId;

}