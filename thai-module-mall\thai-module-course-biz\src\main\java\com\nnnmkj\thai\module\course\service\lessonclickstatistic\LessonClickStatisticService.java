package com.nnnmkj.thai.module.course.service.lessonclickstatistic;

import com.nnnmkj.thai.framework.common.pojo.PageResult;
import com.nnnmkj.thai.module.course.controller.admin.lessonclickstatistic.vo.LessonClickStatisticPageReqVO;
import com.nnnmkj.thai.module.course.controller.admin.lessonclickstatistic.vo.LessonClickStatisticSaveReqVO;
import com.nnnmkj.thai.module.course.dal.dataobject.lessonclickstatistic.LessonClickStatisticDO;
import jakarta.validation.Valid;

/**
 * 课程点击量统计 Service 接口
 *
 * <AUTHOR>
 */
public interface LessonClickStatisticService {

    /**
     * 创建课程点击量统计
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createLessonClickStatistic(@Valid LessonClickStatisticSaveReqVO createReqVO);

    /**
     * 更新课程点击量统计
     *
     * @param updateReqVO 更新信息
     */
    void updateLessonClickStatistic(@Valid LessonClickStatisticSaveReqVO updateReqVO);

    /**
     * 删除课程点击量统计
     *
     * @param id 编号
     */
    void deleteLessonClickStatistic(Long id);

    /**
     * 获得课程点击量统计
     *
     * @param id 编号
     * @return 课程点击量统计
     */
    LessonClickStatisticDO getLessonClickStatistic(Long id);

    /**
     * 获得课程点击量统计分页
     *
     * @param pageReqVO 分页查询
     * @return 课程点击量统计分页
     */
    PageResult<LessonClickStatisticDO> getLessonClickStatisticPage(LessonClickStatisticPageReqVO pageReqVO);

}