package com.nnnmkj.thai.module.course.controller.admin.membergrouplesson.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Schema(description = "管理后台 - 班级-课程关联 Response VO")
@Data
@ExcelIgnoreUnannotated
public class MemberGroupLessonRespVO {

    @Schema(description = "编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "7")
    @ExcelProperty("编号")
    private Long id;

    @Schema(description = "分组编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "8")
    @ExcelProperty("分组编号")
    private Long groupId;

    @Schema(description = "课程编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "9527")
    @ExcelProperty("课程编号")
    private Long courseId;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

    @Schema(description = "课程标题", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("课程标题")
    private String title;

}