package com.nnnmkj.thai.module.course.controller.admin.lessonprocess.vo;

import com.nnnmkj.thai.module.course.dal.dataobject.lessonprocess.LessonChapterProcessDO;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Schema(description = "管理后台 - 课程进度新增/修改 Request VO")
@Data
public class LessonProcessSaveReqVO {

    @Schema(description = "编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "29827")
    private Long id;

    @Schema(description = "课程ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "414")
    @NotNull(message = "课程ID不能为空")
    private Long courseId;

    @Schema(description = "用户ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "8388")
    @NotNull(message = "用户ID不能为空")
    private Long userId;

    @Schema(description = "总章节数", example = "9479")
    private Integer allCount;

    @Schema(description = "已学习章节数", example = "10383")
    private Integer learnedCount;

    @Schema(description = "未学习章节数", example = "22704")
    private Integer notLearnedCount;

    @Schema(description = "当前学习章节ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "22115")
    @NotNull(message = "当前学习章节ID不能为空")
    private Long currentChapterId;

    @Schema(description = "学习进度百分比", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "学习进度百分比不能为空")
    private BigDecimal progressPercentage;

    @Schema(description = "状态", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "状态不能为空")
    private Integer status;

    @Schema(description = "课程章节进度列表")
    private List<LessonChapterProcessDO> lessonChapterProcesss;

}