package com.nnnmkj.thai.module.course.controller.admin.lessonclickstatistic.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

@Schema(description = "管理后台 - 课程点击量统计新增/修改 Request VO")
@Data
public class LessonClickStatisticSaveReqVO {

    @Schema(description = "编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "2197")
    private Long id;

    @Schema(description = "课程编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "16385")
    @NotNull(message = "课程编号不能为空")
    private Long courseLessonId;

    @Schema(description = "点击次数", requiredMode = Schema.RequiredMode.REQUIRED, example = "12645")
    @NotNull(message = "点击次数不能为空")
    private Integer count;

}