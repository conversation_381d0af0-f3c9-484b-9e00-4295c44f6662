package com.nnnmkj.thai.module.course.service.lessonstudystatistic;

import com.nnnmkj.thai.framework.common.pojo.PageResult;
import com.nnnmkj.thai.module.course.controller.admin.lessonstudystatistic.vo.LessonStudyStatisticPageReqVO;
import com.nnnmkj.thai.module.course.controller.admin.lessonstudystatistic.vo.LessonStudyStatisticSaveReqVO;
import com.nnnmkj.thai.module.course.dal.dataobject.lessonstudystatistic.LessonStudyStatisticDO;
import jakarta.validation.Valid;

/**
 * 课程学习统计 Service 接口
 *
 * <AUTHOR>
 */
public interface LessonStudyStatisticService {

    /**
     * 创建课程学习统计
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createLessonStudyStatistic(@Valid LessonStudyStatisticSaveReqVO createReqVO);

    /**
     * 更新课程学习统计
     *
     * @param updateReqVO 更新信息
     */
    void updateLessonStudyStatistic(@Valid LessonStudyStatisticSaveReqVO updateReqVO);

    /**
     * 删除课程学习统计
     *
     * @param id 编号
     */
    void deleteLessonStudyStatistic(Long id);

    /**
     * 获得课程学习统计
     *
     * @param id 编号
     * @return 课程学习统计
     */
    LessonStudyStatisticDO getLessonStudyStatistic(Long id);

    /**
     * 获得课程学习统计分页
     *
     * @param pageReqVO 分页查询
     * @return 课程学习统计分页
     */
    PageResult<LessonStudyStatisticDO> getLessonStudyStatisticPage(LessonStudyStatisticPageReqVO pageReqVO);

}