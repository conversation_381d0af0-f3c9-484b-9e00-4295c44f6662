package com.nnnmkj.thai.module.ai.service.plugin;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.EasyExcel;
import com.nnnmkj.thai.framework.common.exception.util.ServiceExceptionUtil;
import com.nnnmkj.thai.module.ai.controller.app.plugin.vo.FastGPTExcelGenerateReqVO;
import com.nnnmkj.thai.module.ai.controller.app.plugin.vo.FastGPTExcelGenerateRespVO;
import com.nnnmkj.thai.module.infra.api.file.FileApi;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import java.io.ByteArrayOutputStream;
import java.time.Duration;
import java.util.Date;
import java.util.List;

import static com.nnnmkj.thai.module.ai.enums.ErrorCodeConstants.*;

/**
 * FastGPT 插件 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class FastGPTPluginServiceImpl implements FastGPTPluginService {

    @Resource
    private FileApi fileApi;

    @Resource
    private StringRedisTemplate stringRedisTemplate;

    /**
     * Redis中存储文件信息的键前缀
     */
    private static final String REDIS_KEY_PREFIX = "fast_gpt_excel:";

    /**
     * 文件过期时间：15天
     */
    private static final Duration FILE_EXPIRE_TIME = Duration.ofDays(15);

    @Override
    public FastGPTExcelGenerateRespVO generateExcelAndUpload(FastGPTExcelGenerateReqVO reqVO) {
        log.debug("[generateExcelAndUpload][开始生成Excel文件，标题：{}]", reqVO.getTitle());

        try {
            // 1. 参数校验
            validateRequest(reqVO);

            // 2. 生成Excel文件
            byte[] excelBytes = generateExcelFile(reqVO);

            // 3. 生成文件名和路径
            String fileName = generateFileName(reqVO.getTitle());
            String filePath = generateFilePath(fileName);

            // 4. 上传文件到OSS
            String fileUrl = uploadFileToOSS(excelBytes, filePath, fileName);

            // 5. 保存文件信息到Redis
            saveFileInfoToRedis(fileUrl, filePath);

            // 6. 构造响应
            FastGPTExcelGenerateRespVO respVO = new FastGPTExcelGenerateRespVO();
            respVO.setFileUrl(fileUrl);
            respVO.setFileName(fileName);
            respVO.setFileSize((long) excelBytes.length);

            log.debug("[generateExcelAndUpload][Excel文件生成成功，URL：{}，大小：{}字节]", fileUrl, excelBytes.length);
            return respVO;

        } catch (Exception e) {
            log.error("[generateExcelAndUpload][Excel文件生成失败]", e);
            throw ServiceExceptionUtil.exception(FAST_GPT_PLUGIN_EXCEL_GENERATE_FAILED);
        }
    }

    /**
     * 校验请求参数
     */
    private void validateRequest(FastGPTExcelGenerateReqVO reqVO) {
        if (StrUtil.isBlank(reqVO.getTitle())) {
            throw ServiceExceptionUtil.exception(FAST_GPT_PLUGIN_TITLE_REQUIRED);
        }
        if (reqVO.getRowData() == null || reqVO.getRowData().isEmpty()) {
            throw ServiceExceptionUtil.exception(FAST_GPT_PLUGIN_ROW_DATA_REQUIRED);
        }
    }

    /**
     * 生成Excel文件
     */
    private byte[] generateExcelFile(FastGPTExcelGenerateReqVO reqVO) {
        try (ByteArrayOutputStream outputStream = new ByteArrayOutputStream()) {
            // 使用EasyExcel生成Excel文件
            EasyExcel.write(outputStream)
                    .sheet(reqVO.getTitle())
                    .doWrite(reqVO.getRowData());

            return outputStream.toByteArray();
        } catch (Exception e) {
            log.error("[generateExcelFile][生成Excel文件失败]", e);
            throw ServiceExceptionUtil.exception(FAST_GPT_PLUGIN_EXCEL_GENERATE_FAILED);
        }
    }

    /**
     * 生成文件名
     */
    private String generateFileName(String title) {
        String timestamp = DateUtil.format(new Date(), "yyyyMMdd_HHmmss");
        String uuid = IdUtil.fastSimpleUUID().substring(0, 8);
        return String.format("%s_%s_%s.xlsx", title, timestamp, uuid);
    }

    /**
     * 生成文件路径
     */
    private String generateFilePath(String fileName) {
        String dateFolder = DateUtil.format(new Date(), "yyyy/MM/dd");
        return String.format("fast-gpt/excel/%s/%s", dateFolder, fileName);
    }

    /**
     * 上传文件到OSS
     */
    private String uploadFileToOSS(byte[] fileBytes, String filePath, String fileName) {
        try {
            return fileApi.createFile(fileName, filePath, fileBytes);
        } catch (Exception e) {
            log.error("[uploadFileToOSS][文件上传失败，路径：{}]", filePath, e);
            throw ServiceExceptionUtil.exception(FAST_GPT_PLUGIN_FILE_UPLOAD_FAILED);
        }
    }

    /**
     * 保存文件信息到Redis，设置15天过期时间
     */
    private void saveFileInfoToRedis(String fileUrl, String filePath) {
        try {
            String redisKey = REDIS_KEY_PREFIX + IdUtil.fastSimpleUUID();
            String fileInfo = String.format("%s|%s", fileUrl, filePath);
            stringRedisTemplate.opsForValue().set(redisKey, fileInfo, FILE_EXPIRE_TIME);
            log.debug("[saveFileInfoToRedis][文件信息已保存到Redis，键：{}，过期时间：{}天]", redisKey, FILE_EXPIRE_TIME.toDays());
        } catch (Exception e) {
            log.error("[saveFileInfoToRedis][保存文件信息到Redis失败]", e);
            throw ServiceExceptionUtil.exception(FAST_GPT_PLUGIN_REDIS_SAVE_FAILED);
        }
    }

}
