package com.nnnmkj.thai.module.course.controller.admin.assignmentquestion.vo;

import com.nnnmkj.thai.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

import static com.nnnmkj.thai.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 课程作业题目分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class AssignmentQuestionPageReqVO extends PageParam {

    @Schema(description = "课程ID", example = "27036")
    private Long courseId;

    @Schema(description = "作业ID", example = "12524")
    private Long assignmentId;

    @Schema(description = "题目类型", example = "2")
    private Integer questionType;

    @Schema(description = "难易度")
    private Integer difficulty;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}