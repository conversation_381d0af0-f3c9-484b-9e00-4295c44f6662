package com.nnnmkj.thai.module.course.controller.admin.lessoncollectionstatistic;

import com.nnnmkj.thai.framework.apilog.core.annotation.ApiAccessLog;
import com.nnnmkj.thai.framework.common.pojo.CommonResult;
import com.nnnmkj.thai.framework.common.pojo.PageParam;
import com.nnnmkj.thai.framework.common.pojo.PageResult;
import com.nnnmkj.thai.framework.common.util.object.BeanUtils;
import com.nnnmkj.thai.framework.excel.core.util.ExcelUtils;
import com.nnnmkj.thai.module.course.controller.admin.lessoncollectionstatistic.vo.LessonCollectionStatisticPageReqVO;
import com.nnnmkj.thai.module.course.controller.admin.lessoncollectionstatistic.vo.LessonCollectionStatisticRespVO;
import com.nnnmkj.thai.module.course.controller.admin.lessoncollectionstatistic.vo.LessonCollectionStatisticSaveReqVO;
import com.nnnmkj.thai.module.course.dal.dataobject.lessoncollectionstatistic.LessonCollectionStatisticDO;
import com.nnnmkj.thai.module.course.service.lessoncollectionstatistic.LessonCollectionStatisticService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.util.List;

import static com.nnnmkj.thai.framework.apilog.core.enums.OperateTypeEnum.EXPORT;
import static com.nnnmkj.thai.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 课程收藏统计")
@RestController
@RequestMapping("/course/lesson-collection-statistic")
@Validated
public class LessonCollectionStatisticController {

    @Resource
    private LessonCollectionStatisticService lessonCollectionStatisticService;

    @PostMapping("/create")
    @Operation(summary = "创建课程收藏统计")
    @PreAuthorize("@ss.hasPermission('course:lesson-collection-statistic:create')")
    public CommonResult<Long> createLessonCollectionStatistic(@Valid @RequestBody LessonCollectionStatisticSaveReqVO createReqVO) {
        return success(lessonCollectionStatisticService.createLessonCollectionStatistic(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新课程收藏统计")
    @PreAuthorize("@ss.hasPermission('course:lesson-collection-statistic:update')")
    public CommonResult<Boolean> updateLessonCollectionStatistic(@Valid @RequestBody LessonCollectionStatisticSaveReqVO updateReqVO) {
        lessonCollectionStatisticService.updateLessonCollectionStatistic(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除课程收藏统计")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('course:lesson-collection-statistic:delete')")
    public CommonResult<Boolean> deleteLessonCollectionStatistic(@RequestParam("id") Long id) {
        lessonCollectionStatisticService.deleteLessonCollectionStatistic(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得课程收藏统计")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('course:lesson-collection-statistic:query')")
    public CommonResult<LessonCollectionStatisticRespVO> getLessonCollectionStatistic(@RequestParam("id") Long id) {
        LessonCollectionStatisticDO lessonCollectionStatistic = lessonCollectionStatisticService.getLessonCollectionStatistic(id);
        return success(BeanUtils.toBean(lessonCollectionStatistic, LessonCollectionStatisticRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得课程收藏统计分页")
    @PreAuthorize("@ss.hasPermission('course:lesson-collection-statistic:query')")
    public CommonResult<PageResult<LessonCollectionStatisticRespVO>> getLessonCollectionStatisticPage(@Valid LessonCollectionStatisticPageReqVO pageReqVO) {
        PageResult<LessonCollectionStatisticDO> pageResult = lessonCollectionStatisticService.getLessonCollectionStatisticPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, LessonCollectionStatisticRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出课程收藏统计 Excel")
    @PreAuthorize("@ss.hasPermission('course:lesson-collection-statistic:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportLessonCollectionStatisticExcel(@Valid LessonCollectionStatisticPageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<LessonCollectionStatisticDO> list = lessonCollectionStatisticService.getLessonCollectionStatisticPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "课程收藏统计.xls", "数据", LessonCollectionStatisticRespVO.class,
                        BeanUtils.toBean(list, LessonCollectionStatisticRespVO.class));
    }

}