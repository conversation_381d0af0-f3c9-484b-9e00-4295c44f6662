package com.nnnmkj.thai.module.course.service.assignmentreleasequestion;

import cn.hutool.core.collection.CollectionUtil;
import com.nnnmkj.thai.framework.common.pojo.PageResult;
import com.nnnmkj.thai.framework.common.util.collection.CollectionUtils;
import com.nnnmkj.thai.framework.common.util.object.BeanUtils;
import com.nnnmkj.thai.module.course.controller.admin.assignmentreleasequestion.vo.AssignmentReleaseQuestionPageReqVO;
import com.nnnmkj.thai.module.course.controller.admin.assignmentreleasequestion.vo.AssignmentReleaseQuestionSaveReqVO;
import com.nnnmkj.thai.module.course.controller.app.assignmentreleasequestion.vo.AppAssignmentReleaseQuestionRespVO;
import com.nnnmkj.thai.module.course.dal.dataobject.assignmentreleasequestion.AssignmentReleaseQuestionDO;
import com.nnnmkj.thai.module.course.dal.dataobject.assignmentreleasequestion.AssignmentReleaseQuestionOptionDO;
import com.nnnmkj.thai.module.course.dal.mysql.assignmentreleasequestion.AssignmentReleaseQuestionMapper;
import com.nnnmkj.thai.module.course.dal.mysql.assignmentreleasequestion.AssignmentReleaseQuestionOptionMapper;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import java.util.Collections;
import java.util.List;
import java.util.Map;

import static com.nnnmkj.thai.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.nnnmkj.thai.module.course.enums.ErrorCodeConstants.ASSIGNMENT_RELEASE_QUESTION_NOT_EXISTS;

/**
 * 课程作业发布题目 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class AssignmentReleaseQuestionServiceImpl implements AssignmentReleaseQuestionService {

    @Resource
    private AssignmentReleaseQuestionMapper assignmentReleaseQuestionMapper;
    @Resource
    private AssignmentReleaseQuestionOptionMapper assignmentReleaseQuestionOptionMapper;

    @Override
    public Long createAssignmentReleaseQuestion(AssignmentReleaseQuestionSaveReqVO createReqVO) {
        // 插入
        AssignmentReleaseQuestionDO assignmentReleaseQuestion = BeanUtils.toBean(createReqVO, AssignmentReleaseQuestionDO.class);
        assignmentReleaseQuestionMapper.insert(assignmentReleaseQuestion);
        // 返回
        return assignmentReleaseQuestion.getId();
    }

    @Override
    public void updateAssignmentReleaseQuestionOptions(Long releaseQuestionId, List<AssignmentReleaseQuestionOptionDO> options) {
        assignmentReleaseQuestionOptionMapper.deleteByQuestionId(releaseQuestionId);
        options.forEach(o -> {
            o.setQuestionId(releaseQuestionId);
            o.setId(null);
        });
        assignmentReleaseQuestionOptionMapper.insertBatch(options);
    }

    @Override
    public void updateAssignmentReleaseQuestion(AssignmentReleaseQuestionSaveReqVO updateReqVO) {
        // 校验存在
        validateAssignmentReleaseQuestionExists(updateReqVO.getId());
        // 更新
        AssignmentReleaseQuestionDO updateObj = BeanUtils.toBean(updateReqVO, AssignmentReleaseQuestionDO.class);
        assignmentReleaseQuestionMapper.updateById(updateObj);
    }

    @Override
    public void deleteAssignmentReleaseQuestion(Long id) {
        // 校验存在
        validateAssignmentReleaseQuestionExists(id);
        // 删除
        assignmentReleaseQuestionMapper.deleteById(id);
    }

    private void validateAssignmentReleaseQuestionExists(Long id) {
        if (assignmentReleaseQuestionMapper.selectById(id) == null) {
            throw exception(ASSIGNMENT_RELEASE_QUESTION_NOT_EXISTS);
        }
    }

    @Override
    public AssignmentReleaseQuestionDO getAssignmentReleaseQuestion(Long id) {
        return assignmentReleaseQuestionMapper.selectById(id);
    }

    @Override
    public List<AssignmentReleaseQuestionDO> getAssignmentReleaseQuestionByAssignmentReleaseId(Long id) {
        return assignmentReleaseQuestionMapper.selectList(AssignmentReleaseQuestionDO::getAssignmentReleaseId, id);
    }

    @Override
    public PageResult<AssignmentReleaseQuestionDO> getAssignmentReleaseQuestionPage(AssignmentReleaseQuestionPageReqVO pageReqVO) {
        return assignmentReleaseQuestionMapper.selectPage(pageReqVO);
    }

    @Override
    public void deleteQuestionByAssignmentReleaseId(Long assignmentReleaseId) {
        List<AssignmentReleaseQuestionDO> questionDOS = assignmentReleaseQuestionMapper.selectList(AssignmentReleaseQuestionDO::getAssignmentReleaseId, assignmentReleaseId);
        List<Long> questionIds = questionDOS.stream().map(AssignmentReleaseQuestionDO::getId).toList();
        assignmentReleaseQuestionMapper.deleteByIds(questionIds);
        assignmentReleaseQuestionOptionMapper.deleteByQuestionIds(questionIds);
    }

    @Override
    public List<AppAssignmentReleaseQuestionRespVO> getAssignmentReleaseQuestionList(Long assignmentReleaseId) {
        // 获取指定作业发布ID对应的所有题目
        List<AssignmentReleaseQuestionDO> questions = getAssignmentReleaseQuestionByAssignmentReleaseId(assignmentReleaseId);
        if (CollectionUtil.isEmpty(questions)) {
            return Collections.emptyList();
        }

        // 将题目DO转换为响应VO
        List<AppAssignmentReleaseQuestionRespVO> respVOS = BeanUtils.toBean(questions, AppAssignmentReleaseQuestionRespVO.class);

        // 提取所有题目ID，查询对应的选项
        List<Long> questionIds = CollectionUtils.convertList(respVOS, AppAssignmentReleaseQuestionRespVO::getId);
        if (CollectionUtil.isEmpty(questionIds)) {
            return respVOS;
        }
        List<AssignmentReleaseQuestionOptionDO> optionDOS = assignmentReleaseQuestionOptionMapper.selectListByQuestionIds(questionIds);

        // 按照题目ID分组选项数据
        Map<Long, List<AssignmentReleaseQuestionOptionDO>> questionIdToOptionsMap = CollectionUtils.convertMultiMap(optionDOS, AssignmentReleaseQuestionOptionDO::getQuestionId);

        // 为每个题目设置对应的选项列表
        for (AppAssignmentReleaseQuestionRespVO respVO : respVOS) {
            respVO.setOptionList(questionIdToOptionsMap.get(respVO.getId()));
        }

        return respVOS;
    }

}