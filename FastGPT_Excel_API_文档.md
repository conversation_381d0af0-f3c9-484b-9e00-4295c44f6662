# FastGPT Excel 生成 API 文档

## 功能概述

该功能接受FastGPT工作流传递来的数据，生成Excel文件后，上传到OSS中，然后返回URL给用户。同时将这个文件存到Redis中，设置一个定时器，15天后进行删除。

## API 接口

### 生成Excel文件并上传到OSS

**接口地址：** `POST /fast-gpt/plugin/generate-excel`

**请求参数：**

```json
{
  "title": "学生成绩表",
  "rowData": [
    ["姓名", "语文", "数学", "英语"],
    ["张三", "85", "90", "88"],
    ["李四", "92", "87", "95"],
    ["王五", "78", "85", "82"]
  ]
}
```

**参数说明：**

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| title | String | 是 | Excel文件标题 |
| rowData | List<List<String>> | 是 | Excel数据行，第一行为表头 |

**响应参数：**

```json
{
  "code": 0,
  "data": {
    "fileUrl": "https://example.com/files/excel/20240101/学生成绩表_20240101_123456_abcd1234.xlsx",
    "fileName": "学生成绩表_20240101_123456_abcd1234.xlsx",
    "fileSize": 8192
  },
  "msg": "操作成功"
}
```

**响应参数说明：**

| 参数名 | 类型 | 说明 |
|--------|------|------|
| fileUrl | String | Excel文件下载URL |
| fileName | String | 文件名 |
| fileSize | Long | 文件大小（字节） |

## 数据处理逻辑

### 输入数据格式
假如输入数据是：
```json
[
  ["你好", "再见"],
  ["哈哈", "呵呵"]
]
```

### 生成的Excel表格
生成的Excel表格为：

| 你好 | 再见 |
|------|------|
| 哈哈 | 呵呵 |

第一行作为表头，后续行作为数据行。

## 文件管理

### 文件存储
- 文件上传到OSS存储
- 文件路径格式：`fast-gpt/excel/yyyy/MM/dd/文件名`
- 文件名格式：`{title}_{yyyyMMdd_HHmmss}_{uuid}.xlsx`

### 文件清理
- 文件信息存储在Redis中，键前缀为：`fast_gpt_excel:`
- 文件过期时间：15天
- 定时任务每天凌晨2点执行清理任务
- 清理过期的Excel文件和Redis记录

## 错误码

| 错误码 | 错误信息 | 说明 |
|--------|----------|------|
| 1_040_016_000 | 标题不能为空 | title参数为空 |
| 1_040_016_001 | 表格数据不能为空 | rowData参数为空 |
| 1_040_016_002 | Excel文件生成失败 | Excel文件生成过程中出错 |
| 1_040_016_003 | 文件上传失败 | 文件上传到OSS失败 |
| 1_040_016_004 | 文件信息保存到缓存失败 | Redis保存失败 |

## 使用示例

### cURL 示例

```bash
curl -X POST "http://localhost:8080/fast-gpt/plugin/generate-excel" \
  -H "Content-Type: application/json" \
  -d '{
    "title": "学生成绩表",
    "rowData": [
      ["姓名", "语文", "数学", "英语"],
      ["张三", "85", "90", "88"],
      ["李四", "92", "87", "95"]
    ]
  }'
```

### JavaScript 示例

```javascript
const response = await fetch('/fast-gpt/plugin/generate-excel', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
  },
  body: JSON.stringify({
    title: '学生成绩表',
    rowData: [
      ['姓名', '语文', '数学', '英语'],
      ['张三', '85', '90', '88'],
      ['李四', '92', '87', '95']
    ]
  })
});

const result = await response.json();
console.log('文件URL:', result.data.fileUrl);
```

## 注意事项

1. 文件会在15天后自动删除，请及时下载需要保存的文件
2. 文件大小限制取决于OSS配置
3. 表格数据中的第一行会作为表头处理
4. 支持中文文件名和内容
5. 定时清理任务在每天凌晨2点执行
