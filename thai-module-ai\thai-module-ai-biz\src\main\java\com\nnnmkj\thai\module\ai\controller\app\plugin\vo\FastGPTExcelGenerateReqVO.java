package com.nnnmkj.thai.module.ai.controller.app.plugin.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;

import java.util.List;

/**
 * FastGPT Excel 生成请求 VO
 *
 * <AUTHOR>
 */
@Schema(description = "FastGPT Excel 生成请求 VO")
@Data
public class FastGPTExcelGenerateReqVO {

    @Schema(description = "Excel文件标题", requiredMode = Schema.RequiredMode.REQUIRED, example = "学生成绩表")
    @NotBlank(message = "Excel文件标题不能为空")
    private String title;

    @Schema(description = "Excel数据行，第一行为表头", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "Excel数据不能为空")
    private List<List<String>> rowData;

}
