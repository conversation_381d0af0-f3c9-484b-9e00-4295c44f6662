package com.nnnmkj.thai.module.course.dal.mysql.lessonstudystatistic;

import com.nnnmkj.thai.framework.common.pojo.PageResult;
import com.nnnmkj.thai.framework.mybatis.core.mapper.BaseMapperX;
import com.nnnmkj.thai.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.nnnmkj.thai.module.course.controller.admin.lessonstudystatistic.vo.LessonStudyStatisticPageReqVO;
import com.nnnmkj.thai.module.course.dal.dataobject.lessonstudystatistic.LessonStudyStatisticDO;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 课程学习统计 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface LessonStudyStatisticMapper extends BaseMapperX<LessonStudyStatisticDO> {

    default PageResult<LessonStudyStatisticDO> selectPage(LessonStudyStatisticPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<LessonStudyStatisticDO>()
                .eqIfPresent(LessonStudyStatisticDO::getCourseLessonId, reqVO.getCourseLessonId())
                .eqIfPresent(LessonStudyStatisticDO::getCount, reqVO.getCount())
                .betweenIfPresent(LessonStudyStatisticDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(LessonStudyStatisticDO::getId));
    }

    /**
     * 通用原子更新方法
     * @param delta 变化量（正数增加，负数减少）
     */
    @Insert("INSERT INTO course_lesson_study_statistic (course_lesson_id, count) " +
            "VALUES (#{lessonId}, #{delta}) " +
            "ON DUPLICATE KEY UPDATE count = GREATEST(count + #{delta}, 0)")
    void atomicUpdateCount(@Param("lessonId") Long lessonId, @Param("delta") Integer delta);

}