package com.nnnmkj.thai.module.course.controller.admin.assignmentscore.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Schema(description = "管理后台 - 课程作业成绩 Response VO")
@Data
@ExcelIgnoreUnannotated
public class AssignmentScoreRespVO {

    @Schema(description = "编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "16917")
    @ExcelProperty("编号")
    private Long id;

    @Schema(description = "课程ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "27291")
    @ExcelProperty("课程ID")
    private Long courseId;

    @Schema(description = "作业发布ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "11920")
    @ExcelProperty("作业发布ID")
    private Long assignmentReleaseId;

    @Schema(description = "作答试卷ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "13319")
    @ExcelProperty("作答试卷ID")
    private Long answerPaperId;

    @Schema(description = "用户ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "3160")
    @ExcelProperty("用户ID")
    private Long userId;

    @Schema(description = "总题目数量", example = "27895")
    @ExcelProperty("总题目数量")
    private Integer allCount;

    @Schema(description = "正确题目数量", example = "31859")
    @ExcelProperty("正确题目数量")
    private Integer correctCount;

    @Schema(description = "错误题目数量", example = "5480")
    @ExcelProperty("错误题目数量")
    private Integer errorCount;

    @Schema(description = "成绩", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("成绩")
    private Double score;

    @Schema(description = "答题时间（s）")
    @ExcelProperty("答题时间（s）")
    private Integer timeTaken;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}