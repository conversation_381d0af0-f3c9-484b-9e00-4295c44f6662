package com.nnnmkj.thai.module.course.service.membergrouplesson;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.nnnmkj.thai.framework.common.enums.CommonStatusEnum;
import com.nnnmkj.thai.framework.common.pojo.PageResult;
import com.nnnmkj.thai.framework.common.util.object.BeanUtils;
import com.nnnmkj.thai.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.nnnmkj.thai.module.course.controller.admin.membergrouplesson.vo.LessonAndAllGroupVO;
import com.nnnmkj.thai.module.course.controller.admin.membergrouplesson.vo.MemberGroupLessonPageReqVO;
import com.nnnmkj.thai.module.course.controller.admin.membergrouplesson.vo.MemberGroupLessonSaveReqVO;
import com.nnnmkj.thai.module.course.controller.app.membergrouplesson.vo.AppMemberGroupLessonRespVO;
import com.nnnmkj.thai.module.course.controller.app.membergrouplesson.vo.AppMemberGroupLessonSearchReqVO;
import com.nnnmkj.thai.module.course.convert.membergrouplesson.MembergrouplessonConvert;
import com.nnnmkj.thai.module.course.dal.dataobject.lesson.LessonDO;
import com.nnnmkj.thai.module.course.dal.dataobject.membergrouplesson.MemberGroupLessonDO;
import com.nnnmkj.thai.module.course.dal.mysql.membergrouplesson.MemberGroupLessonMapper;
import com.nnnmkj.thai.module.course.service.lesson.LessonService;
import com.nnnmkj.thai.module.member.api.group.MemberGroupApi;
import com.nnnmkj.thai.module.member.api.group.dto.MemberGroupRespDTO;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import java.util.*;
import java.util.stream.Collectors;

import static com.nnnmkj.thai.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.nnnmkj.thai.module.course.enums.ErrorCodeConstants.MEMBER_GROUP_LESSON_NOT_EXISTS;

/**
 * 班级-课程关联 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class MemberGroupLessonServiceImpl implements MemberGroupLessonService {

    @Resource
    private MemberGroupLessonMapper memberGroupLessonMapper;
    
    @Resource
    private MemberGroupApi memberGroupApi;
    
    @Resource
    private LessonService lessonService;

    @Override
    public Long createMemberGroupLesson(MemberGroupLessonSaveReqVO createReqVO) {
        // 插入
        MemberGroupLessonDO memberGroupLesson = BeanUtils.toBean(createReqVO, MemberGroupLessonDO.class);
        memberGroupLessonMapper.insert(memberGroupLesson);
        // 返回
        return memberGroupLesson.getId();
    }

    @Override
    public void updateMemberGroupLesson(MemberGroupLessonSaveReqVO updateReqVO) {
        // 校验存在
        validateMemberGroupLessonExists(updateReqVO.getId());
        // 更新
        MemberGroupLessonDO updateObj = BeanUtils.toBean(updateReqVO, MemberGroupLessonDO.class);
        memberGroupLessonMapper.updateById(updateObj);
    }

    @Override
    public void deleteMemberGroupLesson(Long id) {
        // 校验存在
        validateMemberGroupLessonExists(id);
        // 删除
        memberGroupLessonMapper.deleteById(id);
    }

    private void validateMemberGroupLessonExists(Long id) {
        if (memberGroupLessonMapper.selectById(id) == null) {
            throw exception(MEMBER_GROUP_LESSON_NOT_EXISTS);
        }
    }

    @Override
    public MemberGroupLessonDO getMemberGroupLesson(Long id) {
        return memberGroupLessonMapper.selectById(id);
    }

    @Override
    public PageResult<MemberGroupLessonDO> getMemberGroupLessonPage(MemberGroupLessonPageReqVO pageReqVO) {
        return memberGroupLessonMapper.selectPage(pageReqVO);
    }

    @Override
    public List<MemberGroupLessonDO> getMemberGroupLessonListByGroupId(Long groupId) {
        return memberGroupLessonMapper.selectList(
            MemberGroupLessonDO::getGroupId, groupId);
    }

    @Override
    public List<MemberGroupLessonDO> getMemberGroupLessonListByCourseId(Long courseId) {
        return memberGroupLessonMapper.selectList(
            MemberGroupLessonDO::getCourseId, courseId);
    }
    
    @Override
    public List<MemberGroupLessonDO> searchMemberGroupLessons(AppMemberGroupLessonSearchReqVO reqVO) {
        // 根据课程ID查询关联关系
        List<MemberGroupLessonDO> memberGroupLessons = memberGroupLessonMapper.selectList(
                new LambdaQueryWrapperX<MemberGroupLessonDO>()
                .eqIfPresent(MemberGroupLessonDO::getCourseId, reqVO.getCourseId()));
        
        if (CollUtil.isEmpty(memberGroupLessons)) {
            return Collections.emptyList();
        }
        
        // 如果没有关键词，直接返回结果
        if (StrUtil.isBlank(reqVO.getKeyword())) {
            return memberGroupLessons;
        }
        
        // 获取所有班级ID
        List<Long> groupIds = memberGroupLessons.stream()
                .map(MemberGroupLessonDO::getGroupId)
                .collect(Collectors.toList());
        
        // 获取班级详细信息
        List<MemberGroupRespDTO> groupList = memberGroupApi.getGroupList(groupIds);
        if (CollUtil.isEmpty(groupList)) {
            return Collections.emptyList();
        }
        
        // 过滤掉非开启状态的班级
        List<MemberGroupRespDTO> enabledGroups = groupList.stream()
                .filter(group -> CommonStatusEnum.ENABLE.getStatus().equals(group.getStatus()))
                .collect(Collectors.toList());
        
        if (CollUtil.isEmpty(enabledGroups)) {
            return Collections.emptyList();
        }
        
        // 根据关键词过滤班级
        String keyword = reqVO.getKeyword().toLowerCase();
        List<MemberGroupRespDTO> filteredGroups = enabledGroups.stream()
                .filter(group -> {
                    // 班级名称包含关键词
                    if (StrUtil.isNotBlank(group.getName()) && 
                            group.getName().toLowerCase().contains(keyword)) {
                        return true;
                    }
                    // 班级备注包含关键词
                    return StrUtil.isNotBlank(group.getRemark()) && 
                            group.getRemark().toLowerCase().contains(keyword);
                })
                .collect(Collectors.toList());
        
        if (CollUtil.isEmpty(filteredGroups)) {
            return Collections.emptyList();
        }
        
        // 获取匹配关键词的班级ID列表
        List<Long> filteredGroupIds = filteredGroups.stream()
                .map(MemberGroupRespDTO::getId).toList();
        
        // 过滤只保留匹配关键词的班级关联
        return memberGroupLessons.stream()
                .filter(relation -> filteredGroupIds.contains(relation.getGroupId()))
                .collect(Collectors.toList());
    }
    
    /**
     * 获取课程关联班级的详细信息列表
     *
     * @param courseId 课程ID
     * @return 班级详细信息列表
     */
    public List<AppMemberGroupLessonRespVO> getClassesWithDetailsByCourseId(Long courseId) {
        // 获取课程信息
        LessonDO lesson = lessonService.getLesson(courseId);
        if (lesson == null) {
            return Collections.emptyList();
        }
        
        // 获取课程关联的班级列表
        List<MemberGroupLessonDO> memberGroupLessons = getMemberGroupLessonListByCourseId(courseId);
        if (CollUtil.isEmpty(memberGroupLessons)) {
            return Collections.emptyList();
        }
        
        return getClassesWithDetails(memberGroupLessons, Collections.singletonList(lesson));
    }
    
    /**
     * 获取课程关联班级的详细信息列表（搜索）
     *
     * @param reqVO 搜索请求
     * @return 班级详细信息列表
     */
    public List<AppMemberGroupLessonRespVO> searchClassesWithDetails(AppMemberGroupLessonSearchReqVO reqVO) {
        // 获取课程信息
        LessonDO lesson = null;
        if (reqVO.getCourseId() != null) {
            lesson = lessonService.getLesson(reqVO.getCourseId());
            if (lesson == null) {
                return Collections.emptyList();
            }
        }
        
        // 搜索课程关联的班级列表
        List<MemberGroupLessonDO> memberGroupLessons = searchMemberGroupLessons(reqVO);
        if (CollUtil.isEmpty(memberGroupLessons)) {
            return Collections.emptyList();
        }
        
        return getClassesWithDetails(memberGroupLessons, lesson != null ? Collections.singletonList(lesson) : Collections.emptyList());
    }
    
    /**
     * 获取班级的详细信息
     * 
     * @param memberGroupLessons 班级课程关联列表
     * @param lessons 课程列表
     * @return 班级详细信息列表
     */
    private List<AppMemberGroupLessonRespVO> getClassesWithDetails(List<MemberGroupLessonDO> memberGroupLessons, List<LessonDO> lessons) {
        // 获取所有班级ID
        List<Long> groupIds = memberGroupLessons.stream()
                .map(MemberGroupLessonDO::getGroupId)
                .collect(Collectors.toList());
        
        // 获取所有班级详细信息
        List<MemberGroupRespDTO> groupList = memberGroupApi.getGroupList(groupIds);
        
        // 过滤掉非开启状态的班级
        List<MemberGroupRespDTO> enabledGroups = groupList.stream()
                .filter(group -> CommonStatusEnum.ENABLE.getStatus().equals(group.getStatus()))
                .collect(Collectors.toList());
        
        if (CollUtil.isEmpty(enabledGroups)) {
            return Collections.emptyList();
        }
        
        // 创建班级ID到班级详情的映射
        Map<Long, MemberGroupRespDTO> groupMap = enabledGroups.stream()
                .collect(Collectors.toMap(MemberGroupRespDTO::getId, group -> group));
        
        // 过滤只保留开启状态班级的关联
        List<MemberGroupLessonDO> filteredGroupLessons = memberGroupLessons.stream()
                .filter(relation -> groupMap.containsKey(relation.getGroupId()))
                .collect(Collectors.toList());
        
        // 转换为VO
        List<AppMemberGroupLessonRespVO> respVOList = MembergrouplessonConvert.INSTANCE.appConvertList(
                filteredGroupLessons, lessons);
        
        // 填充班级详细信息
        for (AppMemberGroupLessonRespVO respVO : respVOList) {
            MemberGroupRespDTO group = groupMap.get(respVO.getGroupId());
            if (group != null) {
                respVO.setGroupName(group.getName());
                respVO.setGroupRemark(group.getRemark());
                respVO.setGroupUserId(group.getUserId());
            }
        }
        
        return respVOList;
    }

    @Override
    public int getLessonCountByGroupId(Long groupId) {
        if (groupId == null) {
            return 0;
        }
        return memberGroupLessonMapper.selectLessonCountByGroupId(groupId);
    }
    
    @Override
    public Map<Long, Integer> getLessonCountMapByGroupIds(List<Long> groupIds) {
        if (CollUtil.isEmpty(groupIds)) {
            return Collections.emptyMap();
        }
        
        Map<Long, Integer> countMap = new HashMap<>(groupIds.size());
        for (Long groupId : groupIds) {
            countMap.put(groupId, getLessonCountByGroupId(groupId));
        }
        return countMap;
    }

    @Override
    public void updateAllMemberGroupLesson(LessonAndAllGroupVO updateReqVO) {
        //判断是否有传入班级id，如果没有则全部关联删除
        if (updateReqVO.getGroupIds() != null) {

            //判断更新的班级关联有没有变化，如果有变化，则删除原来的关联关系
            Set<Long> newGroupIdsSet = new HashSet<>(updateReqVO.getGroupIds());
            List<MemberGroupLessonDO> memberGroupLessonDOS = memberGroupLessonMapper.selectByCourseId(updateReqVO.getCourseId());
            //从memberGroupLessonDOS中获取课程id对应的班级id集合
            Set<Long> oldGroupIds = memberGroupLessonDOS.stream().map(MemberGroupLessonDO::getGroupId).collect(Collectors.toSet());


            // 2. 计算需要删除的元素
            Set<Long> toRemove = oldGroupIds.stream().collect(Collectors.toSet());
            //  从旧的数据中移除与新数据重复的元素，保留不在新数据中的元素
            toRemove.removeAll(newGroupIdsSet);
            Map<Long, Set<Long>> toRemoveMap = new HashMap<>();
            //添加课程id和对应的班级id集合到toRemoveMap中
            toRemoveMap.put(updateReqVO.getCourseId(), toRemove);

            // 3. 删除非共同元素
            if (!toRemove.isEmpty()) {
                // 调用删除方法，
                memberGroupLessonMapper.delectByCourseIdAndGroupId(toRemoveMap);
            }
            //  4. 添加新的元素
            newGroupIdsSet.removeAll(oldGroupIds);

            if (!newGroupIdsSet.isEmpty()) {
                List<MemberGroupLessonDO> toInsert = new ArrayList<>();
                Long courseId = updateReqVO.getId();
                for (Long groupId : newGroupIdsSet) {
                    MemberGroupLessonDO relation = new MemberGroupLessonDO();
                    relation.setGroupId(groupId);
                    relation.setCourseId(courseId);
                    toInsert.add(relation);
                }
                for (MemberGroupLessonDO relation : toInsert) {
                    memberGroupLessonMapper.insert(relation);
                }
            }

        } else {
            memberGroupLessonMapper.delectByCourseId(updateReqVO.getId());
        }
    }


}