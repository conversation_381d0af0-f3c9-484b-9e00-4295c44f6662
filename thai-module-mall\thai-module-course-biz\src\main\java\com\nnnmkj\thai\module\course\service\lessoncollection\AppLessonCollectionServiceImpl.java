package com.nnnmkj.thai.module.course.service.lessoncollection;

import cn.hutool.core.collection.CollUtil;
import com.nnnmkj.thai.framework.common.util.common.CommonUtils;
import com.nnnmkj.thai.framework.common.util.object.BeanUtils;
import com.nnnmkj.thai.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.nnnmkj.thai.module.course.controller.app.lessoncollection.vo.AppLessonCollectionSaveReqVO;
import com.nnnmkj.thai.module.course.dal.dataobject.lessoncollection.LessonCollectionDO;
import com.nnnmkj.thai.module.course.dal.mysql.lessoncollection.LessonCollectionMapper;
import com.nnnmkj.thai.module.course.dal.mysql.lessoncollectionstatistic.LessonCollectionStatisticMapper;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import java.util.Collection;
import java.util.Collections;
import java.util.List;

/**
 * 课程收藏 Service 实现类 (APP)
 *
 * <AUTHOR>
 */
@Service
@Validated
public class AppLessonCollectionServiceImpl implements AppLessonCollectionService {

    @Resource
    private LessonCollectionMapper lessonCollectionMapper;
    
    @Resource
    private LessonCollectionStatisticMapper lessonCollectionStatisticMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long createLessonCollection(AppLessonCollectionSaveReqVO createReqVO) {
        // 插入
        LessonCollectionDO lessonCollection = BeanUtils.toBean(createReqVO, LessonCollectionDO.class);
        lessonCollectionMapper.insert(lessonCollection);
        
        // 更新收藏统计
        lessonCollectionStatisticMapper.atomicUpdateCount(lessonCollection.getLessonId(), 1);
        
        // 返回
        return lessonCollection.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteLessonCollection(Long userId, Integer userType, Long lessonId) {
        String creatorQuery = CommonUtils.getCreatorQueryRequired(userId, userType);
        lessonCollectionMapper.delete(new LambdaQueryWrapperX<LessonCollectionDO>()
                .eq(LessonCollectionDO::getLessonId, lessonId)
                .like(LessonCollectionDO::getCreator, creatorQuery));
        
        // 更新收藏统计
        lessonCollectionStatisticMapper.atomicUpdateCount(lessonId, -1);
    }

    @Override
    public List<LessonCollectionDO> getLessonCollectionList(Long userId, Integer userType, Collection<Long> lessonIds) {
        if (CollUtil.isEmpty(lessonIds)) {
            return Collections.emptyList();
        }
        String creatorQuery = CommonUtils.getCreatorQueryRequired(userId, userType);
        return lessonCollectionMapper.selectList(new LambdaQueryWrapperX<LessonCollectionDO>()
                .in(LessonCollectionDO::getLessonId, lessonIds)
                .like(LessonCollectionDO::getCreator, creatorQuery));
    }

    @Override
    public List<LessonCollectionDO> getLessonCollectionListByUserId(Long userId, Integer userType) {
        String creatorQuery = CommonUtils.getCreatorQueryRequired(userId, userType);
        return lessonCollectionMapper.selectList(new LambdaQueryWrapperX<LessonCollectionDO>()
                .like(LessonCollectionDO::getCreator, creatorQuery)
                .orderByDesc(LessonCollectionDO::getCreateTime));
    }

    @Override
    public boolean isUserCollectedLesson(Long lessonId, Long userId, Integer userType) {
        String creatorQuery = CommonUtils.getCreatorQueryRequired(userId, userType);
        return lessonCollectionMapper.exists(new LambdaQueryWrapperX<LessonCollectionDO>()
                .eq(LessonCollectionDO::getLessonId, lessonId)
                .like(LessonCollectionDO::getCreator, creatorQuery)
        );
    }
}