package com.nnnmkj.thai.module.course.convert.lesson;

import com.nnnmkj.thai.framework.common.enums.UserTypeEnum;
import com.nnnmkj.thai.framework.common.pojo.PageResult;
import com.nnnmkj.thai.framework.common.util.common.CommonUtils;
import com.nnnmkj.thai.framework.common.util.object.BeanUtils;
import com.nnnmkj.thai.module.course.controller.admin.lesson.vo.LessonRespVO;
import com.nnnmkj.thai.module.course.controller.app.lesson.vo.AppLessonRespVO;
import com.nnnmkj.thai.module.course.dal.dataobject.lesson.LessonDO;
import com.nnnmkj.thai.module.course.dal.dataobject.lesson.LessonDTO;
import com.nnnmkj.thai.module.member.api.user.dto.MemberUserRespDTO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;
import java.util.Map;

import static com.nnnmkj.thai.framework.common.util.collection.CollectionUtils.convertMap;

@Mapper
public interface LessonConvert {

    LessonConvert INSTANCE = Mappers.getMapper(LessonConvert.class);

    PageResult<LessonRespVO> convertPage(PageResult<LessonDO> page);

    PageResult<AppLessonRespVO> convertPage1(PageResult<LessonDO> page);

    PageResult<AppLessonRespVO> convertPage2(PageResult<LessonDTO> page);

    default PageResult<LessonRespVO> convertPage(PageResult<LessonDO> pageResult, List<MemberUserRespDTO> userList) {
        PageResult<LessonRespVO> result = convertPage(pageResult);

        Map<Long, Long> systemUserIdToUserIdMap = convertMap(userList, MemberUserRespDTO::getSystemUserId, MemberUserRespDTO::getId);
        Map<Long, String> userNicknameMap = convertMap(userList, MemberUserRespDTO::getSystemUserId, MemberUserRespDTO::getNickname);
        Map<Long, String> lessonCreatorMap = convertMap(pageResult.getList(), LessonDO::getId, LessonDO::getCreator);

        result.getList().forEach(lessonVO -> {
            String creator = lessonCreatorMap.get(lessonVO.getId());
            Long userId = CommonUtils.getUserIdByCreatorQuery(creator, UserTypeEnum.ADMIN);

            lessonVO.setNickname(userNicknameMap.get(userId));
            lessonVO.setUserId(systemUserIdToUserIdMap.get(userId));
        });
        return result;
    }

    default PageResult<AppLessonRespVO> convertPage2(PageResult<LessonDO> pageResult, List<MemberUserRespDTO> userList) {
        PageResult<AppLessonRespVO> result = convertPage1(pageResult);

        Map<Long, Long> systemUserIdToUserIdMap = convertMap(userList, MemberUserRespDTO::getSystemUserId, MemberUserRespDTO::getId);
        Map<Long, String> userNicknameMap = convertMap(userList, MemberUserRespDTO::getSystemUserId, MemberUserRespDTO::getNickname);
        Map<Long, String> lessonCreatorMap = convertMap(pageResult.getList(), LessonDO::getId, LessonDO::getCreator);

        result.getList().forEach(lessonVO -> {
            String creator = lessonCreatorMap.get(lessonVO.getId());
            Long userId = CommonUtils.getUserIdByCreatorQuery(creator, UserTypeEnum.ADMIN);

            lessonVO.setNickname(userNicknameMap.get(userId));
            lessonVO.setUserId(systemUserIdToUserIdMap.get(userId));
        });
        return result;
    }

    default List<AppLessonRespVO> convertList(List<LessonDO> lessonDOList, List<MemberUserRespDTO> memberUserList) {
        List<AppLessonRespVO> result = BeanUtils.toBean(lessonDOList, AppLessonRespVO.class);

        Map<Long, Long> systemUserIdToUserIdMap = convertMap(memberUserList, MemberUserRespDTO::getSystemUserId, MemberUserRespDTO::getId);
        Map<Long, String> userNicknameMap = convertMap(memberUserList, MemberUserRespDTO::getSystemUserId, MemberUserRespDTO::getNickname);
        Map<Long, String> lessonCreatorMap = convertMap(lessonDOList, LessonDO::getId, LessonDO::getCreator);

        result.forEach(lessonVO -> {
            String creator = lessonCreatorMap.get(lessonVO.getId());
            Long userId = CommonUtils.getUserIdByCreatorQuery(creator, UserTypeEnum.ADMIN);

            lessonVO.setUserId(systemUserIdToUserIdMap.get(userId));
            lessonVO.setNickname(userNicknameMap.get(userId));
        });
        return result;
    }

}
