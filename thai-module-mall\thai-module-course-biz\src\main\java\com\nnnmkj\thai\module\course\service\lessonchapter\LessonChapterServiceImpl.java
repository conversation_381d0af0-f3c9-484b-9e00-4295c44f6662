package com.nnnmkj.thai.module.course.service.lessonchapter;

import com.nnnmkj.thai.framework.common.pojo.PageResult;
import com.nnnmkj.thai.framework.common.util.object.BeanUtils;
import com.nnnmkj.thai.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.nnnmkj.thai.module.course.controller.admin.lessonchapter.vo.LessonChapterPageReqVO;
import com.nnnmkj.thai.module.course.controller.admin.lessonchapter.vo.LessonChapterSaveReqVO;
import com.nnnmkj.thai.module.course.dal.dataobject.lessonchapter.LessonChapterDO;
import com.nnnmkj.thai.module.course.dal.dataobject.lessonchapter.LessonChatperAttachmentDO;
import com.nnnmkj.thai.module.course.dal.dataobject.membergrouplesson.MemberGroupLessonDO;
import com.nnnmkj.thai.module.course.dal.mysql.lessonchapter.LessonChapterMapper;
import com.nnnmkj.thai.module.course.dal.mysql.lessonchapter.LessonChatperAttachmentMapper;
import com.nnnmkj.thai.module.course.dal.mysql.membergrouplesson.MemberGroupLessonMapper;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import java.util.Collection;
import java.util.Collections;
import java.util.List;

import static com.nnnmkj.thai.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.nnnmkj.thai.module.course.enums.ErrorCodeConstants.LESSON_CHAPTER_CONTAINS_CHILDREN;
import static com.nnnmkj.thai.module.course.enums.ErrorCodeConstants.LESSON_CHAPTER_NOT_EXISTS;

/**
 * 课程章节 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class LessonChapterServiceImpl implements LessonChapterService {

    @Resource
    private LessonChapterMapper lessonChapterMapper;
    @Resource
    private LessonChatperAttachmentMapper lessonChatperAttachmentMapper;
    @Resource
    private MemberGroupLessonMapper memberGroupLessonMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long createLessonChapter(LessonChapterSaveReqVO createReqVO) {
        // 插入
        LessonChapterDO lessonChapter = BeanUtils.toBean(createReqVO, LessonChapterDO.class);
        lessonChapterMapper.insert(lessonChapter);

        // 插入子表
        createLessonChatperAttachmentList(lessonChapter.getId(), createReqVO.getLessonChatperAttachments());
        // 返回
        return lessonChapter.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateLessonChapter(LessonChapterSaveReqVO updateReqVO) {
        // 校验存在
        validateLessonChapterExists(updateReqVO.getId());
        // 更新
        LessonChapterDO updateObj = BeanUtils.toBean(updateReqVO, LessonChapterDO.class);
        lessonChapterMapper.updateById(updateObj);

        // 更新子表
        updateLessonChatperAttachmentList(updateReqVO.getId(), updateReqVO.getLessonChatperAttachments());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteLessonChapter(Long id) {
        // 校验存在
        validateLessonChapterExists(id);
        
        // 校验是否有子章节
        List<LessonChapterDO> children = getLessonChapterListByParentId(id);
        if (!children.isEmpty()) {
            throw exception(LESSON_CHAPTER_CONTAINS_CHILDREN);
        }
        
        // 获取章节信息，用于关联删除
        LessonChapterDO chapter = getLessonChapter(id);
        if (chapter != null && chapter.getCourseId() != null) {
            // 删除关联的课程班级关联数据
            memberGroupLessonMapper.delete(new LambdaQueryWrapperX<MemberGroupLessonDO>()
                    .eq(MemberGroupLessonDO::getCourseId, chapter.getCourseId()));
        }
        
        // 删除章节
        lessonChapterMapper.deleteById(id);

        // 删除子表
        deleteLessonChatperAttachmentByChapterId(id);
    }

    private void validateLessonChapterExists(Long id) {
        if (lessonChapterMapper.selectById(id) == null) {
            throw exception(LESSON_CHAPTER_NOT_EXISTS);
        }
    }

    @Override
    public LessonChapterDO getLessonChapter(Long id) {
        return lessonChapterMapper.selectById(id);
    }

    @Override
    public PageResult<LessonChapterDO> getLessonChapterPage(LessonChapterPageReqVO pageReqVO) {
        return lessonChapterMapper.selectPage(pageReqVO);
    }
    
    @Override
    public PageResult<LessonChapterDO> getTopLessonChapterPage(LessonChapterPageReqVO pageReqVO) {
        // 构建查询条件并执行查询
        return lessonChapterMapper.selectPage(pageReqVO, new LambdaQueryWrapperX<LessonChapterDO>()
                .eqIfPresent(LessonChapterDO::getCourseId, pageReqVO.getCourseId())
                .inIfPresent(LessonChapterDO::getCourseId, pageReqVO.getCourseIds())
                .likeIfPresent(LessonChapterDO::getName, pageReqVO.getName())
                .betweenIfPresent(LessonChapterDO::getCreateTime, pageReqVO.getCreateTime())
                .and(wrapper -> wrapper.isNull(LessonChapterDO::getParentId).or().eq(LessonChapterDO::getParentId, 0L))
                .orderByDesc(LessonChapterDO::getId));
    }

    @Override
    public List<LessonChapterDO> getLessonChapterList(Collection<Long> ids) {
        if (ids.isEmpty()) {
            return Collections.emptyList();
        }
        return lessonChapterMapper.selectByIds(ids);
    }

    @Override
    public List<LessonChapterDO> getLessonChapterListByParentId(Long parentId) {
        return lessonChapterMapper.selectList(new LambdaQueryWrapperX<LessonChapterDO>()
                .eq(LessonChapterDO::getParentId, parentId)
                .orderByAsc(LessonChapterDO::getSort));
    }

    // ==================== 子表（课程章节附件） ====================

    @Override
    public List<LessonChatperAttachmentDO> getLessonChatperAttachmentListByChapterId(Long chapterId) {
        List<LessonChatperAttachmentDO> attachments = lessonChatperAttachmentMapper.selectListByChapterId(chapterId);
        // 如果附件列表为空，尝试使用其他方式查询
        if (attachments == null || attachments.isEmpty()) {
            attachments = lessonChatperAttachmentMapper.selectList(
                    new LambdaQueryWrapperX<LessonChatperAttachmentDO>()
                            .eq(LessonChatperAttachmentDO::getChapterId, chapterId));
            // 如果还是为空，尝试直接用SQL查询
            if (attachments == null || attachments.isEmpty()) {
                attachments = lessonChatperAttachmentMapper.selectAttachmentsByChapterId(chapterId);
            }
        }
        
        return attachments;
    }

    @Override
    public List<LessonChapterDO> getLessonChapterListByCourseId(Long courseId) {
        return lessonChapterMapper.selectList(new LambdaQueryWrapperX<LessonChapterDO>()
                .eq(LessonChapterDO::getCourseId, courseId));
    }

    private void createLessonChatperAttachmentList(Long chapterId, List<LessonChatperAttachmentDO> list) {
        list.forEach(o -> o.setChapterId(chapterId));
        lessonChatperAttachmentMapper.insertBatch(list);
    }

    private void updateLessonChatperAttachmentList(Long chapterId, List<LessonChatperAttachmentDO> list) {
        deleteLessonChatperAttachmentByChapterId(chapterId);
		list.forEach(o -> o.setId(null).setUpdater(null).setUpdateTime(null)); // 解决更新情况下：1）id 冲突；2）updateTime 不更新
        createLessonChatperAttachmentList(chapterId, list);
    }

    private void deleteLessonChatperAttachmentByChapterId(Long chapterId) {
        lessonChatperAttachmentMapper.deleteByChapterId(chapterId);
    }

}