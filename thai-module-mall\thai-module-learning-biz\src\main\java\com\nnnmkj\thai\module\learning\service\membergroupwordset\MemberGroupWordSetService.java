package com.nnnmkj.thai.module.learning.service.membergroupwordset;

import com.nnnmkj.thai.framework.common.pojo.PageResult;
import com.nnnmkj.thai.module.learning.controller.admin.membergroupwordset.vo.MemberGroupWordSetPageReqVO;
import com.nnnmkj.thai.module.learning.controller.admin.membergroupwordset.vo.MemberGroupWordSetSaveReqVO;
import com.nnnmkj.thai.module.learning.dal.dataobject.membergroupwordset.MemberGroupWordSetDO;
import jakarta.validation.Valid;

import java.util.List;

/**
 * 班级-学习集关联 Service 接口
 *
 * <AUTHOR>
 */
public interface MemberGroupWordSetService {

    /**
     * 创建班级-学习集关联
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createMemberGroupWordSet(@Valid MemberGroupWordSetSaveReqVO createReqVO);

    /**
     * 批量创建班级-学习集关联
     *
     * @param setIds 学习集编号列表
     * @param groupId 班级编号
     * @return 创建的数量
     */
    int batchCreateMemberGroupWordSet(List<Long> setIds, Long groupId);

    /**
     * 批量删除班级-学习集关联
     *
     * @param setIds 学习集编号列表
     * @param groupId 班级编号
     * @return 删除的数量
     */
    int batchDeleteMemberGroupWordSet(List<Long> setIds, Long groupId);

    /**
     * 更新班级-学习集关联
     *
     * @param updateReqVO 更新信息
     */
    void updateMemberGroupWordSet(@Valid MemberGroupWordSetSaveReqVO updateReqVO);

    /**
     * 删除班级-学习集关联
     *
     * @param id 编号
     */
    void deleteMemberGroupWordSet(Long id);

    /**
     * 获得班级-学习集关联
     *
     * @param id 编号
     * @return 班级-学习集关联
     */
    MemberGroupWordSetDO getMemberGroupWordSet(Long id);

    /**
     * 获得班级-学习集关联分页
     *
     * @param pageReqVO 分页查询
     * @return 班级-学习集关联分页
     */
    PageResult<MemberGroupWordSetDO> getMemberGroupWordSetPage(MemberGroupWordSetPageReqVO pageReqVO);

    int countByGroupId(Long groupId);

    int countVisibleByGroupId(Long groupId);

    List<MemberGroupWordSetDO> getMemberGroupWordSetList(Long wordSetId);
}