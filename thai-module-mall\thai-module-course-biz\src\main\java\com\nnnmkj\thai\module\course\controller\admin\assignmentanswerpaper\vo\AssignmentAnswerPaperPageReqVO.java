package com.nnnmkj.thai.module.course.controller.admin.assignmentanswerpaper.vo;

import com.nnnmkj.thai.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

import static com.nnnmkj.thai.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 课程作业作答试卷分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class AssignmentAnswerPaperPageReqVO extends PageParam {

    @Schema(description = "课程ID", example = "27224")
    private Long courseId;

    @Schema(description = "作业发布ID", example = "20079")
    private Long assignmentReleaseId;

    @Schema(description = "用户ID", example = "16442")
    private Long userId;

    @Schema(description = "答题时间（s）")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private Long[] answerTime;

    @Schema(description = "是否提交")
    private Boolean isSubmit;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}