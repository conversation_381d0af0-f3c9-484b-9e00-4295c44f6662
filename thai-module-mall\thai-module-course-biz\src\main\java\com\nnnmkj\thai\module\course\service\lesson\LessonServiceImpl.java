package com.nnnmkj.thai.module.course.service.lesson;

import cn.hutool.core.collection.CollUtil;
import com.nnnmkj.thai.framework.common.pojo.PageResult;
import com.nnnmkj.thai.framework.common.util.object.BeanUtils;
import com.nnnmkj.thai.module.course.controller.admin.lesson.vo.LessonPageReqVO;
import com.nnnmkj.thai.module.course.controller.admin.lesson.vo.LessonSaveReqVO;
import com.nnnmkj.thai.module.course.dal.dataobject.lesson.LessonAttachmentDO;
import com.nnnmkj.thai.module.course.dal.dataobject.lesson.LessonDO;
import com.nnnmkj.thai.module.course.dal.dataobject.lesson.LessonDTO;
import com.nnnmkj.thai.module.course.dal.dataobject.lesson.LessonHasGroupDTO;
import com.nnnmkj.thai.module.course.dal.dataobject.membergrouplesson.MemberGroupLessonDO;
import com.nnnmkj.thai.module.course.dal.mysql.lesson.LessonAttachmentMapper;
import com.nnnmkj.thai.module.course.dal.mysql.lesson.LessonMapper;
import com.nnnmkj.thai.module.course.dal.mysql.lesson.LessonMapperX;
import com.nnnmkj.thai.module.course.dal.mysql.membergrouplesson.MemberGroupLessonMapper;
import com.nnnmkj.thai.module.learning.enums.WordSetVisibilityEnum;
import com.nnnmkj.thai.module.system.api.social.SocialClientApi;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import java.util.*;

import static com.nnnmkj.thai.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.nnnmkj.thai.module.course.enums.ErrorCodeConstants.*;

/**
 * 课程 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class LessonServiceImpl implements LessonService {

    @Resource
    private LessonMapper lessonMapper;
    @Resource
    private MemberGroupLessonMapper memberGroupLessonMapper;
    @Resource
    private LessonMapperX lessonMapperX;
    @Resource
    private LessonAttachmentMapper lessonAttachmentMapper;

    @Resource
    private SocialClientApi socialClientApi;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long createLesson(LessonSaveReqVO createReqVO) {
        Integer visibility = createReqVO.getVisibility();
        // 如果是全部可见或者组内可见，则进行审核
        if (WordSetVisibilityEnum.ALL.getType().equals(visibility) ||  WordSetVisibilityEnum.GROUP.getType().equals(visibility)) {
            Boolean isOk = socialClientApi.auditWxaContentSecurity(createReqVO.getTitle());
            if (!isOk) {
                throw exception(LESSON_TITLE_UNSAFE);
            }
            isOk = socialClientApi.auditWxaContentSecurity(createReqVO.getDescription());
            if (!isOk) {
                throw exception(LESSON_DESC_UNSAFE);
            }
        }
        // 插入
        LessonDO lesson = BeanUtils.toBean(createReqVO, LessonDO.class);
        lessonMapper.insert(lesson);

        // 插入子表
        createLessonAttachmentList(lesson.getId(), createReqVO.getLessonAttachments());

        List<Long> groupIds = createReqVO.getGroupIds();

        if (groupIds != null) {
            // 批量插入课程和班级的关联关系
            List<MemberGroupLessonDO> toInsert = new ArrayList<>();
            Long courseId = lesson.getId();
            for (Long groupId : groupIds) {
                MemberGroupLessonDO relation = new MemberGroupLessonDO();
                relation.setGroupId(groupId);
                relation.setCourseId(courseId);
                toInsert.add(relation);
            }
            // 批量插入
            for (MemberGroupLessonDO relation : toInsert) {
                memberGroupLessonMapper.insert(relation);
            }
        }        // 返回
        return lesson.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateLesson(LessonSaveReqVO updateReqVO) {
        Integer visibility = updateReqVO.getVisibility();
        // 如果是全部可见或者组内可见，则进行审核
        if (WordSetVisibilityEnum.ALL.getType().equals(visibility) ||  WordSetVisibilityEnum.GROUP.getType().equals(visibility)) {
            Boolean isOk = socialClientApi.auditWxaContentSecurity(updateReqVO.getTitle());
            if (!isOk) {
                throw exception(LESSON_TITLE_UNSAFE);
            }
            isOk = socialClientApi.auditWxaContentSecurity(updateReqVO.getDescription());
            if (!isOk) {
                throw exception(LESSON_DESC_UNSAFE);
            }
        }
        // 校验存在
        validateLessonExists(updateReqVO.getId());
        // 更新
        LessonDO updateObj = BeanUtils.toBean(updateReqVO, LessonDO.class);
        lessonMapper.updateById(updateObj);

        //判断是否有传入班级id，如果没有则全部关联删除
        if (updateReqVO.getGroupIds() != null) {

            //判断更新的班级关联有没有变化，如果有变化，则删除原来的关联关系
            Set<Long> newGroupIdsSet = new HashSet<>(updateReqVO.getGroupIds());
            Map<Long, Set<Long>> oldgroupIds = getClassIdsByCourseId(updateReqVO.getId());
            Map<Long, Set<Long>> oldgroupIds2 = new HashMap<>(oldgroupIds);
            // 2. 计算需要删除的元素（存在于 classIds 但不存在于 groupIds）
            Set<Long> toRemove = new HashSet<>(oldgroupIds.get(updateReqVO.getId()));
            toRemove.removeAll(newGroupIdsSet);

            oldgroupIds.put(updateReqVO.getId(), toRemove);
            // 3. 删除非共同元素
            if (!toRemove.isEmpty()) {
                // 调用删除方法，
                memberGroupLessonMapper.delectByCourseIdAndGroupId(oldgroupIds);
            }

            newGroupIdsSet.removeAll(oldgroupIds2.get(updateReqVO.getId()));

            if (!newGroupIdsSet.isEmpty()) {
                List<MemberGroupLessonDO> toInsert = new ArrayList<>();
                Long courseId = updateReqVO.getId();
                for (Long groupId : newGroupIdsSet) {
                    MemberGroupLessonDO relation = new MemberGroupLessonDO();
                    relation.setGroupId(groupId);
                    relation.setCourseId(courseId);
                    toInsert.add(relation);
                }
                for (MemberGroupLessonDO relation : toInsert) {
                    memberGroupLessonMapper.insert(relation);
                }
            }

        } else {
            memberGroupLessonMapper.delectByCourseId(updateReqVO.getId());
        }
        // 更新子表
        updateLessonAttachmentList(updateReqVO.getId(), updateReqVO.getLessonAttachments());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteLesson(Long id) {
        // 校验存在
        validateLessonExists(id);
        // 删除
        lessonMapper.deleteById(id);
        memberGroupLessonMapper.delectByCourseId(id);
        // 删除子表
        deleteLessonAttachmentByCourseId(id);
    }

    private void validateLessonExists(Long id) {
        if (lessonMapper.selectById(id) == null) {
            throw exception(LESSON_NOT_EXISTS);
        }
    }

    @Override
    public LessonDO getLesson(Long id) { return lessonMapper.selectById(id);}

    @Override
    public List<Long> getGroupIdsByCourseId(Long courseId) {
        List<MemberGroupLessonDO> memberGroupLessonDOS = memberGroupLessonMapper.selectByCourseId(courseId);

        List<Long> groupIds = new ArrayList<>();
        for (MemberGroupLessonDO memberGroupLessonDO : memberGroupLessonDOS) {
            groupIds.add(memberGroupLessonDO.getGroupId());
        }
        return groupIds;
    }

    @Override
    public PageResult<LessonDTO> getLessonPage(LessonPageReqVO pageReqVO) {
        return lessonMapperX.selectPage(pageReqVO);
    }

    @Override
    public List<LessonDO> getLessonList(LessonPageReqVO pageReqVO) {
        return lessonMapper.selectList(pageReqVO);
    }

    @Override
    public List<LessonDO> getLessonList(Collection<Long> ids) {
        if (CollUtil.isEmpty(ids)) {
            return Collections.emptyList();
        }
        return lessonMapper.selectByIds(ids);
    }

    // ==================== 子表（课程附件） ====================

    @Override
    public List<LessonAttachmentDO> getLessonAttachmentListByCourseId(Long courseId) {
        return lessonAttachmentMapper.selectListByCourseId(courseId);
    }

    //获取课程关联表的班级和课程关联的集合
    private Map<Long, Set<Long>> getClassIdsByCourseId(Long courseId) {
        Map<Long, Set<Long>> groupIdAndClassIds = new HashMap<>();
        List<MemberGroupLessonDO> memberGroupLessonDOS = memberGroupLessonMapper.selectByCourseId(courseId);
        Set<Long> classIds = new HashSet<>();
        for (MemberGroupLessonDO memberGroupLessonDO : memberGroupLessonDOS) {
            classIds.add(memberGroupLessonDO.getGroupId());
        }
        groupIdAndClassIds.put(courseId, classIds);
        return groupIdAndClassIds;
    }

    private void createLessonAttachmentList(Long id, List<LessonAttachmentDO> list) {
        if (CollUtil.isEmpty(list)) {
            return;
        }
        list.forEach(o -> o.setCourseId(id));
        lessonAttachmentMapper.insertBatch(list);
    }

    private void updateLessonAttachmentList(Long id, List<LessonAttachmentDO> list) {
        // 删除旧数据
        lessonAttachmentMapper.deleteByCourseId(id);
        if (CollUtil.isEmpty(list)) {
            return;
        }
        // 添加新数据前重置id、updater和updateTime，避免主键冲突
        list.forEach(o -> o.setId(null).setUpdater(null).setUpdateTime(null));
        createLessonAttachmentList(id, list);
    }

    private void deleteLessonAttachmentByCourseId(Long id) {
        lessonAttachmentMapper.deleteByCourseId(id);
    }

    @Override
    public PageResult<LessonHasGroupDTO> getLessonPages(LessonPageReqVO pageReqVO) {
        PageResult<LessonHasGroupDTO> lessonPageResult = BeanUtils.toBean(lessonMapperX.selectPage(pageReqVO), LessonHasGroupDTO.class);
        if (CollUtil.isEmpty(lessonPageResult.getList())) {
            return PageResult.empty();
        }

        List<LessonHasGroupDTO> groupList = lessonPageResult.getList();

        for (LessonHasGroupDTO lessonHasGroupDTO : groupList) {
            List<Long> groupIds=new ArrayList<>();
            // 查询关联班级并添加到 DTO
            memberGroupLessonMapper.selectByCourseId(lessonHasGroupDTO.getId()).forEach(
                    item -> groupIds.add(item.getGroupId())
            );
            lessonHasGroupDTO.setGroupIds(groupIds);
        }
        // 显式设置回分页结果中
        lessonPageResult.setList(groupList);

        return lessonPageResult;
    }
}