package com.nnnmkj.thai.module.course.convert.membergrouplesson;

import com.nnnmkj.thai.framework.common.pojo.PageResult;
import com.nnnmkj.thai.module.course.controller.admin.membergrouplesson.vo.MemberGroupLessonRespVO;
import com.nnnmkj.thai.module.course.controller.app.membergrouplesson.vo.AppMemberGroupLessonRespVO;
import com.nnnmkj.thai.module.course.dal.dataobject.lesson.LessonDO;
import com.nnnmkj.thai.module.course.dal.dataobject.membergrouplesson.MemberGroupLessonDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;
import java.util.Map;

import static com.nnnmkj.thai.framework.common.util.collection.CollectionUtils.convertMap;

@Mapper
public interface MembergrouplessonConvert {

    MembergrouplessonConvert INSTANCE = Mappers.getMapper(MembergrouplessonConvert.class);

    default MemberGroupLessonRespVO convert(MemberGroupLessonDO memberGroupLesson, LessonDO lesson){
        if (memberGroupLesson == null) {
            return null;
        }
        MemberGroupLessonRespVO respVO = new MemberGroupLessonRespVO();
        // 设置基本信息
        respVO.setId(memberGroupLesson.getId());
        respVO.setCreateTime(memberGroupLesson.getCreateTime());
        // 设置分组信息
        if (lesson != null) {
            respVO.setTitle(lesson.getTitle());
        }
        return respVO;
    }

    PageResult<MemberGroupLessonRespVO> convertPage(PageResult<MemberGroupLessonDO> page);

    default PageResult<MemberGroupLessonRespVO> convertPage(PageResult<MemberGroupLessonDO> pageResult,
                                                              List<LessonDO> wordSetList) {
        PageResult<MemberGroupLessonRespVO> result = convertPage(pageResult);
        // 处理关联数据
        Map<Long, String> setMap = convertMap(wordSetList, LessonDO::getId, LessonDO::getTitle);
        // 填充关联数据
        result.getList().forEach(p -> p.setTitle(setMap.get(p.getCourseId())));
        return result;
    }

    List<MemberGroupLessonRespVO> convertList(List<MemberGroupLessonDO> list);

    default List<MemberGroupLessonRespVO> convertList(List<MemberGroupLessonDO> list,
                                                        List<LessonDO> wordSetList) {
        List<MemberGroupLessonRespVO> result = convertList(list);
        // 处理关联数据
        Map<Long, String> setMap = convertMap(wordSetList, LessonDO::getId, LessonDO::getTitle);
        // 填充关联数据
        result.forEach(p -> p.setTitle(setMap.get(p.getCourseId())));
        return result;
    }
    
    // App相关转换方法
    default AppMemberGroupLessonRespVO appConvert(MemberGroupLessonDO memberGroupLesson, LessonDO lesson){
        if (memberGroupLesson == null) {
            return null;
        }
        AppMemberGroupLessonRespVO respVO = new AppMemberGroupLessonRespVO();
        // 设置基本信息
        respVO.setId(memberGroupLesson.getId());
        respVO.setGroupId(memberGroupLesson.getGroupId());
        respVO.setCourseId(memberGroupLesson.getCourseId());
        respVO.setCreateTime(memberGroupLesson.getCreateTime());
        // 设置课程信息
        if (lesson != null) {
            respVO.setTitle(lesson.getTitle());
        }
        return respVO;
    }
    
    List<AppMemberGroupLessonRespVO> appConvertList(List<MemberGroupLessonDO> list);
    
    default List<AppMemberGroupLessonRespVO> appConvertList(List<MemberGroupLessonDO> list,
                                                       List<LessonDO> wordSetList) {
        if (list == null) {
            return null;
        }
        
        // 处理关联数据
        Map<Long, LessonDO> lessonMap = convertMap(wordSetList, LessonDO::getId);
        
        // 转换列表
        return list.stream().map(memberGroupLesson -> {
            LessonDO lesson = lessonMap.get(memberGroupLesson.getCourseId());
            return appConvert(memberGroupLesson, lesson);
        }).collect(java.util.stream.Collectors.toList());
    }
}
