package com.nnnmkj.thai.module.course.controller.admin.lessoncollection;

import com.nnnmkj.thai.framework.apilog.core.annotation.ApiAccessLog;
import com.nnnmkj.thai.framework.common.pojo.CommonResult;
import com.nnnmkj.thai.framework.common.pojo.PageParam;
import com.nnnmkj.thai.framework.common.pojo.PageResult;
import com.nnnmkj.thai.framework.common.util.object.BeanUtils;
import com.nnnmkj.thai.framework.excel.core.util.ExcelUtils;
import com.nnnmkj.thai.module.course.controller.admin.lessoncollection.vo.LessonCollectionPageReqVO;
import com.nnnmkj.thai.module.course.controller.admin.lessoncollection.vo.LessonCollectionRespVO;
import com.nnnmkj.thai.module.course.controller.admin.lessoncollection.vo.LessonCollectionSaveReqVO;
import com.nnnmkj.thai.module.course.dal.dataobject.lessoncollection.LessonCollectionDO;
import com.nnnmkj.thai.module.course.service.lessoncollection.LessonCollectionService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.util.List;

import static com.nnnmkj.thai.framework.apilog.core.enums.OperateTypeEnum.EXPORT;
import static com.nnnmkj.thai.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 课程收藏")
@RestController
@RequestMapping("/course/lesson-collection")
@Validated
public class LessonCollectionController {

    @Resource
    private LessonCollectionService lessonCollectionService;

    @PostMapping("/create")
    @Operation(summary = "创建课程收藏")
    @PreAuthorize("@ss.hasPermission('course:lesson-collection:create')")
    public CommonResult<Long> createLessonCollection(@Valid @RequestBody LessonCollectionSaveReqVO createReqVO) {
        return success(lessonCollectionService.createLessonCollection(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新课程收藏")
    @PreAuthorize("@ss.hasPermission('course:lesson-collection:update')")
    public CommonResult<Boolean> updateLessonCollection(@Valid @RequestBody LessonCollectionSaveReqVO updateReqVO) {
        lessonCollectionService.updateLessonCollection(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除课程收藏")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('course:lesson-collection:delete')")
    public CommonResult<Boolean> deleteLessonCollection(@RequestParam("id") Long id) {
        lessonCollectionService.deleteLessonCollection(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得课程收藏")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('course:lesson-collection:query')")
    public CommonResult<LessonCollectionRespVO> getLessonCollection(@RequestParam("id") Long id) {
        LessonCollectionDO lessonCollection = lessonCollectionService.getLessonCollection(id);
        return success(BeanUtils.toBean(lessonCollection, LessonCollectionRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得课程收藏分页")
    @PreAuthorize("@ss.hasPermission('course:lesson-collection:query')")
    public CommonResult<PageResult<LessonCollectionRespVO>> getLessonCollectionPage(@Valid LessonCollectionPageReqVO pageReqVO) {
        PageResult<LessonCollectionDO> pageResult = lessonCollectionService.getLessonCollectionPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, LessonCollectionRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出课程收藏 Excel")
    @PreAuthorize("@ss.hasPermission('course:lesson-collection:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportLessonCollectionExcel(@Valid LessonCollectionPageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<LessonCollectionDO> list = lessonCollectionService.getLessonCollectionPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "课程收藏.xls", "数据", LessonCollectionRespVO.class,
                        BeanUtils.toBean(list, LessonCollectionRespVO.class));
    }

}