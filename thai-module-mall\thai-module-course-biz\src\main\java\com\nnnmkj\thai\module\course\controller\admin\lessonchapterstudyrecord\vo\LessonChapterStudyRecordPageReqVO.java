package com.nnnmkj.thai.module.course.controller.admin.lessonchapterstudyrecord.vo;

import com.nnnmkj.thai.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;
import java.util.Set;

import static com.nnnmkj.thai.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 课程章节学习记录分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class LessonChapterStudyRecordPageReqVO extends PageParam {

    @Schema(description = "课程ID", example = "23385")
    private Long courseId;

    @Schema(description = "章节ID", example = "3322")
    private Long chapterId;

    @Schema(description = "用户ID", example = "5489")
    private Long userId;

    @Schema(description = "状态", example = "1")
    private Integer status;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;
    
    @Schema(description = "课程ID列表")
    private Set<Long> courseIds;

}