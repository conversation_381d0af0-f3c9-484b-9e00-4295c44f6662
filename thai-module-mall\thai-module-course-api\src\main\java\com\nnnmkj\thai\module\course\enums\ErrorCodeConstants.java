package com.nnnmkj.thai.module.course.enums;

import com.nnnmkj.thai.framework.common.exception.ErrorCode;

/**
 * Learning 错误码枚举类
 * learning 系统，使用 1-016-000-000 段
 */
public interface ErrorCodeConstants {

    // ========== 课程 1-016-001-000 ==========
    ErrorCode LESSON_NOT_EXISTS = new ErrorCode(1_016_001_000, "课程不存在");

    // ========== 班级-课程关联 1_016_002_000 ==========
    ErrorCode MEMBER_GROUP_LESSON_NOT_EXISTS = new ErrorCode(1_016_002_000, "班级-课程关联不存在");
    ErrorCode USER_NOT_IN_CLASS = new ErrorCode(1_016_002_001, "当前用户未加入任何班级，请先加入班级后再尝试");
    ErrorCode USER_NOT_IN_CURRENT_CLASS_GROUP = new ErrorCode(1_016_002_002, "用户未加入当前的班级，不能操作");

    // ========== 课程章节 1_016_003_000 ==========
    ErrorCode LESSON_CHAPTER_NOT_EXISTS = new ErrorCode(1_016_003_000, "课程章节不存在");
    ErrorCode LESSON_CHAPTER_CONTAINS_CHILDREN = new ErrorCode(1_016_003_001, "课程章节存在子章节，请先删除子章节");

    // ========== 课程进度 1_016_004_000 ==========
    ErrorCode LESSON_PROCESS_NOT_EXISTS = new ErrorCode(1_016_004_000, "课程进度不存在");

    // ========== 课程章节学习记录 1_016_005_000 ==========
    ErrorCode LESSON_CHAPTER_STUDY_RECORD_NOT_EXISTS = new ErrorCode(1_016_005_000, "课程章节学习记录不存在");

    // ========== 课程作业 1_016_006_000 ==========
    ErrorCode ASSIGNMENT_NOT_EXISTS = new ErrorCode(1_016_006_000, "课程作业不存在");

    // ========== 课程作业题目 1_016_007_000 ==========
    ErrorCode ASSIGNMENT_QUESTION_NOT_EXISTS = new ErrorCode(1_016_007_000, "课程作业题目不存在");
    ErrorCode ASSIGNMENT_QUESTION_EMPTY = new ErrorCode(1_016_007_000, "课程作业题目为空，请先添加课程作业题目");

    // ========== 课程作业发布题目 1_016_008_000 ==========
    ErrorCode ASSIGNMENT_RELEASE_QUESTION_NOT_EXISTS = new ErrorCode(1_016_008_000, "课程作业发布题目不存在");
    ErrorCode ASSIGNMENT_RANDOM_QUESTION_INSUFFICIENT = new ErrorCode(1_016_008_001, "题目数量不足，无法完成随机抽题");
    ErrorCode ASSIGNMENT_RELEASE_QUESTION_OPTION_NOT_EXISTS = new ErrorCode(1_016_008_002, "题目选项不存在");

    // ========== 课程作业发布 1_016_009_000 ==========
    ErrorCode ASSIGNMENT_RELEASE_NOT_EXISTS = new ErrorCode(1_016_009_000, "课程作业发布不存在");

    // ========== 课程收藏 1_016_010_000 ==========
    ErrorCode LESSON_COLLECTION_NOT_EXISTS = new ErrorCode(1_016_010_000, "课程收藏不存在");

    // ========== 课程作业发布班级关联 1_016_011_000 ==========
    ErrorCode MEMBER_GROUP_ASSIGNMENT_RELEASE_NOT_EXISTS = new ErrorCode(1_016_011_000, "课程作业发布班级关联不存在");

    // ========== 课程作业成绩 1_016_012_000 ==========
    ErrorCode ASSIGNMENT_SCORE_NOT_EXISTS = new ErrorCode(1_016_012_000, "课程作业成绩不存在");

    // ========== 课程作业作答试卷 1_016_013_000 ==========
    ErrorCode ASSIGNMENT_ANSWER_PAPER_NOT_EXISTS = new ErrorCode(1_016_013_000, "课程作业作答试卷不存在");
    ErrorCode ASSIGNMENT_ANSWER_PAPER_TARGET_USER_EMPTY = new ErrorCode(1_016_013_001, "发布对象用户为空");

    // ========== 课程作业作答记录 1_016_014_000 ==========
    ErrorCode ASSIGNMENT_ANSWER_RECORD_NOT_EXISTS = new ErrorCode(1_016_014_000, "课程作业作答记录不存在");
    ErrorCode ASSIGNMENT_ANSWER_ITEMS_EMPTY = new ErrorCode(1_016_014_001, "作答题目为空");

    // ========== 课程附件 1_016_015_000 ==========
    ErrorCode LESSON_ATTACHMENT_NOT_EXISTS =new ErrorCode(1_016_015_000, "课程附件不存在");
    // ========== 课程统计 1_016_016_000 ==========
    ErrorCode LESSON_STUDY_STATISTIC_NOT_EXISTS = new ErrorCode(1_016_016_000, "课程学习统计不存在");
    ErrorCode LESSON_COLLECTION_STATISTIC_NOT_EXISTS = new ErrorCode(1_016_016_001, "课程收藏统计不存在");
    ErrorCode LESSON_CLICK_STATISTIC_NOT_EXISTS = new ErrorCode(1_016_016_002, "课程点击量统计不存在");

    ErrorCode LESSON_TITLE_UNSAFE = new ErrorCode(1_016_016_003, "课程标题包含敏感词汇");
    ErrorCode LESSON_DESC_UNSAFE = new ErrorCode(1_016_016_004, "课程描述包含敏感词汇");
}
