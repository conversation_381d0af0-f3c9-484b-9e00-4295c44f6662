package com.nnnmkj.thai.module.learning.dal.mysql.membergroupwordset;

import com.nnnmkj.thai.framework.common.pojo.PageResult;
import com.nnnmkj.thai.framework.mybatis.core.mapper.BaseMapperX;
import com.nnnmkj.thai.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.nnnmkj.thai.module.learning.controller.admin.membergroupwordset.vo.MemberGroupWordSetPageReqVO;
import com.nnnmkj.thai.module.learning.dal.dataobject.membergroupwordset.MemberGroupWordSetDO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 班级-学习集关联 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface MemberGroupWordSetMapper extends BaseMapperX<MemberGroupWordSetDO> {

    default PageResult<MemberGroupWordSetDO> selectPage(MemberGroupWordSetPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<MemberGroupWordSetDO>()
                .inIfPresent(MemberGroupWordSetDO::getGroupId, reqVO.getGroupIds())
                .eqIfPresent(MemberGroupWordSetDO::getGroupId, reqVO.getGroupId())
                .eqIfPresent(MemberGroupWordSetDO::getWordSetId, reqVO.getWordSetId())
                .betweenIfPresent(MemberGroupWordSetDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(MemberGroupWordSetDO::getId));
    }

    default int selectCountByGroupId(Long groupId) {
        Long count = selectCount(new LambdaQueryWrapperX<MemberGroupWordSetDO>()
                .eq(MemberGroupWordSetDO::getGroupId, groupId));
        return count == null ? 0 : count.intValue();
    }

    default boolean updateBatchById(List<MemberGroupWordSetDO> list){
        for (MemberGroupWordSetDO memberGroupWordSetDO : list) {
            updateById(memberGroupWordSetDO);
        }
        return true;
    }

}