package com.nnnmkj.thai.module.course.dal.mysql.assignmentanswerrecord;

import com.nnnmkj.thai.framework.common.pojo.PageResult;
import com.nnnmkj.thai.framework.mybatis.core.mapper.BaseMapperX;
import com.nnnmkj.thai.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.nnnmkj.thai.module.course.controller.admin.assignmentanswerrecord.vo.AssignmentAnswerRecordPageReqVO;
import com.nnnmkj.thai.module.course.dal.dataobject.assignmentanswerrecord.AssignmentAnswerRecordDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * 课程作业作答记录 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface AssignmentAnswerRecordMapper extends BaseMapperX<AssignmentAnswerRecordDO> {

    default PageResult<AssignmentAnswerRecordDO> selectPage(AssignmentAnswerRecordPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<AssignmentAnswerRecordDO>()
                .eqIfPresent(AssignmentAnswerRecordDO::getCourseId, reqVO.getCourseId())
                .eqIfPresent(AssignmentAnswerRecordDO::getAssignmentReleaseId, reqVO.getAssignmentReleaseId())
                .eqIfPresent(AssignmentAnswerRecordDO::getAnswerPaperId, reqVO.getAnswerPaperId())
                .eqIfPresent(AssignmentAnswerRecordDO::getQuestionId, reqVO.getQuestionId())
                .eqIfPresent(AssignmentAnswerRecordDO::getUserId, reqVO.getUserId())
                .likeIfPresent(AssignmentAnswerRecordDO::getAnswer, reqVO.getAnswer())
                .eqIfPresent(AssignmentAnswerRecordDO::getIsCorrect, reqVO.getIsCorrect())
                .betweenIfPresent(AssignmentAnswerRecordDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(AssignmentAnswerRecordDO::getId));
    }

}