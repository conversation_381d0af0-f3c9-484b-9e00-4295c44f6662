package com.nnnmkj.thai.module.course.service.membergrouplesson;

import com.nnnmkj.thai.module.course.controller.app.membergrouplesson.vo.AppMemberGroupLessonRespVO;
import com.nnnmkj.thai.module.course.controller.app.membergrouplesson.vo.AppMemberGroupLessonSaveReqVO;
import com.nnnmkj.thai.module.course.controller.app.membergrouplesson.vo.AppMemberGroupLessonSearchReqVO;
import com.nnnmkj.thai.module.course.dal.dataobject.membergrouplesson.MemberGroupLessonDO;
import jakarta.validation.Valid;

import java.util.List;

/**
 * 班级-课程关联 Service 接口
 *
 * <AUTHOR>
 */
public interface AppMemberGroupLessonService {

    /**
     * 创建班级-课程关联
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createMemberGroupLesson(@Valid AppMemberGroupLessonSaveReqVO createReqVO);

    /**
     * 删除班级-课程关联
     *
     * @param id 编号
     */
    void deleteMemberGroupLesson(Long id);

    /**
     * 根据课程ID获取班级关联列表
     *
     * @param courseId 课程ID
     * @return 班级关联列表
     */
    List<MemberGroupLessonDO> getMemberGroupLessonListByCourseId(Long courseId);

    /**
     * 搜索课程关联的班级列表
     *
     * @param reqVO 搜索请求
     * @return 班级关联列表
     */
    List<MemberGroupLessonDO> searchMemberGroupLessons(AppMemberGroupLessonSearchReqVO reqVO);
    
    /**
     * 根据课程ID获取关联的班级详情列表
     *
     * @param courseId 课程ID
     * @return 班级详情列表
     */
    List<AppMemberGroupLessonRespVO> getClassesWithDetailsByCourseId(Long courseId);
    
    /**
     * 搜索课程关联班级的详细信息列表
     *
     * @param reqVO 搜索请求
     * @return 班级详细信息列表
     */
    List<AppMemberGroupLessonRespVO> searchClassesWithDetails(AppMemberGroupLessonSearchReqVO reqVO);
    
    /**
     * 批量创建班级-课程关联
     *
     * @param courseIds 课程ID列表
     * @param classId 班级ID
     * @return 创建的记录数
     */
    int batchCreateMemberGroupLesson(List<Long> courseIds, Long classId);
    
    /**
     * 批量删除班级-课程关联
     *
     * @param courseIds 课程ID列表
     * @param classId 班级ID
     * @return 删除的记录数
     */
    int batchDeleteMemberGroupLesson(List<Long> courseIds, Long classId);

    /**
     * 批量为课程添加班级（已存在的不重复添加）
     *
     * @param courseId 课程ID
     * @param groupIds 班级ID列表
     * @return 创建的记录数
     */
    int batchCreateGroupsInCourse(Long courseId, List<Long> groupIds);

    /**
     * 批量删除课程中的班级
     *
     * @param courseId 课程ID
     * @param groupIds 班级ID列表
     * @return 删除的记录数
     */
    int batchDeleteGroupsInCourse(Long courseId, List<Long> groupIds);
}