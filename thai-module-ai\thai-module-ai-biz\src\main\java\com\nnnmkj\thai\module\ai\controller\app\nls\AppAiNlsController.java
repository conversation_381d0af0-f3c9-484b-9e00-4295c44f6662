package com.nnnmkj.thai.module.ai.controller.app.nls;

import cn.hutool.core.io.IoUtil;
import com.nnnmkj.thai.module.ai.api.nls.AiNlsApi;
import com.nnnmkj.thai.module.ai.api.nls.vo.AiNlsTtsConfigVO;
import com.nnnmkj.thai.module.ai.enums.ErrorCodeConstants;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import static com.nnnmkj.thai.framework.common.exception.util.ServiceExceptionUtil.exception;


@Tag(name = "用户 App - AI 语音合成")
@RestController
@RequestMapping("/ai/nls")
@Validated
@Slf4j
public class AppAiNlsController {

    @Resource
    @Qualifier("aliyunNlsTtsApi")
    private AiNlsApi aliyunNlsApi;
    
    @Resource
    @Qualifier("openAiNlsTtsApi")
    private AiNlsApi openAiNlsApi;

    @PostMapping("tts")
    @Operation(summary = "文字转语音")
    public void tts(@RequestBody @Valid AiNlsTtsConfigVO configVO, HttpServletResponse response) {
        try {
            // 1. 获取参数并调用语音合成
            byte[] audioData = aliyunNlsApi.tts(configVO);

            // 2. 设置响应头
            response.setContentType(MediaType.APPLICATION_OCTET_STREAM_VALUE); // 二进制流
            response.setHeader("Content-Type", "audio/mpeg"); // 实际音频类型
            response.setHeader("Content-Disposition", "inline; filename=speech.mp3"); // 浏览器内嵌播放
            response.setHeader("Cache-Control", "no-cache, no-store, must-revalidate"); // 禁用缓存
            response.setHeader("Pragma", "no-cache");
            response.setHeader("Content-Length", String.valueOf(audioData.length));

            // 3. 写入响应流
            IoUtil.write(response.getOutputStream(), true, audioData);
        } catch (Exception e) {
            throw exception(ErrorCodeConstants.TTS_FAILED);
        }
    }

    @PostMapping("openai/tts")
    @Operation(summary = "OpenAI文字转语音")
    public void openaiTts(@RequestBody @Valid AiNlsTtsConfigVO configVO, HttpServletResponse response) {
        try {
            // 1. 调用OpenAI语音合成API
            byte[] audioData = openAiNlsApi.tts(configVO);

            // 2. 设置响应头
            response.setContentType(MediaType.APPLICATION_OCTET_STREAM_VALUE); // 二进制流
            response.setHeader("Content-Type", "audio/mpeg"); // 实际音频类型
            response.setHeader("Content-Disposition", "inline; filename=openai-speech.mp3"); // 浏览器内嵌播放
            response.setHeader("Cache-Control", "no-cache, no-store, must-revalidate"); // 禁用缓存
            response.setHeader("Pragma", "no-cache");
            response.setHeader("Content-Length", String.valueOf(audioData.length));

            // 3. 写入响应流
            IoUtil.write(response.getOutputStream(), true, audioData);
        } catch (Exception e) {
            throw exception(ErrorCodeConstants.TTS_FAILED);
        }
    }

}
