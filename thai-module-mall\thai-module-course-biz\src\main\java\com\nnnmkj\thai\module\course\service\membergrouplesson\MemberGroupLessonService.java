package com.nnnmkj.thai.module.course.service.membergrouplesson;

import com.nnnmkj.thai.framework.common.pojo.PageResult;
import com.nnnmkj.thai.module.course.controller.admin.membergrouplesson.vo.LessonAndAllGroupVO;
import com.nnnmkj.thai.module.course.controller.admin.membergrouplesson.vo.MemberGroupLessonPageReqVO;
import com.nnnmkj.thai.module.course.controller.admin.membergrouplesson.vo.MemberGroupLessonSaveReqVO;
import com.nnnmkj.thai.module.course.controller.app.membergrouplesson.vo.AppMemberGroupLessonRespVO;
import com.nnnmkj.thai.module.course.controller.app.membergrouplesson.vo.AppMemberGroupLessonSearchReqVO;
import com.nnnmkj.thai.module.course.dal.dataobject.membergrouplesson.MemberGroupLessonDO;
import jakarta.validation.Valid;

import java.util.List;
import java.util.Map;

/**
 * 班级-课程关联 Service 接口
 *
 * <AUTHOR>
 */
public interface MemberGroupLessonService {

    /**
     * 创建班级-课程关联
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createMemberGroupLesson(@Valid MemberGroupLessonSaveReqVO createReqVO);

    /**
     * 更新班级-课程关联
     *
     * @param updateReqVO 更新信息
     */
    void updateMemberGroupLesson(@Valid MemberGroupLessonSaveReqVO updateReqVO);

    /**
     * 删除班级-课程关联
     *
     * @param id 编号
     */
    void deleteMemberGroupLesson(Long id);

    /**
     * 获得班级-课程关联
     *
     * @param id 编号
     * @return 班级-课程关联
     */
    MemberGroupLessonDO getMemberGroupLesson(Long id);

    /**
     * 获得班级-课程关联分页
     *
     * @param pageReqVO 分页查询
     * @return 班级-课程关联分页
     */
    PageResult<MemberGroupLessonDO> getMemberGroupLessonPage(MemberGroupLessonPageReqVO pageReqVO);

    /**
     * 根据班级ID获取课程关联列表
     *
     * @param groupId 班级ID
     * @return 课程关联列表
     */
    List<MemberGroupLessonDO> getMemberGroupLessonListByGroupId(Long groupId);

    /**
     * 根据课程ID获取班级关联列表
     *
     * @param courseId 课程ID
     * @return 班级关联列表
     */
    List<MemberGroupLessonDO> getMemberGroupLessonListByCourseId(Long courseId);
    
    /**
     * 搜索课程关联的班级列表
     *
     * @param reqVO 搜索请求
     * @return 班级关联列表
     */
    List<MemberGroupLessonDO> searchMemberGroupLessons(AppMemberGroupLessonSearchReqVO reqVO);
    
    /**
     * 获取课程关联班级的详细信息列表
     *
     * @param courseId 课程ID
     * @return 班级详细信息列表
     */
    List<AppMemberGroupLessonRespVO> getClassesWithDetailsByCourseId(Long courseId);
    
    /**
     * 获取课程关联班级的详细信息列表（搜索）
     *
     * @param reqVO 搜索请求
     * @return 班级详细信息列表
     */
    List<AppMemberGroupLessonRespVO> searchClassesWithDetails(AppMemberGroupLessonSearchReqVO reqVO);
    
    /**
     * 获取班级的课程数量
     *
     * @param groupId 班级ID
     * @return 课程数量
     */
    int getLessonCountByGroupId(Long groupId);
    
    /**
     * 批量获取班级的课程数量
     *
     * @param groupIds 班级ID列表
     * @return 课程数量Map，key为班级ID，value为课程数量
     */
    Map<Long, Integer> getLessonCountMapByGroupIds(List<Long> groupIds);

    /**
     * 批量修改课程关联的班级
     *
     * @param lessonAndAllGroupVO 课程和对应的班级信息
     */
    void updateAllMemberGroupLesson(LessonAndAllGroupVO lessonAndAllGroupVO);
}