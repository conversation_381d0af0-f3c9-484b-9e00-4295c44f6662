package com.nnnmkj.thai.module.course.service.lessonstudystatistic;

import com.nnnmkj.thai.framework.common.pojo.PageResult;
import com.nnnmkj.thai.framework.common.util.object.BeanUtils;
import com.nnnmkj.thai.module.course.controller.admin.lessonstudystatistic.vo.LessonStudyStatisticPageReqVO;
import com.nnnmkj.thai.module.course.controller.admin.lessonstudystatistic.vo.LessonStudyStatisticSaveReqVO;
import com.nnnmkj.thai.module.course.dal.dataobject.lessonstudystatistic.LessonStudyStatisticDO;
import com.nnnmkj.thai.module.course.dal.mysql.lessonstudystatistic.LessonStudyStatisticMapper;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import static com.nnnmkj.thai.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.nnnmkj.thai.module.course.enums.ErrorCodeConstants.LESSON_STUDY_STATISTIC_NOT_EXISTS;

/**
 * 课程学习统计 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class LessonStudyStatisticServiceImpl implements LessonStudyStatisticService {

    @Resource
    private LessonStudyStatisticMapper lessonStudyStatisticMapper;

    @Override
    public Long createLessonStudyStatistic(LessonStudyStatisticSaveReqVO createReqVO) {
        // 插入
        LessonStudyStatisticDO lessonStudyStatistic = BeanUtils.toBean(createReqVO, LessonStudyStatisticDO.class);
        lessonStudyStatisticMapper.insert(lessonStudyStatistic);
        // 返回
        return lessonStudyStatistic.getId();
    }

    @Override
    public void updateLessonStudyStatistic(LessonStudyStatisticSaveReqVO updateReqVO) {
        // 校验存在
        validateLessonStudyStatisticExists(updateReqVO.getId());
        // 更新
        LessonStudyStatisticDO updateObj = BeanUtils.toBean(updateReqVO, LessonStudyStatisticDO.class);
        lessonStudyStatisticMapper.updateById(updateObj);
    }

    @Override
    public void deleteLessonStudyStatistic(Long id) {
        // 校验存在
        validateLessonStudyStatisticExists(id);
        // 删除
        lessonStudyStatisticMapper.deleteById(id);
    }

    private void validateLessonStudyStatisticExists(Long id) {
        if (lessonStudyStatisticMapper.selectById(id) == null) {
            throw exception(LESSON_STUDY_STATISTIC_NOT_EXISTS);
        }
    }

    @Override
    public LessonStudyStatisticDO getLessonStudyStatistic(Long id) {
        return lessonStudyStatisticMapper.selectById(id);
    }

    @Override
    public PageResult<LessonStudyStatisticDO> getLessonStudyStatisticPage(LessonStudyStatisticPageReqVO pageReqVO) {
        return lessonStudyStatisticMapper.selectPage(pageReqVO);
    }

}